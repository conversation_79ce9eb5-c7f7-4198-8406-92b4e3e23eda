<?php
// 在实际应用中，你可能需要这样使用：
// 设置自定义错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error [$errno] $errstr in $errfile on line $errline");
    throw new ErrorException($errstr, 0, $errno, $errfile, $errline);
});

// 设置异常处理函数
set_exception_handler(function($e) {
    error_log("Uncaught Exception: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    http_response_code(500);
    die(json_encode([
        'code' => 500,
        'message' => 'Internal Server Error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTrace()
    ]));
});

require './includes/common.php';
$storage = new \lib\ProductStorage();
$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;

$result = $storage->getPaginatedProducts($date, $page, 10, 'desc');
//exit(json_encode($result));
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品列表 - <?= htmlspecialchars($date) ?></title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .date-selector {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .date-selector input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .date-selector button {
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .data-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .item {
            background: #fff;
            border: 1px solid #eee;
            border-radius: 6px;
            padding: 15px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .item h3 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 16px;
        }
        .item p {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .price {
            color: #e74c3c;
            font-weight: bold;
            font-size: 18px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }
        .pagination a, .pagination span {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #3498db;
        }
        .pagination a:hover {
            background-color: #f8f9fa;
        }
        .pagination .current {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }
        .stats-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        @media (max-width: 768px) {
            .data-list {
                grid-template-columns: 1fr;
            }
            .stats-bar {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品列表 (<?= htmlspecialchars($date) ?>)</h1>
        
        <div class="date-selector">
            <input type="date" id="datePicker" value="<?= htmlspecialchars($date) ?>">
            <button onclick="changeDate()">查询</button>
        </div>
        
        <div class="stats-bar">
            <div>总商品数: <?= $result['pagination']['total'] ?></div>
            <div>总页数: <?= $result['pagination']['total_pages'] ?></div>
        </div>
        
        <div class="data-list">
            <?php foreach ($result['data'] as $item): 
                $itemData = is_string($item) ? json_decode($item, true) : $item;
                if (!is_array($itemData)) continue;
            ?>
                <div class="item">
                    <?php if (!empty($itemData['photo'])): ?>
                        <img src="<?= htmlspecialchars($itemData['photo']) ?>" alt="<?= htmlspecialchars($itemData['name'] ?? '') ?>">
                    <?php endif; ?>
                    <h3><?= htmlspecialchars($itemData['name'] ?? '未命名商品') ?></h3>
                    <p>品牌: <?= htmlspecialchars($itemData['brand'] ?? '无品牌') ?></p>
                    <p>分类: <?= htmlspecialchars($itemData['category3'] ?? $itemData['category1'] ?? '未分类') ?></p>
                    <p class="price">¥<?= number_format($itemData['avgPrice'] ?? 0, 2) ?></p>
                    <p>销量: <?= $itemData['soldCount'] ?? 0 ?></p>
                    <p>库存: <?= $itemData['stock'] ?? 0 ?></p>
                    <a href="<?= htmlspecialchars($itemData['link'] ?? '#') ?>" target="_blank">查看详情</a>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="pagination">
            <?php if ($result['pagination']['current_page'] > 1): ?>
                <a href="?date=<?= $date ?>&page=<?= $result['pagination']['current_page'] - 1 ?>">上一页</a>
            <?php endif; ?>
            
            <?php 
            // 显示页码导航
            $current = $result['pagination']['current_page'];
            $totalPages = $result['pagination']['total_pages'];
            $start = max(1, $current - 2);
            $end = min($totalPages, $current + 2);
            
            if ($start > 1) echo '<span>...</span>';
            
            for ($i = $start; $i <= $end; $i++): ?>
                <?php if ($i == $current): ?>
                    <span class="current"><?= $i ?></span>
                <?php else: ?>
                    <a href="?date=<?= $date ?>&page=<?= $i ?>"><?= $i ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            if ($end < $totalPages) echo '<span>...</span>';
            ?>
            
            <?php if ($result['pagination']['current_page'] < $result['pagination']['total_pages']): ?>
                <a href="?date=<?= $date ?>&page=<?= $result['pagination']['current_page'] + 1 ?>">下一页</a>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function changeDate() {
            const date = document.getElementById('datePicker').value;
            window.location.href = `?date=${date}&page=1`;
        }
        
        // 可以添加更多交互功能，比如：
        // - 加载更多按钮
        // - 商品搜索过滤
        // - 排序选项
    </script>
</body>
</html>
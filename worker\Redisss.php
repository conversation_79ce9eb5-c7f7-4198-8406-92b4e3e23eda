<?php
// 设置无限执行时间
ini_set('memory_limit', '1024M');
set_time_limit(0);
// 确保脚本在CLI模式下运行
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line");
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 定义日志文件
define('LOG_FILE', __DIR__ . '/../logs/redis_queue_worker.log');

// 自定义日志函数
function log_message($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    echo $logEntry;
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// 添加内存监控函数
function checkMemoryUsage($threshold = 400) {
    $current = memory_get_usage(true) / 1024 / 1024;
    if ($current > $threshold) {
        throw new Exception("内存使用超过阈值: {$current}MB");
    }
    return $current;
}
// 引入配置文件
require_once __DIR__ . '/../includes/common.php';

// 检查必要的扩展
if (!extension_loaded('redis')) {
    log_message("Redis extension is not loaded. Please install and enable it.", 'ERROR');
    exit(1);
}

if (!class_exists('PhpAmqpLib\Connection\AMQPStreamConnection')) {
    log_message("PhpAmqpLib is not installed. Please run: composer require php-amqplib/php-amqplib", 'ERROR');
    exit(1);
}

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exception\AMQPIOException;

// 信号处理函数
function signal_handler($signo) {
    switch ($signo) {
        case SIGTERM:
        case SIGINT:
            log_message("Received shutdown signal, exiting gracefully...");
            exit(0);
            break;
        case SIGHUP:
            log_message("Received reload signal");
            break;
        default:
            // 处理所有其他信号
    }
}

// 设置信号处理
if (extension_loaded('pcntl')) {
    declare(ticks=1);
    pcntl_signal(SIGTERM, "signal_handler");
    pcntl_signal(SIGINT, "signal_handler");
    pcntl_signal(SIGHUP, "signal_handler");
} else {
    log_message("PCNTL extension not available, signal handling disabled", 'WARNING');
}

// 连接Redis
try {
    $redis = new Redis();
    // 设置连接超时时间为5秒
    if (!$redis->connect('127.0.0.1', 6379, 5)) {
        throw new Exception("Failed to connect to Redis");
    }
    // 设置Redis操作超时
    $redis->setOption(Redis::OPT_READ_TIMEOUT, 5);
    
    log_message("Successfully connected to Redis");
} catch (Exception $e) {
    log_message("Redis connection error: " . $e->getMessage(), 'ERROR');
    exit(1);
}

// 预先建立RabbitMQ连接（避免每次循环都重新连接）
$rabbitConnection = null;
$rabbitChannel = null;

try {
    // 确保$Raconfig已定义
    if (!isset($Raconfig)) {
        // 如果没有定义，设置默认值
        $Raconfig = [
            'host' => 'localhost',
            'port' => 5672,
            'user' => 'guest',
            'pwd' => 'guest',
            'vhost' => '/'
        ];
        log_message("Using default RabbitMQ configuration", 'WARNING');
    }
    
    $rabbitConnection = new AMQPStreamConnection(
        $Raconfig['host'], 
        $Raconfig['port'], 
        $Raconfig['user'], 
        $Raconfig['pwd'],
        $Raconfig['vhost'] ?? '/',
        false, 'AMQPLAIN', null, 'en_US', 10 // 10秒连接超时
    );
    
    $rabbitChannel = $rabbitConnection->channel();
    // 声明队列（确保队列存在）
    $rabbitChannel->queue_declare('product_order_updata', false, true, false, false);
    
    log_message("Successfully connected to RabbitMQ");
} catch (AMQPIOException $e) {
    log_message("RabbitMQ connection failed: " . $e->getMessage(), 'ERROR');
    $rabbitConnection = null;
    $rabbitChannel = null;
} catch (Exception $e) {
    log_message("RabbitMQ error: " . $e->getMessage(), 'ERROR');
    $rabbitConnection = null;
    $rabbitChannel = null;
}

// 主循环
$processedCount = 0;
$maxProcessPerCycle = 2000; // 每周期最多处理2000条
$lastRestartTime = time();
$maxRunTime = 300; // 最多运行5分钟

log_message("Delayed queue worker started successfully");

while (true) {
    try {
        // 检查运行时间，超过5分钟自动重启
        if (time() - $lastRestartTime > $maxRunTime) {
            log_message("达到最大运行时间，安全重启");
            safeRestart();
        }
        
        // 使用分批处理
        $batch = processBatch($redis, $rabbitChannel, $now, 100);
        
        if ($batch['processed'] > 0) {
            $emptyCycles = 0;
            $processedCount += $batch['processed'];
            log_message("处理了 " . $batch['processed'] . " 条消息，本轮总计: " . $processedCount);
            
            // 如果达到处理上限，重启
            if ($processedCount >= $maxProcessPerCycle) {
                log_message("达到处理上限，安全重启");
                safeRestart();
            }
        } else {
            $emptyCycles++;
            $sleepTime = min($emptyCycles * 100000, 5000000); // 最多休眠5秒
            usleep($sleepTime);
        }
        
        // 内存管理
        manageMemory();
        
    } catch (Exception $e) {
        log_message("处理错误: " . $e->getMessage(), 'ERROR');
        sleep(5);
    }
}

// 分批处理函数
function processBatch($redis, $channel, $timestamp, $batchSize = 100) {
    $processed = 0;
    $items = $redis->zRangeByScore('delayed_queue', 0, $timestamp, ['limit' => [0, $batchSize]]);
    
    foreach ($items as $item) {
        $taskData = json_decode($item, true);
        
        if ($taskData && isset($taskData['type']) && isset($taskData['data'])) {
            processTask($taskData, $channel);
            $redis->zRem('delayed_queue', $item);
            $processed++;
        } else {
            // 无效数据直接删除
            $redis->zRem('delayed_queue', $item);
        }
        
        // 每处理20条检查内存
        if ($processed % 20 === 0) {
            manageMemory();
        }
    }
    
    return ['processed' => $processed, 'items' => count($items)];
}

function manageMemory() {
    static $lastCheck = 0;
    
    // 每10秒检查一次内存
    if (time() - $lastCheck < 10) {
        return;
    }
    
    $lastCheck = time();
    $memory = memory_get_usage(true) / 1024 / 1024;
    
    if ($memory > 400) {
        log_message("内存使用: " . round($memory, 2) . "MB, 进行垃圾回收");
        gc_collect_cycles();
        
        if (function_exists('gc_mem_caches')) {
            gc_mem_caches();
        }
        
        // 如果内存仍然很高
        if (memory_get_usage(true) / 1024 / 1024 > 450) {
            log_message("内存超过450MB，准备重启");
            safeRestart();
        }
    }
}

function safeRestart() {
    global $rabbitChannel, $rabbitConnection, $redis;
    
    log_message("执行安全重启");
    
    // 关闭所有连接
    if ($rabbitChannel) {
        $rabbitChannel->close();
    }
    if ($rabbitConnection) {
        $rabbitConnection->close();
    }
    if ($redis) {
        $redis->close();
    }
    
    // 退出进程
    exit(0);
}
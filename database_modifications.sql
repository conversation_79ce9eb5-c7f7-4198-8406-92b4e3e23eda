-- 数据库结构修改脚本 - 用户余额管理和打包员扣款功能
-- 创建时间: 2025-08-27

-- 1. 在 ozon_user 表中添加余额字段
ALTER TABLE `ozon_user` 
ADD COLUMN `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户余额(元)' AFTER `phone`;

-- 为余额字段添加索引
ALTER TABLE `ozon_user` 
ADD INDEX `idx_balance` (`balance`) COMMENT '余额索引';

-- 2. 在 packers 表中添加每单扣款金额配置字段
-- 检查字段是否已存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'packers' 
     AND column_name = 'deduction_per_order' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "deduction_per_order 字段已存在" as message',
    'ALTER TABLE `packers` ADD COLUMN `deduction_per_order` decimal(10,2) NOT NULL DEFAULT \'0.00\' COMMENT \'\u6bcf单扣款金额(元)\' AFTER `user_uid`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为扣款配置字段添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'packers' 
     AND index_name = 'idx_deduction' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "idx_deduction 索引已存在" as message',
    'ALTER TABLE `packers` ADD INDEX `idx_deduction` (`deduction_per_order`) COMMENT \'\u6263款配置索引\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 在 packing_records 表中添加余额相关字段
ALTER TABLE `packing_records` 
ADD COLUMN `balance_before` decimal(10,2) DEFAULT NULL COMMENT '扣款前余额(元)' AFTER `material_cost`,
ADD COLUMN `balance_after` decimal(10,2) DEFAULT NULL COMMENT '扣款后余额(元)' AFTER `balance_before`,
ADD COLUMN `deduction_amount` decimal(10,2) DEFAULT NULL COMMENT '本次扣款金额(元)' AFTER `balance_after`;

-- 为余额字段添加索引
ALTER TABLE `packing_records` 
ADD INDEX `idx_balance_before` (`balance_before`) COMMENT '扣款前余额索引',
ADD INDEX `idx_balance_after` (`balance_after`) COMMENT '扣款后余额索引',
ADD INDEX `idx_deduction_amount` (`deduction_amount`) COMMENT '扣款金额索引';

-- 4. 创建余额变更记录表（可选，用于审计）
CREATE TABLE IF NOT EXISTS `balance_changes` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `change_type` varchar(20) NOT NULL COMMENT '变更类型(deduction=扣款, recharge=充值, refund=退款)',
  `amount` decimal(10,2) NOT NULL COMMENT '变更金额(元)',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变更前余额(元)',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变更后余额(元)',
  `related_table` varchar(50) DEFAULT NULL COMMENT '关联表名',
  `related_id` int DEFAULT NULL COMMENT '关联记录ID',
  `packerId` int DEFAULT NULL COMMENT '打包员ID（扣款时记录）',
  `notes` text COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`) COMMENT '用户ID索引',
  KEY `idx_change_type` (`change_type`) COMMENT '变更类型索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引',
  KEY `idx_packer` (`packerId`) COMMENT '打包员索引',
  KEY `idx_related` (`related_table`, `related_id`) COMMENT '关联记录索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户余额变更记录表';

-- 验证表结构修改
-- SELECT TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'erp' 
-- AND TABLE_NAME IN ('ozon_user', 'packers', 'packing_records', 'balance_changes')
-- ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- 示例：为打包员配置扣款金额（请根据实际情况修改packerId和金额）
-- UPDATE packers SET deduction_per_order = 2.00 WHERE packerId = 1;
-- UPDATE packers SET deduction_per_order = 1.50 WHERE packerId = 2;

-- 示例：为用户设置初始余额（请根据实际情况修改uid和余额）
-- UPDATE ozon_user SET balance = 100.00 WHERE uid IN (10, 10000, 10079, 10159);

-- 查询打包员和关联用户的配置（用于验证）
-- SELECT p.packerId, p.username, p.user_uid, p.deduction_per_order 
-- FROM packers p 
-- WHERE p.user_uid IS NOT NULL AND p.user_uid != '';

-- 查询用户余额（用于验证）
-- SELECT uid, username, balance 
-- FROM ozon_user 
-- WHERE uid IN (10, 10000, 10079, 10159) 
-- ORDER BY balance DESC;
<?php
// product_sync_worker.php - 同步获取用户下所有店铺的商品数据到数据库

require_once __DIR__ . '/includes/common.php'; // 引入现有数据库连接等配置

// 函数: 使用cURL调用Ozon API
function callOzonApi($url, $method, $clientId, $apiKey, $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api-seller.ozon.ru' . $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Client-Id: ' . $clientId,
        'Api-Key: ' . $apiKey,
        'Content-Type: application/json'
    ]);
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo 'cURL错误: ' . curl_error($ch);
    }
    curl_close($ch);
    return json_decode($response, true);
}

// 主函数: 同步商品数据
function syncProducts($db) {
    // 获取所有启用的店铺
    $stmt = $db->query('SELECT id as storeid, uid, ClientId, ApiKey FROM ozon_client WHERE status = 1');
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($stores as $store) {
        $clientId = $store['ClientId'];
        $apiKey = $store['ApiKey'];
        $uid = $store['uid'];
        $storeid = $store['storeid'];
        $last_id = '';

        echo "开始同步店铺: {$storeid} (UID: {$uid})\n";

        do {
            // Ozon API v3: 获取商品列表
            $list_data = [
                'filter' => new stdClass(),
                'limit' => 1000,
                'last_id' => $last_id,
            ];

            $list_response = callOzonApi('/v3/product/list', 'POST', $clientId, $apiKey, $list_data);
            
            if (!$list_response || !isset($list_response['result']['items']) || empty($list_response['result']['items'])) {
                echo "店铺 {$storeid}: 未找到商品或API返回错误。\n";
                break;
            }

            $product_ids = array_map(function($item) {
                return $item['product_id'];
            }, $list_response['result']['items']);

            // Ozon API v3: 批量获取商品详情
            $info_data = ['product_id' => $product_ids];
            $info_response = callOzonApi('/v3/product/info/list', 'POST', $clientId, $apiKey, $info_data);

            if ($info_response && isset($info_response['result']['items'])) {
                $products = $info_response['result']['items'];
                
                $db->beginTransaction();
                try {
                    $sql = "INSERT INTO ozon_products (
                                uid, storeid, product_id, offer_id, sku, name, description, 
                                created_at, updated_at, price, old_price, marketing_price, min_price,
                                currency_code, vat, volume_weight, primary_image, images, stocks, 
                                attributes, errors, status_name, status_description,
                                depth, width, height, weight
                            ) VALUES (
                                :uid, :storeid, :product_id, :offer_id, :sku, :name, :description,
                                :created_at, :updated_at, :price, :old_price, :marketing_price, :min_price,
                                :currency_code, :vat, :volume_weight, :primary_image, :images, :stocks,
                                :attributes, :errors, :status_name, :status_description,
                                :depth, :width, :height, :weight
                            ) ON DUPLICATE KEY UPDATE 
                                name = VALUES(name), description = VALUES(description), updated_at = VALUES(updated_at),
                                price = VALUES(price), old_price = VALUES(old_price), marketing_price = VALUES(marketing_price),
                                min_price = VALUES(min_price), currency_code = VALUES(currency_code), vat = VALUES(vat),
                                volume_weight = VALUES(volume_weight), primary_image = VALUES(primary_image),
                                images = VALUES(images), stocks = VALUES(stocks), attributes = VALUES(attributes),
                                errors = VALUES(errors), status_name = VALUES(status_name), 
                                status_description = VALUES(status_description), depth = VALUES(depth),
                                width = VALUES(width), height = VALUES(height), weight = VALUES(weight)";

                    $stmt = $db->prepare($sql);

                    foreach ($products as $product) {
                        $status = $product['status'] ?? [];
                        $stmt->execute([
                            ':uid' => $uid,
                            ':storeid' => $storeid,
                            ':product_id' => $product['id'],
                            ':offer_id' => $product['offer_id'],
                            ':sku' => $product['sku'],
                            ':name' => $product['name'],
                            ':description' => $product['description'],
                            ':created_at' => date('Y-m-d H:i:s', strtotime($product['created_at'])),
                            ':updated_at' => date('Y-m-d H:i:s', strtotime($product['updated_at'])),
                            ':price' => (float)($product['price'] ?? 0),
                            ':old_price' => (float)($product['old_price'] ?? 0),
                            ':marketing_price' => (float)($product['marketing_price'] ?? 0),
                            ':min_price' => (float)($product['min_price'] ?? 0),
                            ':currency_code' => $product['currency_code'],
                            ':vat' => (float)($product['vat'] ?? 0),
                            ':volume_weight' => (float)($product['volume_weight'] ?? 0),
                            ':primary_image' => $product['primary_image'],
                            ':images' => json_encode($product['images'] ?? []),
                            ':stocks' => json_encode($product['stocks'] ?? []),
                            ':attributes' => json_encode($product['attributes'] ?? []),
                            ':errors' => json_encode($product['errors'] ?? []),
                            ':status_name' => $status['state_name'] ?? '',
                            ':status_description' => $status['state_description'] ?? '',
                            ':depth' => (int)($product['depth'] ?? 0),
                            ':width' => (int)($product['width'] ?? 0),
                            ':height' => (int)($product['height'] ?? 0),
                            ':weight' => (int)($product['weight'] ?? 0)
                        ]);
                    }
                    $db->commit();
                    echo "店铺 {$storeid}: 成功同步 " . count($products) . " 个商品。\n";
                } catch (Exception $e) {
                    $db->rollBack();
                    echo "店铺 {$storeid}: 数据库操作失败: " . $e->getMessage() . "\n";
                }
            }

            $last_id = $list_response['result']['last_id'] ?? '';
            if (!empty($last_id)) {
                echo "店铺 {$storeid}: 准备获取下一页数据 (Last ID: {$last_id})...\n";
                sleep(1); // 遵守API速率限制
            }

        } while (!empty($last_id));
        echo "店铺 {$storeid} 同步完成。\n\n";
    }
}

// 执行同步
echo "开始执行商品同步任务...\n";
syncProducts($db);
echo "所有店铺同步任务完成。\n";
<?php
include("../includes/common.php");
$posting_number = daddslashes($_GET['posting_number']);
$data = $DB->getRow("SELECT A.*,B.purchase_url FROM ozon_order A LEFT JOIN ozon_management B ON A.sku=B.sku WHERE A.uid='{$uid}' AND A.posting_number='{$posting_number}'");
$product_image_url = $data['primary_image'] ?? 'https://picsum.photos/seed/product/300/300';
if($data['quantity']){
    $product_specs['产品规格'] = '数量：'.$data['quantity'];
}
if($data['color']){
    $product_specs['颜色'] = $data['color'];
}
if($data['dimensions']){
    $product_specs['尺寸'] = $data['dimensions'];
}
if($data['material']){
    $product_specs['材质'] = $data['material'];
}
$product_specs = $product_specs ?? ['产品规格' => '数量：1', '颜色' => '金色', '尺寸' => '40cm×30cm'];
?>
<link rel="stylesheet" href="/assets/css/order_dfd_purchase_modal.css">
<script src="/assets/js/jquery-3.6.0.min.js"></script>
<style>
    :root {
        --primary-color: #1890ff;
        --primary-light: #e6f7ff;
        --primary-dark: #0050b3;
        --success-color: #52c41a;
        --warning-color: #faad14;
        --error-color: #f5222d;
        --text-color: #333;
        --text-secondary: #666;
        --text-tertiary: #999;
        --background-light: #f5f7fa;
        --background-white: #ffffff;
        --border-color: #e8e8e8;
        --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        --box-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.12);
        --spacing-xs: 4px;
        --spacing-sm: 8px;
        --spacing-md: 16px;
        --spacing-lg: 24px;
        --spacing-xl: 32px;
        --border-radius: 8px;
        --transition: all 0.3s ease;
    }
</style>
<div class="modal-header">
    <h2 class="modal-title">代发采购管理</h2>
    <div class="support-info">
        <i class="iconfont icon-question"></i>
        支持平台：1688，拼多多 <i class="iconfont icon-info"></i>
    </div>
</div>

<div class="product-info">
    <div class="product-details">
        <div class="product-image-container">
            <img src="<?php echo htmlspecialchars($product_image_url); ?>" alt="产品主图" class="product-image">
        </div>
        <div class="product-id">
            <div class="id">
                <i class="fa fa-barcode"></i>
                SKU:<?=$data['sku']?>
            </div>
            <div class="product-specs">
                <?php foreach ($product_specs as $key => $value): ?>
                    <div class="spec">
                        <span class="spec-label"><?php echo htmlspecialchars($key); ?>：</span>
                        <span><?php echo htmlspecialchars($value); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <div class="form-section">
        <h3 class="section-title">自动同步采购金额</h3>
        <div class="sync-options">
            <label class="sync-option" for="sync_yes">
                <input type="radio" name="sync_purchase_amount" id="sync_yes" value="yes">
                <span><i class="fa fa-check-circle"></i> 是</span>
            </label>
            <label class="sync-option" for="sync_no">
                <input type="radio" name="sync_purchase_amount" id="sync_no" value="no" checked>
                <span><i class="fa fa-times-circle"></i> 否</span>
            </label>
            <label class="sync-option" for="sync_auto">
                <input type="radio" name="sync_purchase_amount" id="sync_auto" value="auto">
                <span><i class="fa fa-refresh"></i> 自动</span>
            </label>
        </div>
    </div>
    
    <div class="form-section">
        <h3 class="section-title">资源链接</h3>
        <div id="resource-links-container">
            <?php if(empty($data['purchase_url'])){?>
            <div class="resource-link-group" data-index="0">
                <span class="remove-link-btn" onclick="removeLink(this)">
                    <i class="fa fa-times-circle"></i>
                </span>
                <div class="form-col">
                    <label class="form-label">输入备注</label>
                    <input type="text" class="form-input" placeholder="请输入备注">
                </div>
                <div class="form-col">
                    <label class="form-label">请输入下单链接</label>
                    <input type="text" class="form-input" placeholder="请输入下单链接">
                    <div class="error-message">下单链接不能为空</div>
                </div>
                <label class="form-label"></label>
                <button type="button" class="save-btn" onclick="urlopen(this)">
                    <i class="fa fa-check"></i> 采购
                </button>
            </div>
            <?php }else{
                $json = json_decode($data['purchase_url'],true);
                $i = 0;
                foreach ($json as $item){
                    echo '<div class="resource-link-group" data-index="'.$i.'">
                <span class="remove-link-btn" onclick="removeLink(this)">
                    <i class="fa fa-times-circle"></i>
                </span>
                <div class="form-col">
                    <label class="form-label">输入备注</label>
                    <input type="text" class="form-input" placeholder="请输入备注" value="'.$item['note'].'">
                </div>
                <div class="form-col">
                    <label class="form-label">请输入下单链接</label>
                    <input type="text" class="form-input" placeholder="请输入下单链接" value="'.$item['url'].'">
                    <div class="error-message">下单链接不能为空</div>
                </div>
                <label class="form-label"></label>
                <button type="button" class="save-btn" onclick="urlopen(this)">
                    <i class="fa fa-check"></i> 采购
                </button>
            </div>';
                   $i++;
                }
            } ?>
        </div>
        <div class="button-container">
            <button type="button" class="add-link-btn" onclick="addLink()">
                <i class="fa fa-plus"></i> 添加资源链接
            </button>
            
            <div class="action-buttons">
                <button type="button" class="cancel-btn" onclick="cancelForm()">
                    <i class="fa fa-times"></i> 取消
                </button>
                <button type="button" class="save-btn" onclick="saveLinks()">
                    <i class="fa fa-check"></i> 保存
                </button>
            </div>
        </div>
    </div>
    <!--
    <div class="form-section sku-section">
        <div class="form-row">
            <label class="form-label">Ozon规格</label>
            <span class="ml-4">默认SKU</span>
        </div>
        
        <div class="form-row">
            <label class="form-label">货源规格</label>
            <div class="sku-actions">
                <button type="button" class="sku-button">
                    <i class="fa fa-refresh"></i> 获取货源规格
                </button>
                <button type="button" class="sku-button">
                    <i class="fa fa-pencil"></i> 手动录入货源SKU
                </button>
            </div>
            <select class="form-input">
                <option value="">请选择</option>
            </select>
        </div>
    </div>
    -->
    <div class="product-recommendations">
        <h3 class="section-title">1688同款商品推荐</h3>
        
        <!-- 推荐商品容器 -->
        <div id="recommendation-container">
            <!-- 商品将在这里动态加载 -->
        </div>
    </div>
</div>

<script>
// 全局变量
let linkIndex = 0;
let page = 1;

window.urlopen = function(button) {
    // Get the parent resource link group
    const $group = $(button).closest('.resource-link-group');
    
    // Get the URL input - more specific selector
    const $urlInput = $group.find('.form-col:eq(1) .form-input');
    const url = $urlInput.val();
    
    // Check if url exists and is a string before calling trim
    if (typeof url !== 'string' || !url.trim()) {
        showError($urlInput[0], '下单链接不能为空');
        return;
    }
    
    // Validate URL format
    if (!isValidUrl(url)) {
        showError($urlInput[0], '请输入有效的URL链接');
        return;
    }
    $.ajax({
        url: './ajax_order.php?act=get_urljh',
        method: 'POST',
        data: {
            url: url,
            posting_number: '<?=$posting_number?>',
            sku: '<?=$data["sku"]?>'
        },
        success: function(response) {
            setTimeout(() => {
                if (response.code === 0) {
                    window.open(response.url, '_blank');
                } else {
                    
                }
            }, 500);
        },
        error: function(xhr, status, error) {
            showToast('网络错误，请重试', 'error');
        },
        complete: function() {
            // 移除加载动画
            $('.toast').first().remove();
        }
    });
}

// Helper function to validate URL
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}
// 表单验证
function validateLink(input) {
    const $input = $(input);
    if (!$input.val().trim()) {
        showError(input, '下单链接不能为空');
    } else {
        hideError(input);
    }
}

function showError(input, message) {
    const $input = $(input);
    const $errorElement = $input.next('.error-message');
    
    $errorElement.text(message).show();
    $input.addClass('error');
}

function hideError(input) {
    const $input = $(input);
    const $errorElement = $input.next('.error-message');
    
    $errorElement.hide();
    $input.removeClass('error');
}

// 添加资源链接
window.addLink = function() {
    linkIndex++;
    const $container = $('#resource-links-container');
    
    const newLinkGroup = $(`
        <div class="resource-link-group" data-index="${linkIndex}">
            <span class="remove-link-btn">
                <i class="fa fa-times-circle"></i>
            </span>
            <div class="form-col">
                <label class="form-label">输入备注</label>
                <input type="text" class="form-input" placeholder="请输入备注">
            </div>
            <div class="form-col">
                <label class="form-label">请输入下单链接</label>
                <input type="text" class="form-input" placeholder="请输入下单链接">
                <div class="error-message">下单链接不能为空</div>
            </div>
            <label class="form-label"></label>
            <button type="button" class="save-btn" onclick="urlopen(this)">
                <i class="fa fa-check"></i> 采购
            </button>
        </div>
    `);
    
    $container.append(newLinkGroup);
    
    // 添加淡入动画
    newLinkGroup.css('opacity', '0');
    setTimeout(() => {
        newLinkGroup.css({
            'opacity': '1',
            'transition': 'opacity 0.3s ease'
        });
    }, 10);
    
    // 为新添加的输入框添加验证
    newLinkGroup.find('.form-input[type="text"]:nth-child(2)').on('blur', function() {
        validateLink(this);
    });
    
    // 添加移除按钮事件
    newLinkGroup.find('.remove-link-btn').on('click', function() {
        removeLink(this);
    });
}

// 移除资源链接
window.removeLink = function(button) {
    const $linkGroup = $(button).closest('.resource-link-group');
    
    // 添加淡出动画
    $linkGroup.css({
        'opacity': '0',
        'transform': 'translateY(-10px)',
        'transition': 'opacity 0.3s ease, transform 0.3s ease'
    });
    
    setTimeout(() => {
        $linkGroup.remove();
    }, 300);
}

// 加载商品
function loadProducts(products) {
    const $container = $('#recommendation-container');
    $container.empty();
    
    if (!products || products.length === 0) {
        $container.html(`
            <div class="no-products" style="text-align: center; padding: 40px 0;">
                <i class="fa fa-inbox" style="font-size: 48px; color: var(--text-tertiary);"></i>
                <p style="margin-top: 16px; color: var(--text-secondary);">暂无推荐商品</p>
            </div>
        `);
        return;
    }
    
    // 创建商品网格
    const $grid = $('<div class="recommendation-grid"></div>');
    $container.append($grid);
    
    // 添加商品项
    $.each(products, function(index, product) {
        const $item = $(`
            <div class="recommendation-item" data-sku="${product.offerId}">
                <a href="https://detail.1688.com/offer/${product.offerId}.html?spm=a1688g.extenstion&kj_agent_plugin=aibuy&kjSource=pc&lang=zh" target="_blank">
                    <div class="recommendation-image">
                        <img src="${product.imageUrl}" alt="${product.title}">
                    </div>
                    <div class="recommendation-title">${product.title}</div>
                    ${product.offerId ? `<div class="recommendation-sku">${product.offerId}</div>` : ''}
                    <div class="recommendation-price">${product.targetLowPrice}~${product.targetHighPrice}</div>
                    <div class="recommendation-sales">店铺评分 ${product.goodsScore}  已售${product.days90SoldOut}</div>
                </a>
            </div>
        `);
        
        $item.on('click', function() {
            selectProduct(this, product);
        });
        
        // 添加淡入动画，错开每个商品的动画时间
        $item.css({
            'opacity': '0',
            'transform': 'translateY(10px)'
        });
        
        $grid.append($item);
        
        setTimeout(() => {
            $item.css({
                'opacity': '1',
                'transform': 'translateY(0)',
                'transition': 'opacity 0.5s ease, transform 0.5s ease'
            });
        }, 100 + index * 50);
    });
}

// 显示加载动画
function showLoading(container) {
    const $container = $(container);
    $container.html(`
        <div class="recommendation-grid">
            ${Array(8).fill().map(() => `
                <div class="recommendation-item">
                    <div class="recommendation-image loading-shimmer"></div>
                    <div class="recommendation-title loading-shimmer" style="height: 36px; margin-bottom: 8px;"></div>
                    <div class="recommendation-sku loading-shimmer" style="height: 16px; margin-bottom: 8px;"></div>
                    <div class="recommendation-price loading-shimmer" style="height: 20px; margin-bottom: 8px;"></div>
                    <div class="recommendation-sales loading-shimmer" style="height: 14px;"></div>
                </div>
            `).join('')}
        </div>
    `);
}

// 选择商品
function selectProduct(element, product) {
    // 移除其他商品的选中状态
    $('.recommendation-item').removeClass('active');
    
    // 添加选中状态
    $(element).addClass('active');
    
    // 这里可以添加选择商品后的逻辑，比如填充到表单中
    console.log('选中商品:', product);
}

// 保存链接
// 修改前端 saveLinks 函数
window.saveLinks = function() {
    const links = [];
    let isValid = true;
    
    $('.resource-link-group').each(function() {
        const $group = $(this);
        const $noteInput = $group.find('.form-input:first');
        const $urlInput = $group.find('.form-input').eq(1);
        const note = $noteInput.val().trim();
        const url = $urlInput.val().trim();
        
        if (!url) {
            showError($urlInput[0], '下单链接不能为空');
            isValid = false;
        } else {
            hideError($urlInput[0]);
            links.push({
                note: note,
                url: url
            });
        }
    });
    
    if (!isValid) {
        showToast('请检查并修复表单中的错误', 'error');
        return;
    }

    // 显示加载动画
    const loadingToast = showToast('保存中...', 'loading', 0);
    
    // 发送AJAX请求
    $.ajax({
        url: './ajax_order.php?act=save_links',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            links: links,
            posting_number: '<?=$posting_number?>',
            sku: '<?=$data["sku"]?>'
        }),
        success: function(response) {
            setTimeout(() => {
                if (response.status === 'success') {
                    showToast('保存成功', 'success');
                    // 可选：关闭模态框或刷新页面
                    // setTimeout(() => location.reload(), 1500);
                } else {
                    showToast(response.message || '保存失败', 'error');
                }
            }, 500);
        },
        error: function(xhr, status, error) {
            showToast('网络错误，请重试', 'error');
        },
        complete: function() {
            // 移除加载动画
            $('.toast').first().remove();
        }
    });
};

// 添加全局Toast通知函数
function showToast(message, type = 'info', duration = 3000) {
    // 移除已有Toast
    $('.toast').remove();
    
    // 创建Toast元素
    const toastClass = `toast toast-${type}`;
    const $toast = $(`
        <div class="${toastClass}">
            <div class="toast-content">${message}</div>
        </div>
    `);
    
    $('body').append($toast);
    
    // 添加动画
    setTimeout(() => {
        $toast.addClass('show');
    }, 100);
    
    // 自动隐藏
    if (duration > 0) {
        setTimeout(() => {
            $toast.removeClass('show');
            setTimeout(() => $toast.remove(), 300);
        }, duration);
    }
    
    return $toast;
}


// 取消表单
window.cancelForm = function() {
    if (confirm('确定要取消吗？未保存的更改将丢失。')) {
        // 这里可以添加取消表单的逻辑
        console.log('取消表单');
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    // 为已有的输入框添加验证
    $('.resource-link-group .form-input[type="text"]:nth-child(2)').on('blur', function() {
        validateLink(this);
    });
    
    // 添加移除按钮事件
    $('.remove-link-btn').on('click', function() {
        removeLink(this);
    });
    
    // 默认加载1688商品
    //loadProducts('1688');
    // 模拟加载延迟
    
});
const $container = $('#recommendation-container');
    
// 显示加载动画
const $loadingIndicator = $(`
    <div class="loading-more">
        <div class="loading-shimmer" style="width: 100%; height: 50px; margin: 20px 0; border-radius: 8px;"></div>
    </div>
`);
$container.append($loadingIndicator);
$.ajax({
    url: './ajax_order.php?act=1688',
    method: 'POST',
    data: { posting_number: '<?=$posting_number?>' },
    success: function(response) {
        loadProducts(response.result.data);
    },
    error: function(xhr, status, error) {
        showToast('网络错误，请重试', 'error');
    },
    complete: function() {
        // 移除加载动画
        $('.toast').first().remove();
    }
});
</script>
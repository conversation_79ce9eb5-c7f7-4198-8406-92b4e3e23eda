<?php
/*
// 设置自定义错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error [$errno] $errstr in $errfile on line $errline");
    throw new ErrorException($errstr, 0, $errno, $errfile, $errline);
});

// 设置异常处理函数
set_exception_handler(function($e) {
    error_log("Uncaught Exception: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    http_response_code(500);
    die(json_encode([
        'code' => 500,
        'message' => 'Internal Server Error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTrace()
    ]));
});
*/
if (function_exists("set_time_limit")) {
    @set_time_limit(0);
}
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');
require './includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
$s = isset($_GET['s'])?$_GET['s']:exit('404 Not Found'.$clientip.'');
unset($_GET['s']);
$json_data = file_get_contents('php://input');
// 解析 JSON
$data = json_decode(trim($json_data,':'), true);
if($s!='notice' and $s!='app' and $s!='getappcron' and $s!='setappcron' and $s!='1688auth' and $s!='collect-cookies'){
    if($islogin2==1){
     /* if($userrow['uid']==10){
         
        } */  checkUserStatusAndExpiry();
    }else{
        //http_response_code(404);
        exit('{"code":-3,"msg":"No Login'.$clientip.'"}');
    } 
}

$redis = new Redis();
try {
    $redis->connect('127.0.0.1', 6379);
    $redis->setOption(Redis::OPT_READ_TIMEOUT, 5); // 设置读取超时
    register_shutdown_function(function() use ($redis) {
        if ($redis->isConnected()) {
            $redis->close();
        }
    });
    switch ($s) {
    case 'ozonsku':
        $sku = $data['sku'];
        $sellerId = isset($data['sellerId'])?$data['sellerId']:null;
        $redisdata = $redis->get($data['sku'].'_ozonsku');
        if($redisdata){
            $redisdata2 = $redis->get($sku.'_ozonlhaw');
            $redisdata3 = $redis->get($sku.'_ozonskudata');
            if($redisdata3){
                $redisdata3 = json_decode($redisdata3,true);
                if($redisdata and $redisdata!=false and $redisdata2 and $redisdata3['countryCode']=="CN"){
                    $json = json_decode($redisdata,true);
                    if(!$DB->find('store', '*', ['ClientId' => $json['items'][0]['sellerId']])){
                        $importer = new \lib\JsonImporter($DB);
                        $ser = $importer->importFullOzonCnProducts($redisdata,$redisdata3,json_decode($redisdata2,true));
                    }
                }
            }
            echo $redisdata;
        }else{
            if($sellerId){
                $sellerdata = $redis->get($sellerId.'_sellersku');
                if($sellerdata){
                    $skuList = json_decode($sellerdata,true);
                    if (!in_array($sku, $skuList)) {
                        http_response_code(204);exit;
                    }
                }
            }
            $array = [
                'limit'=>'50',
                'offset'=>'0',
                'filter'=>['stock'=>'any_stock','sku'=>$data['sku']],
                'sort'=>['key'=>'sum_missed_gmv_desc']
            ];
            $url = "https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3";
            $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers";
            $res = doRequests($url,$array, $referer);
            $json = json_decode($res['body'],true);
            if(empty($json['items'][0])){
                http_response_code(204);exit;
            }else{
                $result = json_encode($json);
                $redis->set($data['sku'].'_ozonsku',$result,86400);
                echo json_encode($json);
            }
        }
    break;
    case 'ozonlhaw':
        $v = isset($data['v'])?$data['v']:null;
        $sku = intval($data['sku']);
        if(empty($sku))exit(json_encode(['code'=>-1,'msg'=>'Sku No！']));
        $redisdata = $redis->get($sku.'_ozonlhaw');
        if($redisdata){
            exit($redisdata);
        }else{
            $res = get_curl('http://127.0.0.1:3000/api/sku-query',json_encode(['sku'=>strval($sku),'limit'=>10]),0,0,0,0,0,['Content-Type: application/json']);
            $json = json_decode($res, true);
            if(count($json['items'])){
                $categories = $json['items'][0]['categories'];
                $keywords = $subject = null;
                foreach ($json['items'][0]['attributes'] as $item) {
                    switch ($item['key']) {
                        case 9456: // 高 (毫米)
                            $height = (int) $item['value'];
                        break;
                        case 9454: // 长 (毫米)
                            $depth = (int) $item['value'];
                        break;
                        case 9455: // 宽 (毫米)
                            $width = (int) $item['value'];
                        break;
                        case 4497: // 商品重量 (克)
                            $weight = (float) $item['value'];
                        break;
                        case 22336: //关键词 
                            $keywords = $item['value'];
                        break;    #主题标签
                        case 23171:
                            $subject = $item['value'];
                        break;
                    }
                }
                $json['result'] = ['weight'=>$weight,'height'=>$height,'depth'=>$depth,'width'=>$width];
        
                $brokerage = commissionData($categories,$v);
                if(empty($brokerage)){
                    $brokerage = ['rFBS_1500' => '无', 'FBP_1500' => '无', 'rFBS_1500plus' => '无', 'FBP_1500plus' => '无'];
                }
                $result = ['result'=>$json['result'],'brokerage'=>$brokerage?$brokerage:$categories,'categories'=>$categories,'keywords'=>$keywords,'subject'=>$subject,'fx'=>$fx];
                if($v=='2.0'){
                    unset($result['brokerage']);
                }
                $result = json_encode($result);
                $redis->set($sku.'_ozonlhaw',$result,86400);
                echo $result;
            }else{
                http_response_code(204);
            }
        }
    break;
    case 'ozoncategorie':
        $sku = intval($data['sku']);
        $result = getRedis($sku.'_ozoncategorie');
        if($result){
            $result['fx'] = $fx;
            exit(json_encode($result));
        }
        $referer = 'https://seller.ozon.ru/app/products/add/general-info';
        $result = doRequests('https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku', ['skus' => [strval($sku)]], $referer);
        $json = json_decode($result['body'], true);
        $data = $json['resolved_categories_by_sku'][$sku];
        if(empty($data)){
            http_response_code(204);exit;
        }
        $data['sku'] = $sku;
        $data['code'] = 0;
        if(isset($data['description_type_id'])){
            $jsonContent = file_get_contents(ROOT.'/assets/ozon/精准类目佣金8.5.json');
            $jsondata = json_decode($jsonContent, true);
            if(isset($jsondata[$data['description_type_id']])){
                $data['brokerage'] = $jsondata[$data['description_type_id']];
            }
        }
        if(isset($data['description_category_id_level_2'])){
            $jsonContent = file_get_contents(ROOT.'/assets/ozon/cncategories/'.$data['description_category_id_level_2'].'.json');
            if($jsonContent){
                $jsondata = json_decode($jsonContent, true);
                $data['description_category_name_level_2'] = $jsondata['category_name'];
            }
        }
        if($data['description_category_id_level_2']){
            $data['fx'] = $fx;
            setRedis($sku.'_ozoncategorie',json_encode($data),86400);
        }
        exit(json_encode($data));
    break;
    case '0ar':
        $req = get_curl("http://*************:3003/api/0ar");
        $json = json_decode($req,true);
        exit(json_encode(['code'=>0,'data'=>['anti_content'=>$json['anti_content']]]));
    break;
    case 'notice':
        $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
        $channel = $connection->channel();
        
        // 声明队列
        $channel->queue_declare('notice', false, true, false, false);
        
        // 创建消息
        $msg = new AMQPMessage(
            json_encode($data),
            ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT] // 使消息持久化
        );
        // 发送消息
        $channel->basic_publish($msg, '', 'notice');
        
        // 关闭连接
        $channel->close();
        $connection->close();
        
        if($data['message_type']!='TYPE_UPDATE_MESSAGE')file_put_contents('notice.log', "\r\n\r\n".date('Y-m-d H:i:s')."\r\n".json_encode($data)."\r\n",FILE_APPEND);
        if($data['message_type']=='TYPE_PING'){
            exit(json_encode(['result'=>true,'version'=>'1.0.0','name'=>'aa','time'=>parseDateString()]));
        }else{
            exit(json_encode(['result'=>true]));
        }
    break;
    case 'import':
        $type = 'Sellwith';
        $status = '准备中';
        $jjl = 0;
        
        // 统一配置结构，与plimport保持一致
        $config = [
            'pricing' => [
                'mode' => $data['config']['pricing']['mode'] ?? $data['pricing']['mode'] ?? 'none',
                'value' => $data['config']['pricing']['rate'] ?? $data['config']['pricing']['value'] ?? $data['pricing']['value'] ?? 0
            ],
            'priceRange' => $data['config']['randomPrice'] ?? $data['config']['priceRange'] ?? [],
            'offerIdSettings' => $data['config']['offerIdSettings'] ?? [],
            'stock' => $data['config']['inventory']['threshold'] ?? $data['stock'] ?? 0
        ];
        
        foreach ($data['products'] as $product) {
            foreach ($data['stores'] as $store) {
                $sku = intval($product['sku']);
                $storeId = intval($store['storeId']);
                if($DB->getRow("SELECT * FROM ozon_cron WHERE sku=:sku AND storeid=:storeid limit 1", [':sku'=>$sku,':storeid'=>$storeId])){
                    $jjl=1;
                }else{
                    $time = time()+rand(1, 10);
                    $price = floatval($product['price']);
                    $warehouse = intval($store['warehouse']);
                    
                    // 处理定价配置
                     $data['config']['pricing']['mode'] = $data['config']['pricing']['mode']?$data['config']['pricing']['mode']:$data['pricing']['mode'];
                     $data['config']['pricing']['rate'] = $data['config']['pricing']['rate']?$data['config']['pricing']['rate']:$data['pricing']['value'];
                     $finalPrice = $price;
                     if($data['config']['pricing']['rate']>0){
                         switch ($data['config']['pricing']['mode']) {
                             case '加模式':
                                 $finalPrice+=$data['config']['pricing']['rate'];
                             break;
                             case '减模式':
                                 $finalPrice-=$data['config']['pricing']['rate'];
                             break;
                             case '乘模式':
                                 $finalPrice=$finalPrice*$data['config']['pricing']['rate'];
                             break;
                             case '除模式':
                                 $finalPrice=$finalPrice/$data['config']['pricing']['rate'];
                             break;
                         }
                     }
                    
                    // 应用随机价格区间（与insertProduct函数逻辑一致）
                    $priceRange = $config['priceRange'];
                    if (!empty($priceRange) && (isset($priceRange['min']) || isset($priceRange['max']))) {
                        $randomAdjustment = calculateRandomPriceAdjustment($priceRange, $finalPrice, $sku, $storeId);
                        $finalPrice += $randomAdjustment;
                        $finalPrice = max($finalPrice, 0.01);
                    }
                    // 保留两位小数
                    $finalPrice = round($finalPrice, 2);
                    $stock = intval($config['stock']);
                    
                    // 处理货号配置（与insertProduct函数逻辑一致）
                    $client = new \lib\OzonApiClient();
                    $offerIdSettings = $config['offerIdSettings'];
                    $prefix = $offerIdSettings['prefix'] ?? '';
                    $method = $offerIdSettings['method'] ?? 'random';

                    if ($method === 'sku_based') {
                        $randomPart = $client->generate_offer_id(3, false);
                        $offer_id = $prefix . $sku . $randomPart;
                    } else { // 'random' or default
                        $offer_id = $prefix . $client->generate_offer_id(10 - strlen($prefix), false);
                    }
                    
                    $offer_id2 = $client->generate_offer_id(15);
                    if(isset($data['config']['watermark']['mode'])){
                        if($data['config']['watermark']['mode']=='text'){
                            $watermark = 1; #  文本水印
                        }else if($data['config']['watermark']['mode']=='logo'){
                            $watermark = 2; #  Logo水印
                        }else{
                            $watermark = 0;
                        }
                    }else{
                        $watermark = 0;
                    }
                    
                    // 构建完整SQL语句
                    $clients = $DB->getRow("SELECT * FROM `ozon_client` WHERE `limit` >= 50 ORDER BY `limit` DESC");
                    $clientids = getclient();
                    $date = date("Y-m-d");
                    $sql = "INSERT INTO `ozon_cron` (`uid`, `sku`, `price`, `offer_id`, `offer_id2`, `storeid`, `clientids`, `type`, `warehouse_id`, `watermark`,`status`, `stock`, `time`, `addtime`, `date`)
                        VALUES 
                        ('{$uid}', '{$sku}', '{$finalPrice}', '{$offer_id}', '{$offer_id2}',
                        '{$storeId}','{$clientids}', '{$type}', '{$warehouse}',
                        '{$watermark}',
                        '{$status}', '{$stock}','{$time}', NOW(), '{$date}')";
                    $sds=$DB->exec($sql);
                }
            }
        }
        if($jjl==1)exit(json_encode(['code'=>-1,'msg'=>'可能部分店铺导入成功，原因有店铺已经上架过了,请排查']));
        exit(json_encode(['code'=>0,'msg'=>$DB->error()]));
    break;
    case 'plimport':
        // 简单直接处理，参考原来的import逻辑
        $type = 'Sellwith';
        $status = '准备中';
        $successCount = 0;
        $skipCount = 0;
        $errorList = [];
        
        // 处理均分逻辑
        if (!empty($data['distributeSkus']) && $data['distributeSkus'] === true) {
            $products = $data['products'];
            $stores = $data['stores'];
            $totalProducts = count($products);
            $totalStores = count($stores);
            
            if ($totalProducts < $totalStores) {
                exit(json_encode(['code' => -1, 'msg' => "SKU数量({$totalProducts})少于店铺数量({$totalStores})，无法均分"]));
            }
            
            // 均分SKU到各店铺
            $skusPerStore = intval($totalProducts / $totalStores);
            $remainder = $totalProducts % $totalStores;
            $productIndex = 0;
            
            foreach ($stores as $storeIndex => $store) {
                $numProductsForThisStore = $skusPerStore + ($remainder > 0 ? 1 : 0);
                $productsForStore = array_slice($products, $productIndex, $numProductsForThisStore);
                
                // 为这个店铺处理商品
                foreach ($productsForStore as $product) {
                    $sku = intval($product['sku']);
                    $storeId = intval($store['storeId']);
                    
                    if($DB->getRow("SELECT * FROM ozon_cron WHERE sku=:sku AND storeid=:storeid limit 1", [':sku'=>$sku,':storeid'=>$storeId])){
                        $skipCount++;
                        continue;
                    }
                    
                    // 处理水印配置
                    $watermark = isset($data['watermark']) ? intval($data['watermark']) : 0;
                    
                    // 构建配置数组，适配前端数据结构
                    $adaptedData = [
                        'pricing' => $data['config']['pricing'] ?? ['mode' => 'none'],
                        'priceRange' => $data['config']['randomPrice'] ?? $data['config']['priceRange'] ?? [],
                        'offerIdSettings' => $data['config']['offerIdSettings'] ?? [],
                        'stock' => $data['config']['inventory']['threshold'] ?? $data['stock'] ?? 0
                    ];
                    
                    if(insertProduct($product, $store, $adaptedData, $DB, $uid, $watermark)){
                        $successCount++;
                    } else {
                        $errorList[] = "SKU {$sku} 导入失败";
                    }
                }
                
                $productIndex += $numProductsForThisStore;
                if ($remainder > 0) $remainder--;
            }
        } else {
            // 不均分，所有商品分配给所有店铺
            foreach ($data['products'] as $product) {
                foreach ($data['stores'] as $store) {
                    $sku = intval($product['sku']);
                    $storeId = intval($store['storeId']);
                    
                    if($DB->getRow("SELECT * FROM ozon_cron WHERE sku=:sku AND storeid=:storeid limit 1", [':sku'=>$sku,':storeid'=>$storeId])){
                        $skipCount++;
                        continue;
                    }
                    
                    // 处理水印配置
                    $watermark = isset($data['watermark']) ? intval($data['watermark']) : 0;
                    
                    // 构建配置数组，适配前端数据结构
                    $adaptedData = [
                        'pricing' => $data['config']['pricing'] ?? ['mode' => 'none'],
                        'priceRange' => $data['config']['randomPrice'] ?? $data['config']['priceRange'] ?? [],
                        'offerIdSettings' => $data['config']['offerIdSettings'] ?? [],
                        'stock' => $data['config']['inventory']['threshold'] ?? $data['stock'] ?? 0
                    ];
                    
                    if(insertProduct($product, $store, $adaptedData, $DB, $uid, $watermark)){
                        $successCount++;
                    } else {
                        $errorList[] = "SKU {$sku} 导入失败";
                    }
                }
            }
        }
        
        // 返回结果
        $errorCount = count($errorList);
        if ($errorCount > 0) {
            exit(json_encode(['code' => -1, 'msg' => "导入完成！成功: {$successCount}, 跳过重复: {$skipCount}, 失败: {$errorCount}", 'errors' => $errorList]));
        } else {
            exit(json_encode(['code' => 0, 'msg' => "导入成功！成功: {$successCount}, 跳过重复: {$skipCount}"]));
        }
    break;
    case 'get':
        $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid} AND apistatus=1");
    	$list2 = [];
    	$i = 0;
    	foreach($list as $row){
    	    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    	    //$client->productinfolimit($row);
    	    if(!$row['warehouses'] or $row['warehouses']=='null'){
    	        $row['warehouses']=$client->Ozonwarehouses();
    	        $DB->update('store', ['warehouses'=>json_encode($row['warehouses'])], ['id'=>$row['id']]);
    	    }else{
    	        $row['warehouses']=json_decode($row['warehouses'],true);
    	    }
    	    unset($row['apistatus']);
    	    unset($row['key']);
    	    unset($row['cookie']);
    	    unset($row['addtime']);
    	    unset($row['status']);
    	    unset($row['uid']);
    		$list2[] = $row;
    		$i++;
    	}
    	exit(json_encode(['code'=>0,'stores'=>$list2,'fx'=>$fx['ru']]));
    break;
    case 'getShopGroups':
        // 获取用户的店铺分组信息
        $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid=:uid", [':uid' => $uid]);
        
        if ($user && $user['shop_groups']) {
            $shopGroups = json_decode($user['shop_groups'], true);
            exit(json_encode(['code' => 0, 'groups' => $shopGroups['groups'], 'defaultGroupId' => $shopGroups['defaultGroupId']]));
        } else {
            // 如果没有分组数据，返回空数组
            exit(json_encode(['code' => 0, 'groups' => [], 'defaultGroupId' => null]));
        }
    break;
    case 'seller':
        $data = json_decode($json_data, true);
        $sellerid = intval($data['sellerId']);
        if($sellerid==0 or empty($sellerid)){
            exit(json_encode(['code'=>-1,'msg'=>'数据不能为0，或者不能为空。']));
        }
        if($DB->getRow("SELECT * FROM ozon_store WHERE uid=:uid AND seller_id=:seller_id limit 1", [':uid'=>$uid,':seller_id'=>$sellerid])){
            exit(json_encode(['code'=>-2,'msg'=>'已经存在']));
        }
        $productData = [
            'uid'=>$uid,
            'seller_id'=>$sellerid,
            'addtime'=>date("Y-m-d H:i:s"),
            'date'=>date("Y-m-d")
        ];
        if($DB->insert('sellerjk', $productData)!== false){
            $result = ['code'=>0];
        }else{
            $result = ['code'=>-1,'msg'=>$DB->error()];
        }
        exit(json_encode($result));
    break;
    case 'production':
        $title = daddslashes(trim($data['title']));
        $type = daddslashes(trim($data['type']));
        $images = daddslashes(trim($data['images']));
        $mainImage = daddslashes(trim($data['mainImage']));
        $orderUrl = daddslashes(trim($data['orderUrl']));
        $videos = daddslashes(trim($data['videos']));
        $detail_url = daddslashes(trim($data['detail_url']));
        $jsonimgData =daddslashes(trim($data['jsonimgData']));
        $productAttributes = trim($data['productAttributes']);
        $platformType = intval($data['platformType']);
        $CategoryName = trim($data['CategoryName']);
        $weight = intval(trim($data['weight']));
        if(empty($title) or empty($type) or empty($uid)){
            exit(json_encode(['code'=>0,'msg'=>'Ok']));
        }
        $productionData = [
            'uid'=>$uid,
            'type'=>$type,
            'title'=>$title,
            'images'=>$images??NULL,
            'orderUrl'=>$orderUrl??NULL,
            'detail_url'=>$detail_url??NULL,
            'videos'=>$videos??NULL,
            'platformType'=>$platformType??NULL,
            'weight'=>$weight??NULL,
            'addtime'=>$date,
        ];
        if($mainImage){
            $productionData['mainImage'] = replaceImageUrl($mainImage);
        }
        if($productAttributes){
            $productionData['productAttributes'] = json_encode($productAttributes);
        }
        if($DB->insert('production', $productionData)){
            $result = ['code'=>0];
        }else{
            $result = ['code'=>-1,'msg'=>$DB->error()];
        }
        $id=$DB->lastInsertId();
        
        echo(json_encode($result));
        flush(); // 确保输出发送到浏览器
        if($id){
            // 连接到 RabbitMQ
            $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
            $channel = $connection->channel();
            
            // 声明队列
            $channel->queue_declare('CollectProducts', false, true, false, false);
            
            // 准备数据
            $messageData = [
                'type' => 'production',
                'id' => $id,
                'datatype' => $type,
                'orderUrl' => $orderUrl
            ];
            
            if ($type) $messageData['pttype'] = $type;
            if ($jsonimgData) $messageData['jsonimgData'] = $jsonimgData;
            if ($images) $messageData['images'] = $images;
        // 创建消息
            $msg = new AMQPMessage(
                json_encode($messageData),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT] // 使消息持久化
            );
            // 发送消息
            $channel->basic_publish($msg, '', 'CollectProducts');
            
            // 关闭连接
            $channel->close();
            $connection->close();
        }
    break;
    case 'product_data':
        $hterporderid = daddslashes($data['hterporderid']);
        if($hterporderid){
            $data = $DB->getRow("SELECT A.*,B.purchase_url FROM ozon_order A LEFT JOIN ozon_management B ON A.sku=B.sku WHERE A.uid='{$uid}' AND A.purchase_id='{$hterporderid}'");
            $result = ['code'=>0,'data'=>$data];
        }else{
            $result = ['code'=>-1];
        }
        exit(json_encode($result));
    break;
    case 'import_order':
        $hterporderid = daddslashes($data['hterporderid']);
        $orderSn = daddslashes($data['orderSn']);
        $payableAmount = daddslashes($data['payableAmount']);
        $purchase_type = daddslashes($data['purchase_type']);
        $pddcookie = daddslashes($data['pddCookie']);
        $cookie1688 = daddslashes($data['Cookie1688']);
        file_put_contents('import_order.log', "\r\n\r\n".date('Y-m-d H:i:s')."\r\n".json_encode($data)."\r\n",FILE_APPEND);
        setcookie("hterporderid", "", time() - 604800);
        setcookie("type", "", time() - 604800);
        /*
        if($pddcookie and $uid){
            $DB->update('user', ['pddcookie'=>$pddcookie], ['uid' => $uid]);
        }
        */
        $orderdata['purchase_orderSn'] = $orderSn;
        $orderdata['purchase_type'] = $purchase_type;
        $orderdata['Purchase_date'] = $date;
        if($payableAmount){
            $orderdata['cost'] = $payableAmount;
        }else if($purchase_type===1688){
            if($userrow['1688auth']){
                $service = new \lib\AlibabaLogisticsService(json_decode($userrow['1688auth'],true));
                $result = $service->getbuyerView($orderSn);
                $cost = $result['result']['baseInfo']['totalAmount'];
                $statusStr = $result['result']['productItems'][0]['statusStr'];
                if(isset($cost)){
                    $orderdata['cost'] = $cost;
                }
                if($statusStr=='等待卖家发货'){
                    $orderdata['purchase_ok'] = 1;
                }
            }
        }
        if($hterporderid and $uid){
            if($DB->update('order', $orderdata, ['uid' => $uid,'purchase_id'=>$hterporderid])){
                $result = ['code'=>0];
            }else{
                $result['code']=-1;
            }
        }else{
            $result['code']=-2;
        }
        
        exit(json_encode($result));
    break;
    case 'logistics':
        $v = isset($data['v'])?$data['v']:null;
        if($v){
            $json = file_get_contents(ROOT.'assets/json/物流数据.json');
            $json = json_decode($json,true);
            exit(json_encode(['code'=>0,'data'=>$json,'fx'=>$fx]));
        }else{
            echo file_get_contents(ROOT.'assets/json/物流数据.json');exit;
        }
    break;
    case 'getappcron':
        $data = $DB->find('urlcron', '*', ['time' => 0]);//,'status'=>0
        $DB->update('urlcron', ['status'=>1], ['id' => $data['id']]);
        if($data){
            $result = ['code'=>0,'url'=>$data['url'],'id'=>$data['id']];
        }else{
            $result = ['code'=>-1];
        }
        exit(json_encode($result));
    break;
    case 'skudata':
        $sku = $data['sku'];
        $redis = new Redis();$redis->connect('127.0.0.1', 6379);
        $redisdata = $redis->get($sku.'_ozonsku');
        $redisdata2 = $redis->get($sku.'_ozonlhaw');
        if(isset($data['countryCode'])){
            if($sku and !$redis->get($sku.'_ozonskudata') and $data['countryCode']=="CN"){
                $redis->set($data['sku'].'_ozonskudata',json_encode($data),60);
            }
            if($redisdata and $redisdata!=false and $redisdata2 and $data['countryCode']=="CN"){
                $json = json_decode($redisdata,true);
                if(!$DB->find('store', '*', ['ClientId' => $json['items'][0]['sellerId']])){
                    $importer = new \lib\JsonImporter($DB);
                    $ser = $importer->importFullOzonCnProducts($redisdata,$data,json_decode($redisdata2,true));
                }
            }
        }
        
        //file_put_contents('collect-cookies.log', "\r\n\r\n".date('Y-m-d H:i:s')."\r\n".$clientip."\r\n".json_encode($data)."\r\n".json_encode($ser)."\r\n",FILE_APPEND);
        exit(json_encode(['code'=>0,'msg'=>'Yes']));
    break;
    case 'collect-cookies':
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        if ($data['cookieString']) {
            $cookie = $data['cookieString'];
            if($DB->update('dailiip', ['cookie' => $cookie, 'uptime'=>$date, 'status'=>0,'msg'=>'','time'=>''], ['ip' => $clientip])){
                $array = [
                    'limit'=>'50',
                    'offset'=>'0',
                    'filter'=>['stock'=>'any_stock','sku'=>2337995210],
                    'sort'=>['key'=>'sum_missed_gmv_desc']
                ];
                $url = "https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3";
                $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers";
                //json_encode($array)
                doRequests($url,$array, $referer, $DB->find('dailiip', '*', ['ip' => $clientip]));
            }
            //file_put_contents('collect-cookies.log', "\r\n\r\n".date('Y-m-d H:i:s')."\r\n".$clientip."\r\n".$data['cookieString'].$DB->error()."\r\n",FILE_APPEND);
            exit(json_encode(['code' => 0, 'msg' => 'cookie updated for ' . $clientip]));
        } else {
            exit(json_encode(['code' => -1, 'msg' => 'cookieString not found.']));
        }
    break;
    case 'setappcron':
        echo "id: {$_GET['id']}</br>";
        $json_data = file_get_contents('php://input');
        file_put_contents('setappcron.log', "\r\n\r\n".date('Y-m-d H:i:s')."\r\n".$json_data."\r\n".json_encode($_REQUEST)."\r\n",FILE_APPEND);
        
        $uploadDir = "uploads/";
    
        // 确保上传目录存在
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        // 检查文件是否上传成功
        if (!isset($_FILES["file"]) || $_FILES["file"]["error"] != UPLOAD_ERR_OK) {
            die("文件上传失败，错误代码: " . $_FILES["file"]["error"]);
        }
        
        // 获取文件信息
        $fileName = $_FILES["file"]["name"];
        $fileTmpPath = $_FILES["file"]["tmp_name"];
        $fileSize = $_FILES["file"]["size"];
        $fileType = $_FILES["file"]["type"];
        
        // 验证文件扩展名
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if ($fileExt != "txt") {
            die("只允许上传 .txt 文件");
        }
        
        // 文件大小限制 (例如1MB)
        $maxFileSize = 1024 * 1024; // 1MB
        if ($fileSize > $maxFileSize) {
            die("文件大小不能超过 1MB");
        }
        
        // 生成安全的文件名
        $safeFileName = preg_replace("/[^a-zA-Z0-9._-]/", "", basename($fileName));
        $safeFileName = uniqid() . "_" . $safeFileName;
        $destinationPath = $uploadDir . $safeFileName;
        
        // 移动文件到目标位置
        if (move_uploaded_file($fileTmpPath, $destinationPath)) {
            // 读取文件内容 (可选)
            $fileContent = file_get_contents($destinationPath);
            
            // 输出成功信息和文件内容
            echo "<h2>文件上传成功!</h2>";
            echo "<p>文件名: " . htmlspecialchars($fileName) . "</p>";
            echo "<p>保存为: " . htmlspecialchars($safeFileName) . "</p>";
            echo "<p>文件大小: " . round($fileSize / 1024, 2) . " KB</p>";
        } else {
            die("文件移动失败");
        }
    break;
    case '1688auth':
        $client_id = '7762286';          // 你的应用ID
        $client_secret = 'Y14Wuu65O7Z';   // 从开放平台获取
        $redirect_uri = 'https://www.100b.cn/api/1688auth';
        if (isset($_GET['code']) && isset($_GET['state'])) {
            $code = $_GET['code'];
            $token_url = 'https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/' . $client_id;
            $post_data = [
                'grant_type' => 'authorization_code',
                'need_refresh_token' => 'true',
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'redirect_uri' => $redirect_uri,
                'code' => $code
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $token_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            $token_data = json_decode($response, true);
            
            if (isset($token_data['access_token'])) {
                // 获取access_token成功
                $access_token = $token_data['access_token'];
                $refresh_token = $token_data['refresh_token'];
                
                // 这里可以存储token到数据库或session中
                $_SESSION['1688_access_token'] = $access_token;
                $_SESSION['1688_refresh_token'] = $refresh_token;
                $Data = [
                    'uid'=>$uid,
                    'aliId'=>$token_data['aliId'],
                    'access_token'=>$token_data['access_token'],
                    'resource_owner'=>$token_data['resource_owner'],
                    'refresh_token'=>$token_data['refresh_token'],
                    'json'=>$response,
                    'addtime'=>$date
                ];
                if($DB->find('1688auth', 'aliId', ['aliId' => $token_data['aliId']])){
                    if($DB->update('1688auth', $Data, ['aliId' => $token_data['aliId']])){
                        die('授权成功，您可以关闭此页面，继续采购');
                    }else{
                        die('数据保存失败，请联系客服，错误内容：'.$DB->error);
                    }
                }else{
                    if($DB->insert('1688auth', $Data) !== false){
                        die('授权成功，您可以关闭此页面，继续采购');
                    }else{
                        die('数据保存失败，请联系客服，错误内容：'.$DB->error);
                    }
                }
                
            } else {
                // 获取token失败
                die('获取access_token失败: ' . $response);
            }
        } else {
            // 确保包含所有必填参数
            $authParams = [
                'client_id'     => $client_id,
                'redirect_uri'  => $redirect_uri,
                'response_type' => 'code',
                'state'         => bin2hex(random_bytes(16)), // 强随机state
                '_aop_timestamp' => round(microtime(true) * 1000),
                'site'          => '1688', // 可选但推荐
                'sceneId'       => 'daily_homepage' // 你的场景ID
            ];
            
            // 生成签名前确保没有空值
            $authParams = array_filter($authParams);
            $authParams['_aop_signature'] = generate1688Signature($authParams, $client_secret);
            // 构建授权URL
            $authUrl = 'https://auth.1688.com/oauth/authorize?' . http_build_query($authParams);
            header("Location: $authUrl");
        }
    break;
    case 'getSeller':
        $sellerId = intval($data['sellerId']);
        $offset = strval(0);
        $storage = new \lib\ProductStorage();
        $today = date('Y-m-d');
        if($sellerId){
            $data = $redis->get($sellerId.'_sellersku');
            if(empty($data)){
                // 设置API URL和初始参数
                $url = "https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3";
                $company_ids[] = $sellerId;
                $offset = 0; // 起始偏移量
                $limit = 50; // 每次请求获取的商品数量
                $sellerdata = []; // 存储所有商品SKU的数组
                $hasMore = true; // 是否还有更多商品的标志
                $totals = null;
                $sl = 0;
                while ($hasMore) {
                    // 构建请求参数
                    $requestData = [
                        'limit' => strval($limit),
                        'offset' => strval($offset),
                        'filter' => [
                            'stock' => 'any_stock',
                            'period' => 'monthly',
                            'company_ids' => $company_ids,
                        ],
                        'sort' => [
                            'key' => 'sum_gmv_desc'
                        ]
                    ];
                    $referer = 'https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers';
                    $result = doRequests($url, $requestData, $referer);
                    if ($result['body']) {
                        $response = json_decode($result['body'], true);
                        if (isset($response['items']) && count($response['items']) > 0) {
                            foreach ($response['items'] as $item) {
                                if (!empty($item['sku'])) {
                                    if(calculateDaysBetweenDates(replace($item['nullableCreateDate']))<31){
                                        // 存储数据
                                        $storage->storeDailyProducts($today, [$item]);
                                    }
                                    $redis->set($item['sku'].'_ozonsku', json_encode(['items' => [$item]]), 86400);
                                    $sellerdata[] = $item['sku']; // 收集所有SKU
                                    $sl+=$item['soldCount'];
                                }
                            }
                            if(!$totals){
                                $totals = $response['totals'];
                            }
                            $offset += count($response['items']);
                            if (count($response['items']) < $limit) {
                                $hasMore = false;
                            }
                        } else {
                            $hasMore = false;
                        }
                    } else {
                        $hasMore = false;
                    }
                }
                if (!empty($sellerdata) and $totals) {
                    $redis->set($sellerId.'_sellersku', json_encode($sellerdata), 3600);
                    $redis->set($sellerId.'_sellersl', $sl, 3600);
                }
                $sellerdata = count($sellerdata);
            }else{
                $sl = $redis->get($sellerId.'_sellersl');
                $json = json_decode($data,true);
                $sellerdata = count($json);
                $totals = $sellerdata;
            }
            
            if($uid==10000 or $uid==10){
                $data = $DB->find('store', 'uid', ['ClientId' => $sellerId]);
                if($data){
                    $text = '自家人UID：'.$data['uid'];
                }else{
                    $text = '敌人';
                }
                $result = ['status' => 'success', 'totals'=>$totals,'total_products' => $sellerdata, 'sl'=>$sl,'text'=>$text];
            }else{
                $result = ['status' => 'no','msg'=>'参数无效'];
            }
            echo json_encode($result);
        }
    break;
    case 'order':
        $posting_number = daddslashes(trim($data['posting_number']));
        if($posting_number){
            $row = $DB->find('order', '*', ['posting_number' => $posting_number,'uid'=>$uid]);
            echo json_encode($row);
        }
    break;
    case 'orderupdate':
        $purchase_orderSn = daddslashes(trim($data['purchase_orderSn']));
        $courierNumber = daddslashes(trim($data['courierNumber']));
        $purchase_lus = daddslashes(trim(($data['purchase_lus'])));
        $row = $DB->getRow("SELECT * FROM ozon_order WHERE uid='{$uid}' AND purchase_orderSn LIKE '%{$purchase_orderSn}%' limit 1");
        if($row){
            if($row['courierNumber']){
                if($row['courierNumber']==$courierNumber){
                    $DB->update('order', ['purchase_lus'=>$purchase_lus,'purchase_ok'=>1], ['uid' => $uid,'purchase_orderSn'=>$row['purchase_orderSn']]);
                }elseif (strpos($row['courierNumber'], $courierNumber) !== false){
                    $DB->update('order', ['purchase_lus'=>$purchase_lus,'purchase_ok'=>1], ['uid' => $uid,'posting_number'=>$row['posting_number']]);
                }else{
                    $courierNumber = ','.$courierNumber;
                    $DB->update('order', ['courierNumber'=>$courierNumber,'purchase_lus'=>$purchase_lus,'purchase_ok'=>1], ['uid' => $uid,'posting_number'=>$row['posting_number']]);
                }
            }else{
                $DB->update('order', ['courierNumber'=>$courierNumber,'purchase_lus'=>$purchase_lus,'purchase_ok'=>1], ['uid' => $uid,'purchase_orderSn'=>$purchase_orderSn]);
            }
            $result = ['code'=>0];
        }else{
            $result = ['code'=>-1,'msg'=>'没有此订单'];
        }
    break;
    case '1688authdata':
        $appkey = intval($data['appKey']);
        $appSecret = daddslashes(trim($data['appSecret']));
        $apptoken = daddslashes(trim($data['apptoken']));
        $userId = intval($data['userId']);
        if($appkey && $appSecret && $apptoken && $userId && $uid){
            $array = [
                'appkey'=>$appkey,
                'appSecret'=>$appSecret,
                'access_token'=>$apptoken,
                'aliId'=>$userId
            ];
            $DB->update('user', ['1688auth'=>json_encode($array)], ['uid' => $uid]);
        }
        exit(json_encode(['code'=>0,'msg'=>'ok']));
    break;
    case 'uporderdata':
        $courierNumber = isset($data['courierNumber']) ? daddslashes($data['courierNumber']) : null;
        $purchase_orderSn = isset($data['orderId']) ? daddslashes($data['orderId']) : null;
        $purchase_kdname = isset($data['purchase_kdname']) ? daddslashes($data['purchase_kdname']) : null;
        $purchase_lus = isset($data['purchase_lus']) ? daddslashes($data['purchase_lus']) : null;
        $purchase_ok = isset($data['purchase_ok']) ? intval($data['purchase_ok']) : null;
        
        // 查询订单
        $rs = $DB->getAll("SELECT * FROM ozon_order WHERE uid='{$uid}' AND purchase_type='pdd' AND (purchase_orderSn='{$purchase_orderSn}' OR purchase_orderSn LIKE '%{$purchase_orderSn}%')");
        
        if(empty($rs)) {
            exit(json_encode(['code'=>-1, 'msg'=>'找不到该订单']));
        }
        if(checkKeywordsRegex($purchase_lus)){
            $purchase_ok = 3;
        }
        foreach ($rs as $row) {
            $updateData = [
                'purchase_kdname' => $purchase_kdname,
                'purchase_ok' => $purchase_ok,
                'purchase_lus' => $purchase_lus
            ];
            
            // 处理快递单号（只有当提供了新单号时才更新）
            if (!empty($courierNumber)) {
                $newNumbers = array_filter(explode(',', $courierNumber));
                $existingNumbers = !empty($row['courierNumber']) ? array_filter(explode(',', $row['courierNumber'])) : [];
                
                // 合并新旧单号并去重
                $mergedNumbers = array_unique(array_merge($existingNumbers, $newNumbers));
                $updateData['courierNumber'] = implode(',', $mergedNumbers);
            }
            
            // 执行更新
            $result = $DB->update('order', $updateData, [
                'uid' => $uid,
                'posting_number' => $row['posting_number']
            ]);
            
            if (!$result) {
                exit(json_encode(['code' => -1, 'msg' => '更新失败']));
            }
        }
        
        exit(json_encode(['code' => 1, 'msg' => '更新成功']));
    break;
    default:
    	exit(json_encode(['code'=>-1,'msg'=>'404']));
    }
} finally {
    $redis->close();
}
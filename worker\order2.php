<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 连接到 RabbitMQ
try {
    $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
    $channel = $connection->channel();
    $channel->queue_declare('order', false, true, false, false); // 持久化队列
} catch (Exception $e) {
    die("连接失败: " . $e->getMessage());
}

echo " [*] 等待消息. 按 CTRL+C 退出\n";

// 定义回调函数
$callback = function ($msg) {
    $data = json_decode($msg->body, true);
    echo "\n [x] 收到 ", $data['type']," ID: ", $data['id']??$data['data']['ClientId'], "  时间: ".date("Y-m-d H:i:s"),"\n";
    
    switch ($data['type']) {
        case 'orderadd':
            $time = time();
            orderadd($data['data']);   #获取新的订单，或刷新订单
            $s = time()-$time;
            echo '店铺：'.$data['data']['storename'].'-ID：'.$data['data']['id'].'获取新的订单，'.$s.'/s 处理完毕。';
        break;
        case 'productsync':
            if (!productsync($data['id'])) {  #商品同步
                // 如果获取锁失败，拒绝消息并重新入队
                $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag'], false, true);
                return;
            }
        break;
        default:
            processSlowTask($data);
        break;
    }
    $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']); // 正确确认方式
};

// 公平分发（同一时间只处理一个消息）
$channel->basic_qos(null, 1, null);

// 订阅队列（修正参数顺序）
$channel->basic_consume('order', '', false, false, false, false, $callback);

// 保持监听
while (count($channel->callbacks)) {
    $channel->wait();
}

// 关闭连接
$channel->close();
$connection->close();

function processSlowTask($data) {
    file_put_contents('log.txt', print_r($data, true) . "\n", FILE_APPEND);
}


function orderadd($row){
    global $DB,$Raconfig;
    $redis = new Redis();$redis->connect('127.0.0.1', 6379);
    if($redis->get('store_'.$row['ClientId'].'_UID'.$row['uid'])){
        $redis->close();
        return true;
    }
    
    $importer = new \lib\JsonImporter($DB,$Raconfig);
    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    $client->productinfolimit($row);
    if($row['actions']==1){
        $client->actionsproducts();
    }
    $filter = [
        'status' => 'cancelled',
        'since' => date('Y-m-d', strtotime('-15 days')),
        'to' => date('Y-m-d')
    ];
    $data = $client->getFbsPostingListV3($filter);
    if($data['code']){
        if($json['message']=='Company is blocked, please contact support'){
            $store['apistatus'] = 3; # 公司已封锁，请联系支持
        }else if($json['message']=='Api-key is deactivated, use another one or generate a new one'){
            $store['apistatus'] = 2; # Api-key 已失效
        }
        $DB->update('store', $store, ['ClientId'=>$row['ClientId']]);
        $redis->set('store_'.$row['ClientId'].'_UID'.$row['uid'],$data['message'],3600);
        //echo(json_encode($data).'</br>'.$row['ClientId'].'</br></br>');
    }
    foreach ($data['result']['postings'] as $items) {
        $result = $importer->importorder($items, false, $row);
        if($result['errors']){
            //echo(json_encode($result));
        }
        
    }
    $data = $client->fbsunfulfilledlist(['cutoff_from'=>date('Y-m-d H:i:s', strtotime('-1 days')),'cutoff_to'=>date('Y-m-d H:i:s', strtotime('+16 days'))]);
    foreach ($data['result']['postings'] as $item){
        if($item['status']=='awaiting_deliver'){
            $data = $DB->find('order', '*', ['order_id' => $item['order_id']]);
            if($data['packagelabel']==0){
                if($client->packagelabel($data)){
                    $DB->update('order', ['packagelabel'=>1], ['order_id'=>$item['order_id']]);
                }
            }
        }
        $result = $importer->importorder($item, false, $row);
        if($result['errors']){
            //echo(json_encode($result));
        }
        
    }
    $redis->close();
}
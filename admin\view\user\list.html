<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户管理</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>用户管理</span>
        </div>
        <div class="layui-card-body">
            <!-- 搜索栏 -->
            <div class="layui-form layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <input type="text" id="search-input" placeholder="搜索用户名或UID" class="layui-input">
                </div>
                <div class="layui-col-md3">
                    <button class="layui-btn" id="search-btn">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button class="layui-btn layui-btn-normal" id="refresh-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            
            <!-- 数据表格 -->
            <table class="layui-hide" id="user-table" lay-filter="userTable"></table>
        </div>
    </div>

    <!-- 编辑用户弹窗 -->
    <div id="edit-user-form" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="editForm">
            <input type="hidden" name="uid" />
            
            <div class="layui-form-item">
                <label class="layui-form-label">用户UID</label>
                <div class="layui-input-block">
                    <input type="text" name="uid_display" readonly class="layui-input layui-disabled">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="username" readonly class="layui-input layui-disabled">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">邮箱</label>
                <div class="layui-input-block">
                    <input type="email" name="email" placeholder="请输入邮箱" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">手机号</label>
                <div class="layui-input-block">
                    <input type="text" name="phone" placeholder="请输入手机号" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">用户等级</label>
                <div class="layui-input-block">
                    <select name="user_level" lay-verify="required">
                         <option value="0">普通用户</option>
                        <option value="1">个人</option>
                        <option value="2">团队</option>
                        <option value="3">企业</option>
                        <option value="4">定制</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">代理等级</label>
                <div class="layui-input-block">
                    <select name="agent_level">
                        <option value="0">普通用户</option>
                        <option value="1">一级代理</option>
                        <option value="2">二级代理</option>
                        <option value="3">总代理</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">最大店铺数</label>
                <div class="layui-input-block">
                    <input type="number" name="max_shops" placeholder="请输入最大店铺数量" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">到期时间</label>
                <div class="layui-input-block">
                    <input type="text" name="expiry_date" placeholder="请选择日期" class="layui-input" id="expiry-date">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" checked>
                    <input type="radio" name="status" value="0" title="禁用">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="saveUser">保存修改</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="toolbar">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    </script>
    
    <!-- 状态显示模板 -->
    <script type="text/html" id="status-tpl">
        {{# if(d.status == 1) { }}
            <span class="layui-badge layui-bg-green">启用</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">禁用</span>
        {{# } }}
    </script>
    
    <!-- 用户等级模板 -->
    <script type="text/html" id="level-tpl">
        {{# if(d.user_level == 1) { }}
            <span class="layui-badge">个人</span>
        {{# } else if(d.user_level == 2) { }}
            <span class="layui-badge layui-bg-blue">团队</span>
        {{# } else if(d.user_level == 3) { }}
            <span class="layui-badge layui-bg-orange">企业</span>
        {{# } else if(d.user_level == 4) { }}
            <span class="layui-badge layui-bg-red">定制</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">未知</span>
        {{# } }}
    </script>
    
    <!-- 代理等级模板 -->
    <script type="text/html" id="agent-level-tpl">
        {{# if(d.agent_level == 0) { }}
            <span class="layui-badge layui-bg-gray">普通用户</span>
        {{# } else if(d.agent_level == 1) { }}
            <span class="layui-badge layui-bg-blue">一级代理</span>
        {{# } else if(d.agent_level == 2) { }}
            <span class="layui-badge layui-bg-orange">二级代理</span>
        {{# } else if(d.agent_level == 3) { }}
            <span class="layui-badge layui-bg-red">总代理</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">未知</span>
        {{# } }}
    </script>

    <script>
        layui.use(['table', 'form', 'layer', 'laydate', 'jquery'], function() {
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;
            var $ = layui.jquery;
            
            // 初始化日期选择器
            laydate.render({
                elem: '#expiry-date',
                type: 'datetime'
            });
            
            // 渲染表格
            var tableIns = table.render({
                elem: '#user-table',
                url: 'ajax.php?act=user_list',
                cols: [[
                    {field: 'uid', title: 'UID', width: 80, sort: true},
                    {field: 'username', title: '用户名', width: 120, sort: true},
                    {field: 'email', title: '邮箱', width: 150, sort: true},
                    {field: 'phone', title: '手机号', width: 110},
                    {field: 'user_level', title: '用户等级', width: 90, templet: '#level-tpl', sort: true},
                    {field: 'agent_level', title: '代理等级', width: 90, templet: '#agent-level-tpl', sort: true},
                    {field: 'shop_count', title: '当前店铺', width: 80, sort: true},
                    {field: 'max_shops', title: '最大店铺', width: 80, sort: true},
                    {field: 'addtime', title: '注册时间', width: 130, sort: true},
                    {field: 'expiry_date', title: '到期时间', width: 130, sort: true},
                    {field: 'status', title: '状态', width: 80, templet: '#status-tpl', sort: true},
                    {title: '操作', width: 100, toolbar: '#toolbar', align: 'center', fixed: 'right'}
                ]],
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                loading: true,
                even: true,
                width: '100%',
                initSort: {
                    field: 'uid',
                    type: 'desc'
                }
            });
            
            // 搜索功能
            $('#search-btn').on('click', function() {
                var keyword = $('#search-input').val().trim();
                tableIns.reload({
                    where: {
                        search: keyword
                    },
                    page: {
                        curr: 1
                    }
                });
            });
            
            // 回车搜索
            $('#search-input').on('keydown', function(e) {
                if (e.keyCode === 13) {
                    $('#search-btn').click();
                }
            });
            
            // 刷新按钮
            $('#refresh-btn').on('click', function() {
                $('#search-input').val('');
                tableIns.reload({
                    where: {},
                    page: {
                        curr: 1
                    }
                });
            });
            
            // 监听表格工具条
            table.on('tool(userTable)', function(obj) {
                var data = obj.data;
                
                if (obj.event === 'edit') {
                    // 填充表单数据
                    form.val('editForm', {
                        uid: data.uid,
                        uid_display: data.uid,
                        username: data.username,
                        email: data.email || '',
                        phone: data.phone || '',
                        user_level: data.user_level,
                        agent_level: data.agent_level,
                        max_shops: data.max_shops,
                        expiry_date: data.expiry_date,
                        status: data.status
                    });
                    
                    // 打开编辑弹窗
                    layer.open({
                        type: 1,
                        title: '编辑用户 - ' + data.username,
                        content: $('#edit-user-form'),
                        area: ['600px', '680px'],
                        shadeClose: false
                    });
                }
            });
            
            // 表单提交
            form.on('submit(saveUser)', function(data) {
                $.ajax({
                    url: 'ajax.php?act=user_edit',
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            layer.msg('保存成功', {icon: 1});
                            layer.closeAll();
                            tableIns.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('网络异常', {icon: 2});
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>OZON商品单仓库导入系统</title>

    <style>
        :root {
            --primary-color: #1E9FFF;
            --secondary-color: #5FB878;
            --border-color: #e6e6e6;
            --card-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            background-color: #f8f8f8;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        
        .layui-container {
            padding: 15px;
            max-width: 1600px;
        }
        
        .layui-card {
            border-radius: 4px;
            box-shadow: var(--card-shadow);
            margin-bottom: 15px;
            border: none;
            transition: all 0.3s;
        }
        
        .layui-card-header {
            background-color: #f8f8f8;
            border-bottom: 1px solid var(--border-color);
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .layui-card-header h2, 
        .layui-card-header h3 {
            margin: 0;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .layui-card-body {
            padding: 10px;
        }
        
        .main-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .left-panel, .right-panel {
            flex: 1;
            min-width: 400px;
            max-width: 110%;
        }
        
        /* 商品信息区域 */
        #skuInput {
            
            min-height: 300px;
            font-family: Consolas, Monaco, monospace;
            resize: vertical;
        }
        
        /* 店铺布局 - 网格式 */
        .stores-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
            align-items: stretch;
        }
        
        .store-item {
            position: relative;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 6px 10px;
            display: flex;
            align-items: center;
            cursor: pointer;
            background: #fff;
            transition: all 0.2s;
            min-width: 100px;
            max-width: none;
            flex: 1 0 calc(25% - 8px); /* 一行显示4个店铺 */
            box-sizing: border-box;
        }
        
        @media (max-width: 1400px) {
            .store-item {
                flex: 1 0 calc(33.333% - 8px); /* 中等屏幕一行显示3个 */
            }
        }
        
        @media (max-width: 992px) {
            .store-item {
                flex: 1 0 calc(50% - 8px); /* 小屏幕一行显示2个 */
            }
        }
        
        @media (max-width: 576px) {
            .store-item {
                flex: 1 0 100%; /* 移动屏幕一行显示1个 */
            }
        }
        
        .store-item:hover {
            border-color: #d9d9d9;
            background-color: #fafafa;
        }
        
        .store-item.selected {
            border-color: var(--primary-color);
            background-color: #3366ff;
        }
        
        /* 完全隐藏勾选框和指示器 */
        .store-item .layui-form-checkbox,
        .store-checkbox-indicator {
            display: none !important;
        }
        
        .store-item input[type="checkbox"] {
            /* 使真实的checkbox不可见但可操作 */
            opacity: 0;
            position: absolute;
            pointer-events: none;
        }
        
        .store-content {
            display: flex;
            align-items: center;
            width: 100%;
            overflow: hidden;
        }
        
        .store-name {
            font-size: 13px;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 4px;
        }
        
        .group-tag {
            margin-left: 4px;
            padding: 1px 5px;
            background-color: #e6f7ff;
            border-radius: 10px;
            font-size: 11px;
            color: #1890ff;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        /* 未分组店铺特殊样式 */
        .store-item.new-store {
            border-color: #ff9c6e;
            background-color: #fff7e6;
        }
        
        .store-item.new-store:hover {
            border-color: #ff7f50;
            background-color: #ffe7ba;
        }
        
        .group-tag.new-tag {
            background-color: #fff1e6;
            color: #ff7f50;
            border: 1px solid #ff9c6e;
            animation: pulse-ungrouped 2s infinite;
        }
        
        .group-tag.new-tag i {
            margin-left: 2px;
            font-size: 10px;
        }
        
        @keyframes pulse-ungrouped {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }
        
        .selected-mark {
            display: none;
            margin-left: 4px;
            color: #52c41a;
            font-size: 12px;
            flex-shrink: 0;
        }
        
        /* 仓库选择下拉 */
        .warehouse-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 200px;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            margin-top: 4px;
        }
        
        .warehouse-dropdown-title {
            padding: 8px 12px;
            font-size: 13px;
            color: #666;
            background: #f5f5f5;
            border-bottom: 1px solid #e6e6e6;
        }
        
        .warehouse-list {
            max-height: 200px;
            overflow-y: auto;
            padding: 6px 0;
        }
        
        .warehouse-item {
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            display: block;
            width: 100%;
            text-align: left;
            border: none;
            background: none;
        }
        
        .warehouse-item:hover {
            background-color: #f5f5f5;
        }
        
        .warehouse-item.selected {
            background-color: #e6f7ff;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .warehouse-item.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        
        .warehouse-item.disabled:hover {
            background: none;
        }
        
        /* 选中后显示的标记 */
        .store-item.has-warehouse .selected-mark {
            display: inline-block;
        }
        
        /* 价格策略区域 */
        .price-strategy {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .strategy-item {
            flex: 1;
            min-width: 200px;
        }
        
        /* 货号设置区域 */
        .offer-id-settings {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .offer-id-settings .strategy-item {
            flex: 1;
            min-width: 180px;
        }
        
        /* 按钮组 */
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .button-group .layui-btn {
            flex: 1;
            height: 42px;
            font-size: 14px;
            min-width: 120px;
        }
        
        /* 刷新店铺按钮特殊样式 */
        #refreshStoresBtn {
            background-color: #5FB878;
            border-color: #5FB878;
        }
        
        #refreshStoresBtn:hover {
            background-color: #009688;
            border-color: #009688;
        }
        
        /* 分组筛选器 */
        .group-filter-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .group-filter {
            flex: 1;
            max-width: 250px;
        }
        
        /* 统计信息 */
        .stat-box {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            border-left: 3px solid var(--secondary-color);
        }
        
        /* 响应式调整 */
        @media (max-width: 1400px) {
            .store-card {
                width: calc(50% - 15px); /* 中等屏幕一行两个 */
            }
        }
        
        @media (max-width: 992px) {
            .store-card {
                width: 100%; /* 小屏幕一行一个 */
            }
            
            .left-panel, .right-panel {
                min-width: 100%;
            }
        }
        
        @media (max-width: 768px) {
            .price-strategy {
                flex-direction: column;
            }
            
            .strategy-item {
                min-width: 100%;
            }
        }
        
        /* 优化仓库选择区域 */
        .warehouse-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .warehouse-select-content {
            flex-grow: 1;
            overflow: auto;
        }
        
        /* 优化卡片高度 */
        .store-card .layui-card-body {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 0;
        }
    </style>
</head>
<body>
<div class="layui-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2><i class="layui-icon layui-icon-cart"></i> 单仓库商品导入系统</h2>
        </div>
        
        <div class="layui-card-body">
            <div class="layui-form">
                <div class="main-container">
                    <!-- 左侧面板 - 商品信息 -->
                    <div class="left-panel">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <h3><i class="layui-icon layui-icon-form"></i> 1. 商品信息</h3>
                            </div>
                            <div class="layui-card-body">
                                <textarea id="skuInput" class="layui-textarea" 
                                          placeholder="每行格式：SKU 价格&#10;示例：123456 99.99"
                                          style="min-height: 300px;"></textarea>
                                <div class="stat-box">
                                    <span>有效商品：<span id="lineCount" class="layui-badge">0</span> 条</span>
                                    <small style="display: block; margin-top: 5px; color: #666;">
                                        <i class="layui-icon layui-icon-tips"></i> 
                                        自动去重相同SKU，超过10个商品将分批导入
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                                                <!-- 价格策略和按钮组 -->
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <h3><i class="layui-icon layui-icon-set"></i> 3. 价格策略</h3>
                            </div>
                            <div class="layui-card-body">
                                <div class="price-strategy">
                                    <div class="strategy-item">
                                        <label class="layui-form-label">价格调整</label>
                                        <div class="layui-input-block">
                                            <select id="priceMode" class="layui-select">
                                                <option value="none">不调整</option>
                                                <option value="加模式">加 (+)</option>
                                                <option value="减模式">减 (-)</option>
                                                <option value="乘模式">乘 (×)</option>
                                                <option value="除模式">除 (÷)</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="strategy-item">
                                        <label class="layui-form-label">调整值</label>
                                        <div class="layui-input-block">
                                            <input type="number" id="priceValue" class="layui-input"
                                                   step="0.0001" min="0"
                                                   placeholder="输入调整值">
                                        </div>
                                    </div>

                                    <div class="strategy-item">
                                        <label class="layui-form-label">货号前缀</label>
                                        <div class="layui-input-block">
                                            <input type="text" id="offerPrefix" class="layui-input"
                                                   maxlength="10" placeholder="可选，如：ABC">
                                        </div>
                                    </div>
                                    
                                    <div class="strategy-item">
                                        <label class="layui-form-label">货号创建方法</label>
                                        <div class="layui-input-block">
                                            <select id="offerIdMethod" class="layui-select" lay-filter="offerIdMethod">
                                                <option value="random">随机大写英文字母</option>
                                                <option value="sku_based">被跟卖SKU+随机大写字母</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="strategy-item">
                                        <label class="layui-form-label">库存设置</label>
                                        <div class="layui-input-block">
                                            <input type="number" id="stockInput" class="layui-input"
                                                   min="1" step="1" 
                                                   placeholder="库存数量">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 货号设置区域 -->
                                <div class="offer-id-settings" style="display: none;">
                                </div>
                                
                                <div class="layui-form-item" style="margin-top: 15px;">
                                    <label class="layui-form-label" style="width: auto; padding-left:0;">SKU分配</label>
                                    <div class="layui-input-block" style="margin-left: 100px;">
                                        <input type="checkbox" id="distributeSkus" lay-skin="switch" lay-text="均分|不均分">
                                    </div>
                                </div>
                                <div class="layui-form-item" style="margin-top: 15px;">
                                    <label class="layui-form-label" style="width: auto; padding-left:0;">随机价格区间</label>
                                    <div class="layui-input-block" style="margin-left: 130px; display: flex; align-items: center; gap: 10px;">
                                        <input type="number" id="priceRangeMin" class="layui-input" 
                                               step="0.01" placeholder="最小值" style="width: 100px;">
                                        <span>至</span>
                                        <input type="number" id="priceRangeMax" class="layui-input" 
                                               step="0.01" placeholder="最大值" style="width: 100px;">
                                        <small style="color: #666; margin-left: 10px;">
                                            <i class="layui-icon layui-icon-tips"></i> 
                                            在原价基础上随机增减，支持负数<br>
                                            只填最小值：最小值到最小值+原价20%；只填最大值：负最大值到最大值
                                        </small>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="margin-top: 15px;">
                                    <label class="layui-form-label" style="width: auto; padding-left:0;">水印设置</label>
                                    <div class="layui-input-block" style="margin-left: 100px;">
                                        <select id="watermarkMode" class="layui-select">
                                            <option value="0">不设置水印</option>
                                            <option value="1">文字水印</option>
                                            <option value="2">图片水印</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 按钮组 -->
                        <div class="button-group">
                            <button id="refreshStoresBtn" class="layui-btn layui-btn-normal">
                                <i class="layui-icon layui-icon-refresh-1"></i> 刷新店铺
                            </button>
                            <button id="clearCacheBtn" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-delete"></i> 清除缓存
                            </button>
                            <button id="resetBtn" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                            <button id="submitBtn" class="layui-btn layui-btn-disabled" disabled>
                                <i class="layui-icon layui-icon-upload"></i> 开始导入
                            </button>
                        </div>
                    </div>
                    
                    <!-- 右侧面板 - 仓库选择 -->
                    <div class="right-panel">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <div class="group-filter-container">
                                    <h3><i class="layui-icon layui-icon-app"></i> 2. 店铺仓库选择</h3>
                                    <div class="group-filter">
                                        <select id="groupFilter" class="layui-select" lay-filter="groupFilter">
                                            <option value="all">所有分组</option>
                                            <option value="ungrouped">未分组</option>
                                            <!-- 分组选项将通过JS动态填充 -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-alert layui-alert-normal" id="ungroupedStoreNotice" style="display:none; margin-bottom: 10px;">
                                    <i class="layui-icon layui-icon-tips"></i>
                                    发现未分组的店铺（橙色边框），建议通过店铺管理页面对这些店铺进行分组管理。
                                </div>
                                <div class="stores-container" id="storesContainer">
                                    <div class="layui-anim layui-anim-rotate layui-anim-loop">
                                        <i class="layui-icon layui-icon-loading"></i> 加载店铺中...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/static/layui/layui.js"></script>
<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 存储键名
    const STORAGE_KEY = 'ozon_import_settings';
    
    // 分组映射
    let groupMap = {};
    let shopGroupMap = {}; // 店铺ID到分组ID的映射
    
    // 初始化加载
    $(function(){
        // 检查缓存是否过期（超过24小时）
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                const now = Date.now();
                // 如果缓存超过24小时或没有时间戳，清除缓存
                if (!data.timestamp || (now - data.timestamp > 24 * 60 * 60 * 1000)) {
                    console.log('缓存已过期，清除旧数据');
                    localStorage.removeItem(STORAGE_KEY);
                }
            } catch (e) {
                console.error('解析缓存数据失败，清除缓存:', e);
                localStorage.removeItem(STORAGE_KEY);
            }
        }
        
        // 先加载分组数据，并在完成后再加载店铺
        loadShopGroups().then(() => {
            // 然后加载店铺数据
            return loadStores();
        }).then(() => {
            console.log('初始化加载完成');
        }).catch(err => {
            console.error('加载数据失败:', err);
            // 如果分组加载失败，仍然尝试加载店铺数据
            loadStores().catch(storeErr => {
                console.error('店铺数据也加载失败:', storeErr);
                layer.msg('数据加载失败，请刷新页面重试', {icon: 2, time: 5000});
            });
        });
        
        $('#skuInput').on('input', updateLineCount);
        loadPriceSettings();

        // 新增：仓库项目点击事件 (事件委托)
        $('#storesContainer').on('click', '.warehouse-item:not(.disabled)', function() {
            const $this = $(this);
            const warehouseSelect = $this.closest('.warehouse-select');
            
            if ($this.hasClass('selected')) {
                // 取消选择
                $this.removeClass('selected');
                warehouseSelect.removeAttr('data-selected-warehouse');
            } else {
                // 选中
                $this.siblings().removeClass('selected');
                $this.addClass('selected');
                warehouseSelect.attr('data-selected-warehouse', $this.data('value'));
            }
            
            saveSettings();
        });
    });

    // 加载分组数据 - 使用与 store_manage.php 相同的接口
    function loadShopGroups() {
        return new Promise((resolve, reject) => {
            layer.load(1, {shade: [0.1, '#fff']});
            $.ajax({
                url: 'ajax.php?act=getShopGroups',
                type: 'GET',
                dataType: 'json',
                cache: false, // 禁用缓存
                data: {
                    '_t': Date.now() // 添加时间戳参数避免缓存
                },
                success: function(res) {
                    layer.closeAll('loading');
                    if(res && res.code === 0 && res.data) {
                        // 创建分组映射 - 适配新的数据结构
                        groupMap = {};
                        shopGroupMap = {}; // 清空旧映射
                        
                        res.data.forEach(group => {
                            if (group.id && group.name) {
                                groupMap[group.id] = group.name;
                                
                                // 记录默认分组
                                if (group.isDefault) {
                                    defaultGroupId = group.id;
                                }
                                
                                // 创建店铺到分组的映射
                                if (group.shopIds && Array.isArray(group.shopIds)) {
                                    group.shopIds.forEach(shopId => {
                                        shopGroupMap[shopId] = group.id;
                                    });
                                }
                            }
                        });
                        
                        // 渲染分组筛选器
                        renderGroupFilter(res.data);
                        console.log('分组数据加载成功，共', res.data.length, '个分组');
                        resolve(res.data);
                    } else {
                        console.warn('分组数据加载失败或为空', res);
                        renderGroupFilter([]); // 使用空数组渲染
                        resolve([]); // 即使失败也不拒绝，继续加载店铺
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll('loading');
                    console.error('分组数据请求失败', error);
                    renderGroupFilter([]); // 使用空数组渲染
                    resolve([]); // 即使失败也不拒绝，继续加载店铺
                }
            });
        });
    }

    // 渲染分组筛选器
    function renderGroupFilter(groups) {
        const $filter = $('#groupFilter');
        $filter.find('option:gt(1)').remove(); // 清除旧分组选项
        
        if (groups && groups.length > 0) {
            groups.forEach(group => {
                if (group.id && group.name) {
                    $filter.append(`<option value="${group.id}">${group.name}</option>`);
                }
            });
            console.log('分组筛选器已更新，包含 ' + groups.length + ' 个分组');
        } else {
            console.log('没有可用的分组，只显示默认选项');
        }
        
        // 强制重新渲染select
        form.render('select');
    }

    // 加载店铺数据 - 临时添加调试，同时测试两个接口
    function loadStores() {
        return new Promise((resolve, reject) => {
            layer.load(1, {shade: [0.1, '#fff']});
            
            // 同时测试两个接口以对比数据结构
            console.log('开始测试两个接口的数据结构...');
            
            // 测试原接口
            $.ajax({
                url: '/api/get',
                type: 'GET',
                dataType: 'json',
                cache: false,
                data: { '_t': Date.now() },
                success: function(res1) {
                    console.log('原接口 /api/get 返回数据:', res1);
                    
                    // 测试新接口
                    $.ajax({
                        url: 'ajax.php?act=store_List',
                        type: 'GET', 
                        dataType: 'json',
                        cache: false,
                        data: { '_t': Date.now() },
                        success: function(res2) {
                            console.log('新接口 ajax.php?act=store_List 返回数据:', res2);
                            
                            // 使用原接口的数据结构，但增强缓存处理
                            layer.closeAll('loading');
                            if(res1 && res1.stores && res1.stores.length > 0) {
                                // 确保每个店铺都有warehouses数组
                                res1.stores.forEach(store => {
                                    if (!store.warehouses || !Array.isArray(store.warehouses)) {
                                        store.warehouses = []; // 确保warehouses是数组
                                    }
                                });
                                
                                renderStores(res1.stores);
                                form.render();
                                
                                // 加载缓存的店铺和仓库选择
                                loadStoreSelections();
                                
                                $('#submitBtn').removeClass('layui-btn-disabled').prop('disabled', false);
                                
                                console.log(`店铺加载完成，共 ${res1.stores.length} 个店铺`);
                                resolve(res1.stores);
                            } else {
                                const errorMsg = '店铺加载失败或没有可用店铺';
                                showError(errorMsg);
                                console.error('店铺数据错误:', res1);
                                reject(new Error(errorMsg));
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('新接口请求失败:', error);
                            // 新接口失败，继续使用原接口
                            layer.closeAll('loading');
                            if(res1 && res1.stores && res1.stores.length > 0) {
                                res1.stores.forEach(store => {
                                    if (!store.warehouses || !Array.isArray(store.warehouses)) {
                                        store.warehouses = [];
                                    }
                                });
                                
                                renderStores(res1.stores);
                                form.render();
                                loadStoreSelections();
                                $('#submitBtn').removeClass('layui-btn-disabled').prop('disabled', false);
                                
                                console.log(`店铺加载完成，共 ${res1.stores.length} 个店铺`);
                                resolve(res1.stores);
                            } else {
                                const errorMsg = '店铺加载失败或没有可用店铺';
                                showError(errorMsg);
                                console.error('店铺数据错误:', res1);
                                reject(new Error(errorMsg));
                            }
                        }
                    });
                },
                error: function(xhr, status, error) {
                    layer.closeAll('loading');
                    const errorMsg = '店铺数据加载失败';
                    showError(errorMsg);
                    console.error('原接口数据请求失败:', error);
                    reject(new Error(errorMsg + ': ' + error));
                }
            });
        });
    }

    // 加载价格策略缓存
    function loadPriceSettings() {
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                if (data.pricing) {
                    $('#priceMode').val(data.pricing.mode || 'none');
                    if (data.pricing.value) {
                        $('#priceValue').val(data.pricing.value);
                    }
                    form.render('select');
                }
                if (data.stock) {
                    $('#stockInput').val(data.stock);
                }
                // 加载随机价格区间设置
                if (data.priceRange) {
                    if (data.priceRange.min !== undefined) {
                        $('#priceRangeMin').val(data.priceRange.min);
                    }
                    if (data.priceRange.max !== undefined) {
                        $('#priceRangeMax').val(data.priceRange.max);
                    }
                }
                // 加载货号设置
                if (data.offerIdSettings) {
                    if (data.offerIdSettings.prefix) {
                        $('#offerPrefix').val(data.offerIdSettings.prefix);
                    }
                    if (data.offerIdSettings.method) {
                        $('#offerIdMethod').val(data.offerIdSettings.method);
                        form.render('select');
                    }
                }
            } catch (e) {
                console.error('解析缓存数据失败:', e);
            }
        }
    }

    // 强制刷新店铺数据（不使用缓存）
    function refreshStoresData() {
        const loadingIndex = layer.load(1, {
            shade: [0.2, '#fff'],
            content: '正在刷新店铺和分组数据...'
        });
        
        // 清空现有映射，强制重新获取
        groupMap = {};
        shopGroupMap = {};
        
        // 先重新加载分组数据
        loadShopGroups().then(() => {
            console.log('分组数据已刷新，店铺分组映射：', shopGroupMap);
            // 然后重新加载店铺数据
            return loadStores();
        }).then(() => {
            layer.close(loadingIndex);
            layer.msg('店铺和分组数据已刷新', {icon: 1, time: 2000});
            
            // 检查是否有新店铺可能需要分组更新
            checkNewStoresGrouping();
        }).catch(err => {
            layer.close(loadingIndex);
            console.error('刷新数据失败:', err);
            // 如果分组加载失败，仍然刷新店铺数据
            loadStores().then(() => {
                layer.msg('店铺数据已刷新（分组数据可能未更新）', {icon: 2, time: 3000});
            });
        });
    }
    
    // 检查新店铺的分组状态 - 兼容两种数据结构
    function checkNewStoresGrouping() {
        const ungroupedStores = [];
        const newStores = [];
        
        // 检测未分组和新店铺
        $('.store-item').each(function() {
            const $item = $(this);
            const groupId = $item.attr('data-group-id');
            const groupName = $item.attr('data-group-name');
            const isNew = $item.attr('data-is-new') === 'true';
            const storeName = $item.find('.store-name').text();
            
            // 检查是否为未分组
            const isUngrouped = (groupId === 'ungrouped') || 
                               (!groupName || groupName === '未分组');
            
            if (isNew) {
                newStores.push(storeName);
            } else if (isUngrouped) {
                ungroupedStores.push(storeName);
            }
        });
        
        // 优先提示新店铺
        if (newStores.length > 0) {
            console.log('发现新店铺:', newStores);
            layer.msg(`发现 ${newStores.length} 个新店铺，已用橙色边框标识。建议通过店铺管理页面进行分组。`, {
                icon: 0, 
                time: 6000,
                area: ['450px', 'auto']
            });
        } else if (ungroupedStores.length > 0) {
            console.log('发现未分组的店铺:', ungroupedStores);
            if (ungroupedStores.length <= 3) {
                // 少量未分组店铺，显示详细提示
                layer.msg(`发现 ${ungroupedStores.length} 个未分组店铺（${ungroupedStores.join('、')}），建议通过店铺管理页面进行分组。`, {
                    icon: 0, 
                    time: 6000,
                    area: ['450px', 'auto']
                });
            } else {
                // 较多未分组店铺，显示简化提示
                layer.msg(`发现 ${ungroupedStores.length} 个未分组店铺，建议通过店铺管理页面进行分组。`, {
                    icon: 0, 
                    time: 5000,
                    area: ['400px', 'auto']
                });
            }
        }
    }

    // 加载店铺和仓库选择缓存
    function loadStoreSelections() {
        // 先清除所有选择状态
        $('input[name="selectedStore"]').prop('checked', false);
        $('.store-item').removeClass('selected has-warehouse');
        $('.warehouse-dropdown').hide();
        $('.warehouse-item').removeClass('selected');
        $('.warehouse-dropdown').removeAttr('data-selected-warehouse').removeAttr('data-selected-name');
        
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (!savedData) return;

        try {
            const data = JSON.parse(savedData);
            if (!data.stores || !data.stores.length) return;

            // 遍历缓存的店铺选择
            data.stores.forEach(savedStore => {
                const storeId = savedStore.storeId;
                const warehouseId = savedStore.warehouse;
                
                if (!storeId) return;
                
                // 检查店铺是否存在于当前页面
                const storeItem = $(`.store-item[data-store-id="${storeId}"]`);
                if (!storeItem.length) return; // 如果店铺不存在，跳过
                
                const checkbox = storeItem.find('input[type="checkbox"]');
                const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
                
                if (checkbox.length) {
                    // 选中复选框
                    checkbox.prop('checked', true);
                    storeItem.addClass('selected');
                    
                    // 如果有选择仓库
                    if (warehouseId) {
                        // 找到对应的仓库并选中
                        const warehouseItem = warehouseDropdown.find(`.warehouse-item[data-value="${warehouseId}"]`);
                        if (warehouseItem.length) {
                            warehouseItem.addClass('selected');
                            warehouseDropdown.attr('data-selected-warehouse', warehouseId);
                            warehouseDropdown.attr('data-selected-name', warehouseItem.data('name'));
                            storeItem.addClass('has-warehouse');
                        }
                    }
                }
            });
            
            form.render('checkbox');
        } catch (e) {
            console.error('解析店铺选择缓存失败:', e);
        }
    }

    // 保存当前设置到本地存储
    function saveSettings() {
        try {
            // 清除之前的缓存数据
            localStorage.removeItem(STORAGE_KEY);
            
            const data = {
                stores: [],
                stock: $('#stockInput').val(),
                pricing: {
                    mode: $('#priceMode').val(),
                    value: $('#priceValue').val()
                },
                priceRange: {
                    min: $('#priceRangeMin').val(),
                    max: $('#priceRangeMax').val()
                },
                offerIdSettings: {
                    prefix: $('#offerPrefix').val(),
                    method: $('#offerIdMethod').val()
                },
                timestamp: Date.now() // 添加时间戳以便追踪缓存时间
            };

            // 收集选中的店铺和仓库
            $('input[name="selectedStore"]:checked').each(function() {
                const storeId = $(this).val();
                const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
                const warehouseId = warehouseDropdown.attr('data-selected-warehouse');
                
                // 只保存有效的仓库选择
                if (warehouseId) {
                    data.stores.push({
                        storeId: storeId,
                        warehouse: warehouseId
                    });
                }
            });

            localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
            console.log('已保存设置，包含', data.stores.length, '个店铺选择');
        } catch (e) {
            console.error('保存设置失败:', e);
        }
    }

    // 重置所有设置
    function resetSettings() {
        // 清空商品输入
        $('#skuInput').val('');
        $('#lineCount').text('0');
        
        // 取消所有店铺选择
        $('input[name="selectedStore"]').prop('checked', false);
        $('.store-item').removeClass('selected has-warehouse');
        $('.warehouse-dropdown').hide();
        $('.warehouse-item').removeClass('selected');
        $('.warehouse-dropdown').removeAttr('data-selected-warehouse').removeAttr('data-selected-name');
        
        // 重置库存
        $('#stockInput').val('');
        
        // 重置价格策略
        $('#priceMode').val('none');
        $('#priceValue').val('').prop('disabled', true).addClass('layui-disabled');
        
        // 重置随机价格区间
        $('#priceRangeMin').val('');
        $('#priceRangeMax').val('');
        
        // 重置货号设置
        $('#offerPrefix').val('');
        $('#offerIdMethod').val('random');
        
        // 重置分组筛选
        $('#groupFilter').val('all');
        form.render('select');
        
        // 显示所有店铺
        $('.store-item').show();
        
        // 重新渲染表单
        form.render();
        
        // 清除本地存储
        localStorage.removeItem(STORAGE_KEY);
        
        layer.msg('已重置所有设置', {icon: 1});
    }

    // 店铺复选框选择事件处理
    form.on('checkbox(storeCheckbox)', function(data){
        const storeId = $(data.elem).val();
        handleStoreSelection(storeId, data.elem.checked);
    });

    // 价格策略切换处理
    form.on('select(priceMode)', function(data){
        const isDisabled = data.value === 'none';
        $('#priceValue')
            .prop('disabled', isDisabled)
            .toggleClass('layui-disabled', isDisabled)
            .val('');
        form.render('input');
        
        // 保存设置
        saveSettings();
    });
    
    // 分组筛选处理 - 兼容两种数据结构
    form.on('select(groupFilter)', function(data) {
        const filterValue = data.value;
        
        // 显示所有卡片
        $('.store-item').show();
        
        if (filterValue === 'all') {
            return; // 显示所有
        }
        
        if (filterValue === 'ungrouped') {
            // 显示未分组的店铺
            $('.store-item').each(function() {
                const $item = $(this);
                const groupId = $item.attr('data-group-id');
                const groupName = $item.attr('data-group-name');
                
                // 检查是否为未分组
                const isUngrouped = (groupId === 'ungrouped') || 
                                   (!groupName || groupName === '未分组');
                
                if (!isUngrouped) {
                    $item.hide();
                }
            });
        } else {
            // 显示指定分组的店铺
            const targetGroupName = groupMap[filterValue];
            $('.store-item').each(function() {
                const $item = $(this);
                const groupId = $item.attr('data-group-id');
                const groupName = $item.attr('data-group-name');
                
                // 检查是否匹配筛选条件
                const isMatch = (groupId === filterValue) || 
                               (groupName === targetGroupName);
                
                if (!isMatch) {
                    $item.hide();
                }
            });
        }
    });
    
    // 价格值和库存输入变化事件
    $('#priceValue, #stockInput, #priceRangeMin, #priceRangeMax, #offerPrefix').on('input', function() {
        saveSettings();
    });
    
    // 货号创建方法选择变化事件
    form.on('select(offerIdMethod)', function(data){
        saveSettings();
    });
    
    // 清除缓存按钮点击事件
    $('#clearCacheBtn').click(function() {
        layer.confirm('选择清除缓存方式：<br/><strong>清除本地缓存</strong>：只清除浏览器本地设置<br/><strong>强制刷新</strong>：清除所有缓存并重新加载店铺数据', {
            icon: 3, 
            title:'清除缓存方式', 
            area: ['350px', 'auto'],
            btn: ['清除本地缓存', '强制刷新', '取消']
        }, function(index){
            // 只清除本地缓存
            localStorage.removeItem(STORAGE_KEY);
            layer.msg('本地缓存已清除', {icon: 1});
            layer.close(index);
            
            // 重置设置但不重新加载
            resetSettings();
        }, function(index){
            // 强制刷新所有数据
            localStorage.removeItem(STORAGE_KEY);
            layer.msg('正在强制刷新数据...', {icon: 16});
            layer.close(index);
            
            // 重新加载页面以强制刷新所有数据
            location.reload();
        });
    });
    
    // 刷新店铺按钮点击事件
    $('#refreshStoresBtn').click(function() {
        refreshStoresData();
    });
    
    // 重置按钮点击事件
    $('#resetBtn').click(function() {
        layer.confirm('确定要重置所有设置吗？', {icon: 3, title:'重置确认'}, function(index){
            resetSettings();
            layer.close(index);
        });
    });
    
    // 提交处理
    $('#submitBtn').click(function() {
        try {
            const data = validateForm();
            if(data){
                submitData(data);
            }
        } catch (e) {
            showError(e.message);
        }
    });

    // 渲染店铺 - 网格布局
    function renderStores(stores) {
        const container = $('#storesContainer');
        container.empty();
        
        if (!stores || !stores.length) {
            container.html('<div class="layui-empty">没有可用的店铺</div>');
            return;
        }
        
        stores.forEach(store => {
            // 获取分组信息，兼容两种数据结构
            let groupId, groupName, isNewStore;
            
            if (store.Groupname) {
                // 如果店铺数据直接包含分组名称（新接口）
                groupName = store.Groupname;
                isNewStore = false;
            } else {
                // 使用分组映射（原接口）
                groupId = shopGroupMap[store.id] || 'ungrouped';
                groupName = groupMap[groupId] || '未分组';
                isNewStore = !shopGroupMap.hasOwnProperty(store.id);
            }
            
            // 如果是新店铺，添加到日志
            if (isNewStore) {
                console.log(`检测到可能的新店铺: ${store.storename} (ID: ${store.id})`);
            }
            
            // 确保warehouses是数组
            const warehouses = store.warehouses || [];
            
            // 生成仓库选项
            let warehouseItems = '';
            if (warehouses.length > 0) {
                warehouses.forEach(wh => {
                    warehouseItems += `
                        <div class="warehouse-item ${wh.status === 0 ? 'disabled' : ''}" 
                             data-value="${wh.id}" data-name="${wh.name}">
                            ${wh.name}
                        </div>
                    `;
                });
            } else {
                warehouseItems = '<div class="warehouse-item disabled">暂无可用仓库</div>';
            }
            
            // 为未分组店铺添加特殊标识
            const ungroupedClass = isNewStore ? ' new-store' : '';
            const groupTag = isNewStore ? 
                `<span class="group-tag new-tag">${groupName} <i class="layui-icon layui-icon-tips" title="未分组店铺，建议进行分组管理"></i></span>` :
                `<span class="group-tag">${groupName}</span>`;
            
            const dataAttributes = groupId ? `data-group-id="${groupId}"` : `data-group-name="${groupName}"`;
            const storeItem = `
                <div class="store-item${ungroupedClass}" data-store-id="${store.id}" ${dataAttributes} ${isNewStore ? 'data-is-new="true"' : ''}>
                    <input type="checkbox" name="selectedStore" value="${store.id}" title="" lay-filter="storeCheckbox">
                    <div class="store-content">
                        <span class="store-name" title="${store.storename}">${store.storename}</span>
                        ${groupTag}
                        <span class="selected-mark"><i class="layui-icon layui-icon-ok-circle"></i></span>
                    </div>
                    <div class="warehouse-dropdown" data-store-id="${store.id}">
                        <div class="warehouse-dropdown-title">选择仓库</div>
                        <div class="warehouse-list">
                            ${warehouseItems}
                        </div>
                    </div>
                </div>
            `;
            
            container.append(storeItem);
        });
        
        // 初始化点击事件
        $('.store-item').on('click', function(e) {
            // 阻止事件冒泡，防止点击仓库选项时也触发店铺点击
            if ($(e.target).closest('.warehouse-dropdown').length) {
                return;
            }
            
            const storeId = $(this).data('store-id');
            const checkbox = $(this).find('input[type="checkbox"]');
            
            // 切换选中状态
            checkbox.prop('checked', !checkbox.prop('checked'));
            
            // 更新选中状态
            handleStoreSelection(storeId, checkbox.prop('checked'));
        });
        
        // 仓库项目点击事件
        $(document).on('click', '.warehouse-item:not(.disabled)', function(e) {
            e.stopPropagation();
            
            const $this = $(this);
            const warehouseDropdown = $this.closest('.warehouse-dropdown');
            const storeId = warehouseDropdown.data('store-id');
            const storeItem = $(`.store-item[data-store-id="${storeId}"]`);
            
            // 更新选中状态
            warehouseDropdown.find('.warehouse-item').removeClass('selected');
            $this.addClass('selected');
            
            // 保存选中的仓库ID和名称
            warehouseDropdown.attr('data-selected-warehouse', $this.data('value'));
            warehouseDropdown.attr('data-selected-name', $this.data('name'));
            
            // 更新店铺项的状态
            storeItem.addClass('has-warehouse');
            
            // 隐藏下拉菜单
            warehouseDropdown.hide();
            
            // 保存设置
            saveSettings();
        });
        
        // 点击文档其他部分关闭仓库下拉
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.warehouse-dropdown').length && 
                !$(e.target).closest('.store-item.selected').length) {
                $('.warehouse-dropdown').hide();
            }
        });
        
        // 检查是否有未分组店铺并显示提示
        let ungroupedCount = 0;
        $('.store-item').each(function() {
            const $item = $(this);
            const groupId = $item.attr('data-group-id');
            const groupName = $item.attr('data-group-name');
            const isUngrouped = (groupId === 'ungrouped') || 
                               (!groupName || groupName === '未分组') ||
                               $item.hasClass('new-store');
            if (isUngrouped) ungroupedCount++;
        });
        
        if (ungroupedCount > 0) {
            $('#ungroupedStoreNotice').show();
            console.log(`发现 ${ungroupedCount} 个未分组或新店铺`);
        } else {
            $('#ungroupedStoreNotice').hide();
        }
        
        // 加载已保存的选择
        loadStoreSelections();
    }
    
    // 处理店铺选择状态
    function handleStoreSelection(storeId, isChecked) {
        const storeItem = $(`.store-item[data-store-id="${storeId}"]`);
        const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
        
        // 关闭其他下拉
        $('.warehouse-dropdown').not(warehouseDropdown).hide();
        
        if (isChecked) {
            // 添加选中样式
            storeItem.addClass('selected');
            
            // 显示仓库下拉
            const offset = storeItem.offset();
            warehouseDropdown.css({
                top: '100%',
                left: 0
            }).show();
            
            // 清除之前的仓库选择
            warehouseDropdown.find('.warehouse-item').removeClass('selected');
            warehouseDropdown.removeAttr('data-selected-warehouse');
            warehouseDropdown.removeAttr('data-selected-name');
            storeItem.removeClass('has-warehouse');
        } else {
            // 移除选中样式
            storeItem.removeClass('selected has-warehouse');
            
            // 隐藏下拉并清除选择
            warehouseDropdown.hide();
            warehouseDropdown.find('.warehouse-item').removeClass('selected');
            warehouseDropdown.removeAttr('data-selected-warehouse');
            warehouseDropdown.removeAttr('data-selected-name');
        }
        
        // 保存设置
        saveSettings();
    }

    function validateForm() {
        // 商品数据 - 使用Map去重SKU
        const skuMap = new Map();
        const skuLines = document.getElementById('skuInput').value.split('\n');
        let duplicateCount = 0;
        
        skuLines.forEach(line => {
            const [sku, price] = line.trim().split(/\s+/);
            if (sku && !isNaN(price)) {
                if (skuMap.has(sku)) {
                    duplicateCount++;
                    console.log(`重复SKU: ${sku}, 保留最新价格: ${price}`);
                }
                // 如果SKU重复，使用最新的价格
                skuMap.set(sku, parseFloat(price));
            }
        });
        

        
        // 转换为数组，保持原价不变
        const products = Array.from(skuMap, ([sku, price]) => ({ sku, price }));
        
        if (products.length === 0) throw new Error('至少需要一个有效商品');
        
        // 显示去重信息
        if (duplicateCount > 0) {
            layer.msg(`已自动去重 ${duplicateCount} 个重复SKU，当前有效商品: ${products.length} 个`, {icon: 1, time: 3000});
        }

        // 仓库选择
        const selectedStores = [];
        $('input[name="selectedStore"]:checked').each(function() {
            const storeId = $(this).val();
            const storeName = $(this).closest('.store-item').find('.store-name').text();
            const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
            const warehouseId = warehouseDropdown.attr('data-selected-warehouse');
            
            if (!warehouseId) {
                throw new Error(`请为店铺 "${storeName}" 选择一个仓库`);
            }
            
            selectedStores.push({
                storeId: storeId,
                warehouse: warehouseId
            });
        });
        
        if (selectedStores.length === 0) throw new Error('请至少选择一个店铺和仓库');

        // 如果开启了均分，检查SKU数量是否足够
        if ($('#distributeSkus').is(':checked') && products.length < selectedStores.length) {
             showError(`SKU数量(${products.length})少于店铺数量(${selectedStores.length})，无法均分。`);
             return null;
        }

        // 库存验证
        var stock = parseInt(document.getElementById('stockInput').value);
        if(!stock){
            stock = 0;
        }

        // 价格策略
        const pricing = { mode: 'none' };
        const priceMode = document.getElementById('priceMode').value;
        if (priceMode !== 'none') {
            const value = parseFloat(document.getElementById('priceValue').value);
            if (isNaN(value)) throw new Error('请输入有效的调整值');
            pricing.mode = priceMode;
            pricing.value = value;
        }

        // 随机价格区间验证
        const minValue = document.getElementById('priceRangeMin').value;
        const maxValue = document.getElementById('priceRangeMax').value;
        const priceRange = {};
        
        if (minValue || maxValue) {
            if (minValue && isNaN(parseFloat(minValue))) {
                throw new Error('随机价格区间最小值必须是有效数字');
            }
            if (maxValue && isNaN(parseFloat(maxValue))) {
                throw new Error('随机价格区间最大值必须是有效数字');
            }
            if (minValue && maxValue && parseFloat(minValue) > parseFloat(maxValue)) {
                throw new Error('随机价格区间最小值不能大于最大值');
            }
            
            if (minValue) priceRange.min = parseFloat(minValue);
            if (maxValue) priceRange.max = parseFloat(maxValue);
        }

        // 货号设置
        const offerIdSettings = {
            prefix: document.getElementById('offerPrefix').value.trim(),
            method: document.getElementById('offerIdMethod').value
        };

        // 水印设置
        const watermarkMode = parseInt(document.getElementById('watermarkMode').value);

        return {
            products,
            stores: selectedStores,
            config: {
                inventory: {
                    threshold: stock
                },
                randomPrice: priceRange,
                pricing,
                offerIdSettings
            },
            watermark: watermarkMode
        };
    }

    function showError(message) {
        layer.msg(message, {icon: 2, time: 3000});
    }

    function submitData(data) {
        const distributeSkus = $('#distributeSkus').is(':checked');

        if (distributeSkus) {
            submitDistributed(data);
            return;
        }

        const totalProducts = data.products.length;
        
        // 构建确认消息
        let confirmMsg = `即将导入 ${totalProducts} 个商品到 ${data.stores.length} 个店铺`;
        
        // 检查是否设置了随机价格区间
        const minValue = document.getElementById('priceRangeMin').value;
        const maxValue = document.getElementById('priceRangeMax').value;
        if (minValue || maxValue) {
            let rangeText = '';
            if (minValue && maxValue) {
                rangeText = `${parseFloat(minValue)} 至 ${parseFloat(maxValue)}`;
            } else if (minValue) {
                rangeText = `${parseFloat(minValue)} 至 ${parseFloat(minValue)}+原价20%`;
            } else if (maxValue) {
                rangeText = `-${Math.abs(parseFloat(maxValue))} 至 ${parseFloat(maxValue)}`;
            }
            confirmMsg += `<br><span style="color: #ff6800;">随机价格区间: ${rangeText}（每个店铺独立计算）</span>`;
        }
        confirmMsg += `，是否继续？`;
        
        // 单次提交，后端直接处理
        layer.confirm(confirmMsg, {
            icon: 3, 
            title: '批量导入确认',
            area: ['400px', 'auto']
        }, function(index) {
            layer.close(index);
            submitToBackend(data);
        });
    }

    function submitDistributed(data) {
        const products = data.products;
        const stores = data.stores;
        const totalProducts = products.length;
        const totalStores = stores.length;
        const skusPerStore = Math.floor(totalProducts / totalStores);
        let remainder = totalProducts % totalStores;

        let confirmationMsg = `共 ${totalProducts} 个SKU, ${totalStores} 个店铺。<br>每个店铺将分配 ${skusPerStore} 个SKU。`;
        if (remainder > 0) {
            confirmationMsg += `<br>剩余 ${remainder} 个SKU将依次分配给前面的店铺。`;
        }
        // 检查是否设置了随机价格区间
        const minValue = document.getElementById('priceRangeMin').value;
        const maxValue = document.getElementById('priceRangeMax').value;
        if (minValue || maxValue) {
            let rangeText = '';
            if (minValue && maxValue) {
                rangeText = `${parseFloat(minValue)} 至 ${parseFloat(maxValue)}`;
            } else if (minValue) {
                rangeText = `${parseFloat(minValue)} 至 ${parseFloat(minValue)}+原价20%`;
            } else if (maxValue) {
                rangeText = `-${Math.abs(parseFloat(maxValue))} 至 ${parseFloat(maxValue)}`;
            }
            confirmationMsg += `<br><span style="color: #ff6800;">已应用随机价格区间: ${rangeText}（价格已计算）</span>`;
        }
        confirmationMsg += `<br>后端将自动处理分配，是否继续？`;

        layer.confirm(confirmationMsg, {
            icon: 3,
            title: '均分SKU确认',
            area: ['350px', 'auto']
        }, function(index) {
            layer.close(index);
            
            // 标记为均分模式，让后端处理分配逻辑
            data.distributeSkus = true;
            submitToBackend(data);
        });
    }
    
    // 单次提交到后端处理
    function submitToBackend(data) {
        // 输出调试信息
        console.log('提交数据:', data);
        
        // 检查是否使用了随机价格区间
        const minValue = document.getElementById('priceRangeMin').value;
        const maxValue = document.getElementById('priceRangeMax').value;
        if (minValue || maxValue) {
            console.log('随机价格区间设置:', {min: minValue, max: maxValue});
            console.log('价格已在前端计算完成，商品数据包含调整后的价格');
        }
        
        const loadingIndex = layer.load(1, {
            shade: [0.3, '#fff'],
            content: `正在处理 ${data.products.length} 个商品...`
        });
        
        $.ajax({
            url: '/api/plimport', // 使用新的接口
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data),
            timeout: 60000, // 60秒超时
            success: function(res) {
                layer.close(loadingIndex);
                
                if(res.code == 0) {
                    layer.msg(res.msg || '导入成功！', {
                        icon: 1, 
                        time: 4000,
                        area: ['400px', 'auto']
                    });
                    
                    // 成功后询问是否清空输入
                    setTimeout(() => {
                        layer.confirm('是否清空当前输入的商品信息？', {
                            icon: 3,
                            title: '清空确认'
                        }, function(index) {
                            $('#skuInput').val('');
                            updateLineCount();
                            layer.close(index);
                        });
                    }, 2000);
                } else {
                    layer.msg(res.msg || '导入失败，请重试', {
                        icon: 2,
                        time: 4000,
                        area: ['400px', 'auto']
                    });
                    
                    // 如果有错误详情，显示
                    if (res.errors && res.errors.length > 0) {
                        console.log('导入错误详情:', res.errors);
                    }
                }
            },
            error: function(xhr, status, error) {
                layer.close(loadingIndex);
                
                let errorMsg = '网络请求失败';
                if (status === 'timeout') {
                    errorMsg = '请求超时，请检查网络连接或稍后重试';
                } else if (xhr.responseText) {
                    try {
                        const res = JSON.parse(xhr.responseText);
                        errorMsg = res.msg || errorMsg;
                    } catch (e) {
                        errorMsg = '服务器响应异常';
                    }
                }
                
                layer.msg(errorMsg, {
                    icon: 2,
                    time: 5000,
                    area: ['350px', 'auto']
                });
            }
        });
    }

    function updateLineCount() {
        const skuMap = new Map();
        const skuLines = $('#skuInput').val().split('\n');
        let totalLines = 0;
        let validLines = 0;
        
        skuLines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                totalLines++;
                const [sku, price] = trimmedLine.split(/\s+/);
                if (sku && !isNaN(price)) {
                    skuMap.set(sku, parseFloat(price));
                    validLines++;
                }
            }
        });
        
        const uniqueCount = skuMap.size;
        const duplicateCount = validLines - uniqueCount;
        
        let text = `${uniqueCount}`;
        if (duplicateCount > 0) {
            text += ` (去重${duplicateCount}个)`;
        }
        if (totalLines > validLines) {
            text += ` / ${totalLines}行`;
        }
        
        $('#lineCount').text(text);
    }
});
</script>
</body>
</html>

<?php
/**
 * Ozon财务交易数据同步守护进程
 * 自动获取所有店铺的财务交易明细并同步到数据库
 * 
 * 使用方法:
 * 1. 单次执行: php ozon_finance_sync.php
 * 2. 守护进程: php ozon_finance_sync.php --daemon
 * 3. 调试模式: php ozon_finance_sync.php --debug
 * 4. 检查状态: php ozon_finance_sync.php --status
 * 5. 强制停止: php ozon_finance_sync.php --kill
 * 6. 指定月份: php ozon_finance_sync.php --months=3 (获取最近3个月数据)
 * 
 * 数据去重机制:
 * - 基于operation_id进行去重，防止重复处理同一笔交易
 * - 使用t_order_profit表的operation_ids字段记录已处理的操作ID
 * - 自动检查并添加operation_ids字段到现有表结构
 * - 支持多进程并发而不会产生重复数据
 * - 重复运行脚本不会导致金额叠加，已有完善的去重机制
 * 
 * 宝塔进程守护管理器配置建议:
 * - 启动文件: /www/wwwroot/你的域名/worker/ozon_finance_sync.php
 * - 运行目录: /www/wwwroot/你的域名/worker
 * - 进程数量: 1 (重要！只能设置为1)
 * - 自动重启: 开启
 */

// 设置执行环境
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 0);
error_reporting(E_ALL);

// 检查是否在CLI模式下运行
if (php_sapi_name() !== 'cli') {
    header('Content-Type: text/plain');
    echo "此脚本只能在命令行模式下运行\n";
    exit(1);
}

// 设置正确的工作目录，确保相对路径正确
chdir(__DIR__ . '/..');
require_once __DIR__ . '/../includes/common.php';

// 内置配置
$config = [
    'daemon_mode' => false,           // 是否以守护进程模式运行
    'sync_interval' => 18000,         // 同步间隔（秒），默认1小时
    'api_delay' => 10,               // API调用间隔（秒）
    'max_execution_time' => 88000,    // 最大执行时间（秒），1小时
    'log_level' => 'INFO',           // 日志级别: DEBUG, INFO, WARN, ERROR
    'log_file' => __DIR__ . '/logs/finance_sync.log', // 日志文件路径
    'bt_daemon_mode' => false,       // 宝塔守护进程模式（自动检测）
    'api_timeout' => 50,             // API超时时间（秒）
    'max_retries' => 5,              // API调用最大重试次数
    'retry_delay' => 6,              // 重试间隔（秒）
    'months_to_sync' => 8,           // 默认同步最近3个月的数据
    'page_size' => 1000,             // 每页数据量
    'multi_process' => false,        // 是否启用多进程模式
    'max_processes' => 4,            // 最大进程数
    'process_timeout' => 1800,       // 子进程超时时间（秒）
];

// 全局变量
$running = true;
$logDir = __DIR__ . '/logs';
$lockFileHandle = null; // 全局锁文件句柄

// 确保日志目录存在
if (!is_dir($logDir)) {
    if (!mkdir($logDir, 0755, true)) {
        echo "错误: 无法创建日志目录 {$logDir}\n";
        echo "请检查目录权限或手动创建该目录\n";
        exit(1);
    }
}

/**
 * 获取全局锁，确保只有一个实例在运行
 */
function acquireGlobalLock() {
    global $config, $logDir, $lockFileHandle;

    $lockFilePath = $logDir . '/finance_sync.lock';
    $lockFileHandle = fopen($lockFilePath, 'c+');

    if ($lockFileHandle === false) {
        writeLog('ERROR', '无法创建或打开锁文件', ['path' => $lockFilePath]);
        return false;
    }

    // 尝试以非阻塞方式获取排他锁
    if (!flock($lockFileHandle, LOCK_EX | LOCK_NB)) {
        // 检查锁是否已过期
        $pid = trim(fgets($lockFileHandle));
        if ($pid && function_exists('posix_getpgid') && posix_getpgid((int)$pid) === false) {
            // 进程不存在，我们可以获取锁
            writeLog('WARN', '发现过期的锁文件，强制获取', ['stale_pid' => $pid]);
            ftruncate($lockFileHandle, 0); // 清空文件
            rewind($lockFileHandle); // 重置文件指针
            if (flock($lockFileHandle, LOCK_EX)) {
                fwrite($lockFileHandle, getmypid());
                fflush($lockFileHandle);
                writeLog('INFO', '获取全局锁成功（强制）', ['pid' => getmypid()]);
                return true;
            }
        } else {
            writeLog('INFO', '另一个财务数据同步进程正在运行，退出当前进程');
            if ($config['bt_daemon_mode']) {
                writeLog('INFO', '宝塔守护模式：等待60秒后退出，避免频繁重启');
                sleep(60);
            }
            return false;
        }
    } else {
        // 成功获取锁
        ftruncate($lockFileHandle, 0);
        rewind($lockFileHandle);
        fwrite($lockFileHandle, getmypid());
        fflush($lockFileHandle);
        writeLog('INFO', '获取全局锁成功', ['pid' => getmypid()]);
        return true;
    }
    return false; // 默认返回失败
}

/**
 * 释放全局锁
 */
function releaseGlobalLock() {
    global $lockFileHandle;
    if ($lockFileHandle) {
        flock($lockFileHandle, LOCK_UN); // 释放锁
        fclose($lockFileHandle); // 关闭文件句柄
        $lockFileHandle = null;
        writeLog('INFO', '全局锁已释放');
    }
}

// 检查命令行参数
$isDaemonMode = in_array('--daemon', $argv ?? []);
$isDebugMode = in_array('--debug', $argv ?? []);
$isStatusMode = in_array('--status', $argv ?? []);
$isKillMode = in_array('--kill', $argv ?? []);
$isMultiProcess = in_array('--multi-process', $argv ?? []);

// 解析命令行参数
foreach ($argv ?? [] as $arg) {
    if (strpos($arg, '--months=') === 0) {
        $config['months_to_sync'] = (int)substr($arg, 9);
    } elseif (strpos($arg, '--processes=') === 0) {
        $config['max_processes'] = max(1, min(8, (int)substr($arg, 12)));
    }
}



// 自动检测宝塔守护进程环境
function detectBaotaEnvironment() {
    $ppid = function_exists('posix_getppid') ? posix_getppid() : 0;
    
    if ($ppid > 1) {
        return true;
    }
    
    if (strpos(__DIR__, '/www/wwwroot/') !== false) {
        return true;
    }
    
    return false;
}

// 设置模式
if ($isDaemonMode) {
    $config['daemon_mode'] = true;
    echo "启动守护进程模式...\n";
} elseif (detectBaotaEnvironment() && !$isStatusMode && !$isKillMode) {
    $config['bt_daemon_mode'] = true;
    $config['daemon_mode'] = true;
    echo "检测到宝塔环境，启动守护进程模式...\n";
}

if ($isMultiProcess) {
    $config['multi_process'] = true;
    echo "启用多进程模式，最大进程数: {$config['max_processes']}\n";
}

if ($isDebugMode) {
    $config['log_level'] = 'DEBUG';
    echo "启用调试模式，将输出详细日志...\n";
}

// 注册关机函数，确保锁被释放
register_shutdown_function('releaseGlobalLock');

// 尝试获取全局锁
if (!acquireGlobalLock()) {
    exit(1); // 无法获取锁，退出
}

// 全局变量存储子进程信息
$childProcesses = [];

// 注册信号处理器（如果支持的话）
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, 'signalHandler');
    pcntl_signal(SIGINT, 'signalHandler');
    pcntl_signal(SIGHUP, 'signalHandler');
    pcntl_signal(SIGCHLD, 'childSignalHandler');
}

/**
 * 信号处理函数
 */
function signalHandler($signal) {
    global $running, $childProcesses;
    
    writeLog('INFO', '接收到信号', ['signal' => $signal]);
    
    switch ($signal) {
        case SIGTERM:
        case SIGINT:
            writeLog('INFO', '准备优雅退出');
            $running = false;
            
            // 终止所有子进程
            foreach ($childProcesses as $pid => $info) {
                writeLog('INFO', '终止子进程', ['pid' => $pid]);
                posix_kill($pid, SIGTERM);
            }
            
            // 等待子进程退出
            $timeout = time() + 10; // 10秒超时
            while (!empty($childProcesses) && time() < $timeout) {
                pcntl_signal_dispatch();
                sleep(1);
            }
            
            // 强制杀死剩余子进程
            foreach ($childProcesses as $pid => $info) {
                writeLog('WARN', '强制终止子进程', ['pid' => $pid]);
                posix_kill($pid, SIGKILL);
            }
            releaseGlobalLock(); // 确保锁被释放
            exit(0);
            break;
        case SIGHUP:
            writeLog('INFO', '重新加载配置');
            // 这里可以添加重新加载配置的逻辑
            break;
    }
}

/**
 * 子进程信号处理函数
 */
function childSignalHandler($signal) {
    global $childProcesses;
    
    while (($pid = pcntl_waitpid(-1, $status, WNOHANG)) > 0) {
        if (isset($childProcesses[$pid])) {
            writeLog('INFO', '子进程退出', [
                'pid' => $pid,
                'exit_status' => $status,
                'chunk_index' => $childProcesses[$pid]['chunk_index']
            ]);
            unset($childProcesses[$pid]);
        }
    }
}

// 日志函数
function writeLog($level, $message, $data = null) {
    global $config;
    
    $levels = ['DEBUG' => 0, 'INFO' => 1, 'WARN' => 2, 'ERROR' => 3];
    $currentLevelValue = $levels[$config['log_level']] ?? 1;
    $messageLevelValue = $levels[$level] ?? 1;
    
    if ($messageLevelValue < $currentLevelValue) {
        return;
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message";
    
    if ($data) {
        $logEntry .= ' | Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($config['log_file'], $logEntry . "\n", FILE_APPEND | LOCK_EX);
    echo $logEntry . "\n";
    
    // 日志轮转 - 保留最近7天
    if (rand(1, 100) === 1) {
        cleanOldLogs();
    }
}

// 清理旧日志
function cleanOldLogs() {
    global $logDir;

    
    $files = glob($logDir . '/*.log');
    foreach ($files as $file) {
        if (filemtime($file) < time() - (7 * 24 * 3600)) {
            unlink($file);
        }
    }
}

// 全局API调用间隔控制
function waitForApiInterval() {
    global $config, $logDir;
    
    $apiTimeFile = $logDir . '/api_last_call_finance.time';
    
    if (file_exists($apiTimeFile)) {
        $lastCallTime = (int)file_get_contents($apiTimeFile);
        $timeSinceLastCall = time() - $lastCallTime;
        
        if ($timeSinceLastCall < $config['api_delay']) {
            $waitTime = $config['api_delay'] - $timeSinceLastCall;
            writeLog('DEBUG', "API调用间隔控制，等待 {$waitTime} 秒");
            sleep($waitTime);
        }
    }
    
    file_put_contents($apiTimeFile, time());
}

/**
 * 确保数据库表结构存在
 */
function ensureDatabaseTables() {
    global $DB;
    
    // 检查t_order_profit表是否存在operation_ids字段，如果不存在则添加
    $checkColumnSQL = "SHOW COLUMNS FROM t_order_profit LIKE 'operation_ids'";
    $columnExists = $DB->getRow($checkColumnSQL);
    
    if (!$columnExists) {
        $addColumnSQL = "ALTER TABLE t_order_profit ADD COLUMN operation_ids TEXT COMMENT '已处理的操作ID列表，逗号分隔' AFTER return_amount";
        $result = $DB->query($addColumnSQL);
        if (!$result) {
            writeLog('ERROR', '添加operation_ids字段失败', ['error' => $DB->error()]);
            throw new Exception('数据库表结构更新失败');
        }
        writeLog('INFO', '成功添加operation_ids字段到t_order_profit表');
    }
    
    writeLog('DEBUG', '数据库表结构检查完成');
}

/**
 * 主要同步执行函数
 */
function executeSync() {
    global $config, $DB;
    
    $startTime = time();
    writeLog('INFO', '开始财务数据同步任务', ['months_to_sync' => $config['months_to_sync'], 'multi_process' => $config['multi_process']]);
    
    try {
        // 确保数据库表结构存在
        ensureDatabaseTables();
        
        // 获取所有状态正常的店铺
        $stores = $DB->getAll("SELECT * FROM ozon_store WHERE apistatus = 1");
        
        if ($stores === false) {
            throw new Exception('查询店铺数据失败');
        }
        
        if (!is_array($stores)) {
            $stores = [];
        }
        
        if (empty($stores)) {
            writeLog('WARN', '没有找到状态正常的店铺(apistatus=1)');
            return;
        }
        
        writeLog('INFO', '找到状态正常的店铺', ['count' => count($stores)]);
        
        // 根据配置选择处理模式
        if ($config['multi_process'] && function_exists('pcntl_fork')) {
            $totalStats = executeMultiProcessSync($stores, $startTime);
        } else {
            if ($config['multi_process']) {
                writeLog('WARN', 'pcntl扩展不可用，回退到单进程模式');
            }
            $totalStats = executeSingleProcessSync($stores, $startTime);
        }
        
        $duration = time() - $startTime;
        writeLog('INFO', '财务数据同步任务执行完成', array_merge($totalStats, ['duration_seconds' => $duration]));
        
    } catch (Exception $e) {
        writeLog('ERROR', '财务数据同步任务执行失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    }
}

/**
 * 单进程同步执行
 */
function executeSingleProcessSync($stores, $startTime) {
    global $config;
    
    $totalStats = [
        'stores_processed' => 0,
        'total_transactions' => 0,
        'transactions_inserted' => 0,
        'transactions_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    // 处理每个店铺
    foreach ($stores as $store) {
        if (time() - $startTime > $config['max_execution_time']) {
            writeLog('WARN', '达到最大执行时间，停止处理');
            break;
        }
        
        $storeStats = processSingleStore($store);
        
        // 累计统计
        foreach ($totalStats as $key => $value) {
            $totalStats[$key] += $storeStats[$key] ?? 0;
        }
    }
    
    return $totalStats;
}

/**
 * 多进程同步执行
 */
function executeMultiProcessSync($stores, $startTime) {
    global $config, $logDir, $childProcesses;
    
    writeLog('INFO', '启动多进程同步', ['max_processes' => $config['max_processes'], 'stores_count' => count($stores)]);
    
    $totalStats = [
        'stores_processed' => 0,
        'total_transactions' => 0,
        'transactions_inserted' => 0,
        'transactions_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    // 将店铺分组分配给不同进程
    $storeChunks = array_chunk($stores, ceil(count($stores) / $config['max_processes']));
    $resultFiles = [];
    
    // 启动子进程
    foreach ($storeChunks as $index => $chunk) {
        if (empty($chunk)) continue;
        
        $resultFile = $logDir . "/process_result_{$index}_" . time() . ".json";
        $resultFiles[] = $resultFile;
        
        $pid = pcntl_fork();
        
        if ($pid == -1) {
            writeLog('ERROR', '无法创建子进程', ['chunk_index' => $index]);
            continue;
        } elseif ($pid == 0) {
            // 子进程 - 重置信号处理
            pcntl_signal(SIGTERM, SIG_DFL);
            pcntl_signal(SIGINT, SIG_DFL);
            pcntl_signal(SIGHUP, SIG_DFL);
            pcntl_signal(SIGCHLD, SIG_DFL);
            
            processStoreChunk($chunk, $resultFile, $index);
            exit(0);
        } else {
            // 父进程 - 记录子进程信息
            $childProcesses[$pid] = [
                'chunk_index' => $index,
                'start_time' => time(),
                'result_file' => $resultFile,
                'store_count' => count($chunk)
            ];
            writeLog('INFO', '启动子进程', ['pid' => $pid, 'chunk_index' => $index, 'stores' => count($chunk)]);
        }
    }
    
    // 等待所有子进程完成
    $completedProcesses = 0;
    $totalProcesses = count($childProcesses);
    
    while ($completedProcesses < $totalProcesses) {
        // 处理信号
        pcntl_signal_dispatch();
        
        $status = 0;
        $pid = pcntl_wait($status, WNOHANG);
        
        if ($pid > 0) {
            if (isset($childProcesses[$pid])) {
                $process = $childProcesses[$pid];
                $duration = time() - $process['start_time'];
                
                writeLog('INFO', '子进程完成', [
                    'pid' => $pid,
                    'chunk_index' => $process['chunk_index'],
                    'duration' => $duration,
                    'exit_status' => $status
                ]);
                
                // 读取子进程结果
                if (file_exists($process['result_file'])) {
                    $result = json_decode(file_get_contents($process['result_file']), true);
                    if ($result) {
                        foreach ($totalStats as $key => $value) {
                            $totalStats[$key] += $result[$key] ?? 0;
                        }
                    }
                    unlink($process['result_file']);
                }
                
                unset($childProcesses[$pid]);
                $completedProcesses++;
            }
        } elseif ($pid == 0) {
            // 检查超时的进程
            foreach ($childProcesses as $checkPid => $process) {
                if (time() - $process['start_time'] > $config['process_timeout']) {
                    writeLog('WARN', '子进程超时，强制终止', ['pid' => $checkPid, 'timeout' => $config['process_timeout']]);
                    posix_kill($checkPid, SIGTERM);
                    sleep(2);
                    if (posix_kill($checkPid, 0)) { // 检查进程是否还存在
                        posix_kill($checkPid, SIGKILL);
                    }
                    unset($childProcesses[$checkPid]);
                    $completedProcesses++;
                }
            }
            usleep(100000); // 100ms
        } else {
            // 等待出错
            usleep(100000); // 100ms
        }
        
        // 检查总体超时
        if (time() - $startTime > $config['max_execution_time']) {
            writeLog('WARN', '达到最大执行时间，终止所有子进程');
            foreach ($childProcesses as $checkPid => $process) {
                posix_kill($checkPid, SIGTERM);
            }
            sleep(2);
            foreach ($childProcesses as $checkPid => $process) {
                if (posix_kill($checkPid, 0)) { // 检查进程是否还存在
                    posix_kill($checkPid, SIGKILL);
                }
            }
            break;
        }
    }
    
    // 清理剩余的结果文件
    foreach ($resultFiles as $file) {
        if (file_exists($file)) {
            unlink($file);
        }
    }
    
    return $totalStats;
}

/**
 * 子进程处理店铺块
 */
function processStoreChunk($stores, $resultFile, $chunkIndex) {
    global $config, $DB;
    
    // 重新连接数据库（子进程需要独立连接）
    try {
        require_once __DIR__ . '/../includes/common.php';
    } catch (Exception $e) {
        writeLog('ERROR', '子进程数据库连接失败', ['chunk_index' => $chunkIndex, 'error' => $e->getMessage()]);
        file_put_contents($resultFile, json_encode(['errors' => 1]));
        return;
    }
    
    $stats = [
        'stores_processed' => 0,
        'total_transactions' => 0,
        'transactions_inserted' => 0,
        'transactions_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    writeLog('INFO', '子进程开始处理店铺块', ['chunk_index' => $chunkIndex, 'stores_count' => count($stores)]);
    
    foreach ($stores as $store) {
        try {
            $storeStats = processSingleStore($store);
            
            // 累计统计
            foreach ($stats as $key => $value) {
                $stats[$key] += $storeStats[$key] ?? 0;
            }
            
        } catch (Exception $e) {
            writeLog('ERROR', '子进程处理店铺失败', [
                'chunk_index' => $chunkIndex,
                'store_id' => $store['id'],
                'error' => $e->getMessage()
            ]);
            $stats['errors']++;
        }
    }
    
    // 保存结果到文件
    file_put_contents($resultFile, json_encode($stats));
    writeLog('INFO', '子进程完成店铺块处理', array_merge(['chunk_index' => $chunkIndex], $stats));
}

/**
 * 处理单个店铺的财务数据
 */
function processSingleStore($store) {
    global $config, $DB;
    
    $storeStartTime = time();
    writeLog('INFO', "开始处理店铺财务数据: {$store['storename']}", ['store_id' => $store['id'], 'client_id' => $store['ClientId']]);
    
    $stats = [
        'stores_processed' => 1,
        'total_transactions' => 0,
        'transactions_inserted' => 0,
        'transactions_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    try {
        // 创建API客户端
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        
        // 获取最近N个月的数据
        $endDate = new DateTime();
        $startDate = clone $endDate;
        $startDate->sub(new DateInterval('P' . $config['months_to_sync'] . 'M'));
        
        writeLog('INFO', "获取财务数据时间范围", [
            'store_id' => $store['id'],
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'months' => $config['months_to_sync']
        ]);
        
        // 按月份分批获取数据（API限制每次最多1个月）
        $currentDate = clone $startDate;
        
        while ($currentDate < $endDate) {
            $monthStart = clone $currentDate;
            $monthEnd = clone $currentDate;
            $monthEnd->add(new DateInterval('P1M'));
            
            // 确保不超过结束日期
            if ($monthEnd > $endDate) {
                $monthEnd = clone $endDate;
            }
            
            $monthStats = processMonthData($client, $store, $monthStart, $monthEnd);
            
            // 累计统计
            foreach ($stats as $key => $value) {
                if (isset($monthStats[$key]) && is_numeric($value)) {
                    $stats[$key] += $monthStats[$key];
                }
            }
            
            $currentDate->add(new DateInterval('P1M'));
            
            // API调用间隔
            waitForApiInterval();
        }
        
        $duration = time() - $storeStartTime;
        writeLog('INFO', "店铺财务数据处理完成: {$store['storename']}", array_merge($stats, ['duration_seconds' => $duration]));
        
    } catch (Exception $e) {
        writeLog('ERROR', "店铺财务数据处理失败: {$store['storename']}", ['error' => $e->getMessage()]);
        $stats['errors']++;
    }
    
    return $stats;
}

/**
 * 处理单个月份的财务数据
 */
function processMonthData($client, $store, $startDate, $endDate) {
    global $config, $DB;
    
    $stats = [
        'total_transactions' => 0,
        'transactions_inserted' => 0,
        'transactions_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    try {
        writeLog('INFO', "处理月份数据", [
            'store_id' => $store['id'],
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d')
        ]);
        
        $page = 1;
        $hasMoreData = true;
        
        while ($hasMoreData) {
            // 构建API请求参数
            $requestData = [
                'filter' => [
                    'date' => [
                        'from' => $startDate->format('Y-m-d\TH:i:s.000\Z'),
                        'to' => $endDate->format('Y-m-d\TH:i:s.000\Z')
                    ],
                    'transaction_type' => 'all'
                ],
                'page' => $page,
                'page_size' => $config['page_size']
            ];
            
            writeLog('DEBUG', "调用财务交易API", [
                'store_id' => $store['id'],
                'page' => $page,
                'request_data' => $requestData
            ]);
            
            // 调用API
            $response = $client->getFinanceTransactions($requestData);
            $stats['api_calls']++;
            
            if (!$response || !isset($response['result'])) {
                writeLog('ERROR', "API调用失败或返回数据格式错误", [
                    'store_id' => $store['id'],
                    'page' => $page,
                    'response' => $response
                ]);
                $stats['errors']++;
                break;
            }
            
            $result = $response['result'];
            $operations = $result['operations'] ?? [];
            $pageCount = $result['page_count'] ?? 1;
            
            writeLog('INFO', "API返回数据", [
                'store_id' => $store['id'],
                'page' => $page,
                'operations_count' => count($operations),
                'page_count' => $pageCount
            ]);
            
            // 处理交易数据
            foreach ($operations as $operation) {
                $processResult = processTransaction($operation, $store);
                if ($processResult['success']) {
                    if ($processResult['action'] === 'insert') {
                        $stats['transactions_inserted']++;
                    } else {
                        $stats['transactions_updated']++;
                    }
                } else {
                    $stats['errors']++;
                }
                $stats['total_transactions']++;
            }
            
            // 检查是否还有更多数据
            if ($page >= $pageCount || empty($operations)) {
                $hasMoreData = false;
            } else {
                $page++;
            }
        }
        
    } catch (Exception $e) {
        writeLog('ERROR', "处理月份数据失败", [
            'store_id' => $store['id'],
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'error' => $e->getMessage()
        ]);
        $stats['errors']++;
    }
    
    return $stats;
}

/**
 * 处理单个交易记录 - 按订单号累加各种费用
 */
function processTransaction($operation, $store) {
    global $DB;
    
    try {
        // 提取基本信息
        $postingNumber = $operation['posting']['posting_number'] ?? '';
        $orderDate = $operation['posting']['order_date'] ?? '';
        $operationType = $operation['operation_type'] ?? '';
        $operationId = $operation['operation_id'] ?? '';
        $amount = (float)($operation['amount'] ?? 0);
        
        // 获取店铺ID和用户ID
        $storeId = $store['id'] ?? null;
        $uid = $store['uid'] ?? null;
        
        if (empty($postingNumber)) {
            writeLog('WARN', "交易记录缺少订单号", ['operation_id' => $operationId]);
            return ['success' => false, 'action' => 'skip'];
        }
        
        // 方案1: 基于现有t_order_profit表添加operation_ids字段进行去重
        // 检查operation_id是否已处理过，防止重复处理
        if (!empty($operationId)) {
            // 检查当前订单的operation_ids字段中是否已包含此operation_id
            $existingRecord = $DB->getRow(
                "SELECT operation_ids FROM t_order_profit WHERE posting_number = ?",
                [$postingNumber]
            );
            
            if ($existingRecord && !empty($existingRecord['operation_ids'])) {
                $processedOperations = explode(',', $existingRecord['operation_ids']);
                if (in_array($operationId, $processedOperations)) {
                    writeLog('DEBUG', "操作记录已存在，跳过处理", ['operation_id' => $operationId, 'posting_number' => $postingNumber]);
                    return ['success' => true, 'action' => 'skip_duplicate'];
                }
            }
        }
        
        // 获取商品信息
        $items = $operation['items'] ?? [];
        $itemNames = [];
        $skus = [];
        
        foreach ($items as $item) {
            if (!empty($item['name'])) {
                $itemNames[] = $item['name'];
            }
            if (!empty($item['sku'])) {
                $skus[] = $item['sku'];
            }
        }
        
        $itemName = implode(', ', $itemNames);
        $sku = !empty($skus) ? $skus[0] : null; // 取第一个SKU
        
        // 解析日期
        $orderDateTime = null;
        if (!empty($orderDate)) {
            try {
                $orderDateTime = new DateTime($orderDate);
            } catch (Exception $e) {
                writeLog('WARN', "订单日期格式解析失败", ['order_date' => $orderDate, 'posting_number' => $postingNumber]);
            }
        }
        
        // 根据操作类型分类金额
        $amounts = categorizeAmount($operationType, $amount, $operation);
        
        // 检查订单记录是否已存在
        $existingRecord = $DB->getRow(
            "SELECT * FROM t_order_profit WHERE posting_number = ?",
            [$postingNumber]
        );
        
        if ($existingRecord) {
            // 更新现有记录 - 累加金额
            $updateData = [
                'income' => $existingRecord['income'] + $amounts['income'],
                'commission' => $existingRecord['commission'] + $amounts['commission'],
                'agency_fee' => $existingRecord['agency_fee'] + $amounts['agency_fee'],
                'delivery_fee' => $existingRecord['delivery_fee'] + $amounts['delivery_fee'],
                'acquiring_fee' => $existingRecord['acquiring_fee'] + $amounts['acquiring_fee'],
                'return_amount' => $existingRecord['return_amount'] + $amounts['return_amount']
            ];
            
            // 更新operation_ids字段，添加新的operation_id
            if (!empty($operationId)) {
                $existingOperationIds = !empty($existingRecord['operation_ids']) ? $existingRecord['operation_ids'] : '';
                $operationIdsList = !empty($existingOperationIds) ? explode(',', $existingOperationIds) : [];
                if (!in_array($operationId, $operationIdsList)) {
                    $operationIdsList[] = $operationId;
                    $updateData['operation_ids'] = implode(',', $operationIdsList);
                }
            }

            // 如果记录中缺少商品信息，则更新
            if (empty($existingRecord['item_name']) && !empty($itemName)) {
                $updateData['item_name'] = $itemName;
            }
            if (empty($existingRecord['sku']) && !empty($sku)) {
                $updateData['sku'] = $sku;
            }
            
            // 如果记录中缺少店铺ID或用户ID，则更新
            if (empty($existingRecord['shop_id']) && !empty($storeId)) {
                $updateData['shop_id'] = $storeId;
            }
            if (empty($existingRecord['uid']) && !empty($uid)) {
                $updateData['uid'] = $uid;
            }
            
            $updateFields = [];
            $updateValues = [];
            foreach ($updateData as $field => $value) {
                $updateFields[] = "`{$field}` = ?";
                $updateValues[] = $value;
            }
            $updateValues[] = $postingNumber;
            
            $sql = "UPDATE t_order_profit SET " . implode(', ', $updateFields) . " WHERE posting_number = ?";
            $stmt = $DB->query($sql, $updateValues);
            
            if ($stmt) {
                writeLog('DEBUG', "更新订单记录成功", ['posting_number' => $postingNumber, 'operation_type' => $operationType, 'amount' => $amount]);
                return ['success' => true, 'action' => 'update'];
            } else {
                writeLog('ERROR', "更新订单记录失败", ['posting_number' => $postingNumber, 'error' => $DB->error()]);
                return ['success' => false, 'action' => 'update_failed'];
            }
        } else {
            // 插入新记录
            $data = [
                'posting_number' => $postingNumber,
                'shop_id' => $storeId,
                'uid' => $uid,
                'order_date' => $orderDateTime ? $orderDateTime->format('Y-m-d H:i:s') : null,
                'item_name' => $itemName,
                'sku' => $sku,
                'income' => $amounts['income'],
                'commission' => $amounts['commission'],
                'agency_fee' => $amounts['agency_fee'],
                'delivery_fee' => $amounts['delivery_fee'],
                'acquiring_fee' => $amounts['acquiring_fee'],
                'return_amount' => $amounts['return_amount'],
                'operation_ids' => !empty($operationId) ? $operationId : ''
            ];
            
            $fields = array_keys($data);
            $placeholders = array_fill(0, count($fields), '?');
            $values = array_values($data);
            
            $sql = "INSERT INTO t_order_profit (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
            $stmt = $DB->query($sql, $values);
            
            if ($stmt) {
                writeLog('DEBUG', "插入订单记录成功", ['posting_number' => $postingNumber, 'operation_type' => $operationType, 'amount' => $amount]);
                return ['success' => true, 'action' => 'insert'];
            } else {
                writeLog('ERROR', "插入订单记录失败", ['posting_number' => $postingNumber, 'error' => $DB->error()]);
                return ['success' => false, 'action' => 'insert_failed'];
            }
        }
        
    } catch (Exception $e) {
        writeLog('ERROR', "处理交易记录异常", [
            'posting_number' => $operation['posting']['posting_number'] ?? 'unknown',
            'operation_id' => $operation['operation_id'] ?? 'unknown',
            'error' => $e->getMessage()
        ]);
        return ['success' => false, 'action' => 'exception'];
    }
}

/**
 * 根据操作类型分类金额
 */
function categorizeAmount($operationType, $amount, $operation = null) {
    $amounts = [
        'income' => 0,
        'commission' => 0,
        'agency_fee' => 0,
        'delivery_fee' => 0,
        'acquiring_fee' => 0,
        'return_amount' => 0
    ];
    
    // 根据操作类型分类
    switch ($operationType) {
        // 收入类型 - 客户签收金额
        case 'OperationAgentDeliveredToCustomer':
            // 优先使用更详细的字段（如果存在）
            if (isset($operation['accruals_for_sale'])) {
                $amounts['income'] = $operation['accruals_for_sale'];
                $amounts['commission'] = $operation['sale_commission'] ?? 0;
            } elseif ($amount > 0) {
                $amounts['income'] = $amount;
            }
            break;
            
        // 销售佣金 - 负数表示费用
        case 'MarketplaceServiceItemFulfillment':
        case 'MarketplaceServiceItemDirectFlowTrans':
        case 'MarketplaceServiceItemDelivToCustomer':
        case 'MarketplaceServicePremiumCashback':
            $amounts['commission'] = $amount; // 保持原始值（通常为负数）
            break;
            
        // 国际物流代理佣金
        case 'OperationMarketplaceAgencyFeeAggregator3PLGlobal':
        case 'MarketplaceServiceItemDirectFlowLogistic':
            $amounts['agency_fee'] = $amount; // 保持原始值（通常为负数）
            break;
            
        // 物流分摊费用
        case 'MarketplaceRedistributionOfDeliveryServicesOperation':
        case 'MarketplaceServiceItemDropoffPVZ':
        case 'MarketplaceServiceItemDropoffSC':
        case 'MarketplaceServiceItemDropoffFF':
            $amounts['delivery_fee'] = $amount; // 保持原始值（通常为负数）
            break;
            
        // 支付手续费
        case 'MarketplaceRedistributionOfAcquiringOperation':
            $amounts['acquiring_fee'] = $amount; // 保持原始值（通常为负数）
            break;
            
        // 退货/退款金额
        case 'ClientReturnAgentOperation':
        case 'ClientReturnAgentOperationPVZ':
        case 'OperationReturnGoodsFBSofRMS':
        case 'MarketplaceServiceItemReturnFlowTrans':
            $amounts['return_amount'] = $amount; // 保持原始值（通常为负数）
            break;
            
        // 其他未分类的操作
        default:
            if ($amount > 0) {
                $amounts['income'] = $amount;
            } else {
                // 负数金额归类为佣金费用
                $amounts['commission'] = $amount;
            }
            writeLog('DEBUG', "未知操作类型", [
                'operation_type' => $operationType,
                'amount' => $amount
            ]);
            break;
    }
    
    return $amounts;
}



/**
 * 守护进程循环
 */
function daemonLoop() {
    global $config, $running;
    
    writeLog('INFO', '启动财务数据同步守护进程', [
        'sync_interval' => $config['sync_interval'],
        'multi_process' => $config['multi_process'],
        'max_processes' => $config['max_processes']
    ]);
    
    while ($running) {
        try {
            executeSync();
            
            if ($running) {
                writeLog('INFO', "财务数据同步完成，等待下次执行", ['next_sync_in_seconds' => $config['sync_interval']]);
                sleep($config['sync_interval']);
            }
            
        } catch (Exception $e) {
            writeLog('ERROR', '守护进程执行异常', ['error' => $e->getMessage()]);
            sleep(60); // 出错时等待1分钟再重试
        }
        
        // 处理信号
        if (function_exists('pcntl_signal_dispatch')) {
            pcntl_signal_dispatch();
        }
    }
    
    writeLog('INFO', '财务数据同步守护进程正常退出');
}



// 读取文件的最后几行
function tail($filename, $lines = 10) {
    if (!file_exists($filename)) {
        return "文件不存在\n";
    }
    
    $handle = fopen($filename, "r");
    if (!$handle) {
        return "无法读取文件\n";
    }
    
    $result = [];
    while (($line = fgets($handle)) !== false) {
        $result[] = $line;
        if (count($result) > $lines) {
            array_shift($result);
        }
    }
    
    fclose($handle);
    return implode('', $result);
}


// 主程序入口
try {
    writeLog('INFO', '程序启动', [
        'daemon_mode' => $config['daemon_mode'],
        'multi_process' => $config['multi_process'],
        'max_processes' => $config['max_processes'],
        'months_to_sync' => $config['months_to_sync']
    ]);

    if ($config['daemon_mode']) {
        // 守护进程循环
        while ($running) {
            executeSync();
            $sleepTime = $config['sync_interval'];
            writeLog('INFO', "任务周期完成，休眠 {$sleepTime} 秒");
            sleep($sleepTime);
        }
    } else {
        // 单次执行
        executeSync();
    }
} catch (Exception $e) {
    writeLog('ERROR', '程序执行失败', ['error' => $e->getMessage()]);
    exit(1);
}

writeLog('INFO', '财务数据同步程序执行完成');
?>
<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义日志目录
define('LOG_DIR', __DIR__ . '/logs/');
if (!is_dir(LOG_DIR)) {
    mkdir(LOG_DIR, 0755, true);
}

require_once __DIR__ . '/../includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 日志函数
function log_message($message, $level = 'INFO', $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] [{$level}] {$message}";
    
    if (!empty($context)) {
        $logEntry .= " " . json_encode($context, JSON_UNESCAPED_UNICODE);
    }
    
    $logEntry .= "\n";
    
    // 输出到控制台
    echo $logEntry;
    
    // 同时写入日志文件
    $logFile = LOG_DIR . 'productsync_worker_' . date('Ymd') . '.log';
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    log_message("正在连接到 RabbitMQ...");
    $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
    $channel = $connection->channel();
    $channel->queue_declare('productsync', false, true, false, false);
    log_message("RabbitMQ 连接成功");
} catch (Exception $e) {
    log_message("RabbitMQ 连接失败: " . $e->getMessage(), 'ERROR');
    die("连接失败: " . $e->getMessage());
}

echo " [*] 等待消息. 按 CTRL+C 退出\n";

// 定义回调函数
$callback = function ($msg) {
    $data = json_decode($msg->body, true);
    $logContext = ['message_id' => $msg->delivery_info['delivery_tag'], 'data' => $data];
    
    //log_message("收到消息", 'INFO', $logContext);
    
    try {
        // 根据消息类型处理
        if (isset($data['type']) && $data['type'] === 'productsync') {
            log_message("开始处理商品同步消息", 'INFO');
            $result = productsyncssss($data['id']);
            
            if ($result['status'] === 'success') {
                log_message("{$data['id']}商品同步成功: " . $result['message'], 'SUCCESS');
                $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
            } else {
                log_message("{$data['id']}商品同步失败: " . $result['message'], 'ERROR');
                $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag'], false, false);
            }
        } 
        // 处理旧格式的消息（只有店铺数据，没有type字段）
        elseif (isset($data['id']) && isset($data['ClientId'])) {
            log_message("{$data['ClientId']}处理旧格式店铺同步消息", 'INFO');
            $result = productsyncssss($data['id']);
            
            if ($result['status'] === 'success') {
                log_message("{$data['id']}商品同步成功: " . $result['message'], 'SUCCESS');
                $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
            } else {
                log_message("{$data['id']}商品同步失败: " . $result['message'], 'ERROR');
                $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag'], false, false);
            }
        }
        else {
            log_message("未知消息格式，进入默认处理", 'WARNING', $data);
            processSlowTask($data);
            $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
        }
    } catch (Exception $e) {
        log_message("处理消息时发生异常: " . $e->getMessage(), 'ERROR', $logContext);
        $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag'], false, false);
    }
};

// 公平分发
$channel->basic_qos(null, 1, null);

// 订阅队列
$channel->basic_consume('productsync', '', false, false, false, false, $callback);

log_message("Worker 启动成功，开始监听消息...");

// 保持监听
try {
    while (count($channel->callbacks)) {
        $channel->wait();
    }
} catch (Exception $e) {
    log_message("Worker 异常退出: " . $e->getMessage(), 'ERROR');
} finally {
    $channel->close();
    $connection->close();
    log_message("Worker 正常关闭");
}

function processSlowTask($data) {
    log_message("处理慢任务", 'INFO', $data);
    // 这里可以添加其他处理逻辑
}

function productsyncssss($id) {
    global $DB, $dbconfig;
    $messageId = uniqid();
    log_message("[{$messageId}]开始商品同步", 'INFO');
    
    set_time_limit(3600);
    ini_set('memory_limit', '512M');
    
    try {
        $DB = new \lib\PdoHelper($dbconfig);
        $redis = new Redis();
        
        log_message("[{$messageId}]连接Redis ID".$id, 'INFO');
        if (!$redis->connect('127.0.0.1', 6379, 2)) {
            throw new Exception("无法连接到Redis服务器");
        }
        
        // 获取店铺信息
        $row = $DB->find('store', '*', ['id' => $id]);
        if (!$row) {
            throw new Exception("未找到店铺信息");
        }
        
        log_message("[{$messageId}]找到店铺信息", 'INFO');
        
        // 设置锁 - 防止重复同步
        $lockKey = 'productsync_store_' . $id;
        if ($redis->get($lockKey)) {
            throw new Exception("[{$messageId}]该店铺正在同步中，请稍后再试");
        }
        $redis->setex($lockKey, 86400, 1);
        
        $importer = new \lib\JsonImporter($DB);
        $lastId = null;
        $totalImported = 0;
        $limit = 1000;
        $page = 1;
        
        do {
            log_message("[{$messageId}]获取第 {$page} 页产品列表", 'INFO');
            
            $requestData = [
                'filter' => ['visibility' => 'ALL'],
                'limit' => $limit
            ];
            
            if ($lastId) {
                $requestData['last_id'] = $lastId;
            }
            
            $listResponse = ozonapicurl(
                ['ClientId' => $row['ClientId'], 'key' => $row['key']],
                'https://api-seller.ozon.ru/v3/product/list',
                'POST',
                $requestData,
                30
            );
            
            if (!$listResponse) {
                throw new Exception("获取产品列表API返回空响应");
            }
            $listData = json_decode($listResponse, true);
            
            if (isset($listData['error'])) {
                throw new Exception("获取产品列表失败: " . ($listData['message'] ?? '未知错误'));
            }
            
            if (empty($listData['result']['items'])) {
                log_message("[{$messageId}]没有更多产品需要同步", 'INFO');
                break;
            }
            
            $productIds = array_column($listData['result']['items'], 'product_id');
            log_message("[{$messageId}]获取到产品列表", 'INFO');
            
            // 获取产品详情
            log_message("[{$messageId}]获取产品详情", 'INFO');
            $detailsResponse = ozonapicurl(
                ['ClientId' => $row['ClientId'], 'key' => $row['key']],
                'https://api-seller.ozon.ru/v3/product/info/list',
                'POST',
                ['product_id' => $productIds],
                30
            );
            
            if (!$detailsResponse) {
                throw new Exception("获取产品详情API返回空响应");
            }
            
            $detailsData = json_decode($detailsResponse, true);
            if (isset($detailsData['error'])) {
                throw new Exception("获取产品详情失败: " . ($detailsData['message'] ?? '未知错误'));
            }
            
            // 导入产品
            log_message("[{$messageId}]开始导入产品", 'INFO');
            $importResult = $importer->importProducts($detailsData, false, $row);
            
            if (count($importResult['errors'])===0) {
                $count = count($listData['result']['items']);
                $totalImported += $count;
                log_message("[{$messageId}]成功导入批{$page}次{$count}商品", 'SUCCESS');
            } else {
                log_message("[{$messageId}]导入产品失败", 'ERROR');
            }
            
            $lastId = $listData['result']['last_id'] ?? null;
            $page++;
        } while ($lastId !== null);
        
        $redis->del($lockKey);
        log_message("[{$messageId}]同步完成，共导入 {$totalImported} 个商品", 'SUCCESS');
        
        return ['status' => 'success', 'message' => "[{$messageId}]同步完成，共导入 {$totalImported} 个商品"];
        
    } catch (Exception $e) {
        if (isset($lockKey) && isset($redis)) {
            $redis->del($lockKey);
        }
        log_message("同步失败", 'ERROR', ['error' => $e->getMessage()]);
        return ['status' => 'error', 'message' => $e->getMessage()];
    } finally {
        if (isset($redis)) {
            $redis->close();
        }
    }
}
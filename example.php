<?php

function addWatermark($imageUrls, $text, $position = 'bottom-right', $scale = 1.0) {
    $apiUrl = 'http://localhost:8000/watermark';
    
    $data = [
        'image_urls' => $imageUrls,
        'text' => $text,
        'position' => $position,
        'scale' => $scale
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($apiUrl, false, $context);
    
    if ($result === FALSE) {
        throw new Exception('调用水印服务失败');
    }
    
    return json_decode($result, true);
}

// 基本测试
echo "开始水印服务基本测试...\n";

try {
    // 测试图片URL（使用可靠的测试图片）
    $imageUrls = [
        'https://picsum.photos/800/600',  // 随机测试图片1
        'https://picsum.photos/1024/768'  // 随机测试图片2
    ];
    
    echo "测试图片URL:\n";
    foreach ($imageUrls as $url) {
        echo "- $url\n";
    }
    
    // 调用水印服务
    echo "\n正在添加水印...\n";
    $result = addWatermark(
        $imageUrls,
        'test123',
        'bottom-right',
        1.0
    );
    
    // 输出结果
    echo "\n处理成功！\n";
    echo "处理后的图片路径：\n";
    foreach ($result['processed_paths'] as $path) {
        echo "- $path\n";
    }
    
} catch (Exception $e) {
    echo "\n错误：" . $e->getMessage() . "\n";
}

echo "\n测试完成。\n"; 
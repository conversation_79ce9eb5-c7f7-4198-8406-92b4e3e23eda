<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 内存和性能设置
ini_set('memory_limit', '512M');
set_time_limit(0);

class SafeConsumer {
    private $connection;
    private $channel;
    private $maxRetries = 3;
    private $logger;
    private $config;
    private $db;
    
    public function __construct($config,$DB) {
        $this->config = $config;
        $this->logger = new ConsumerLogger();
        $this->connectRabbitMQ();
        $this->db = $DB; // 假设有一个数据库封装类
    }
    
    private function connectRabbitMQ() {
        try {
            $this->connection = new AMQPStreamConnection(
                $this->config['host'],
                $this->config['port'],
                $this->config['user'],
                $this->config['pwd'],
                '/',
                false,
                'AMQPLAIN',
                null,
                'en_US',
                60,  // 连接超时
                30,  // 读写超时
                null,
                true // 保持连接
            );
            
            $this->channel = $this->connection->channel();
            $this->channel->queue_declare('task_queue', false, true, false, false);
            $this->channel->basic_qos(null, 1, null);
            
        } catch (Exception $e) {
            $this->logger->error("RabbitMQ连接失败: ".$e->getMessage());
            throw $e;
        }
    }
    
    public function consume() {
        $callback = function ($msg) {
            $startTime = microtime(true);
            $messageId = uniqid();
            
            try {
                $this->logger->info("[$messageId] 开始处理消息");
                
                $data = $this->parseMessage($msg->body);
                $this->logger->info("[$messageId] 收到 {$data['type']} ID: ".($data['id']??$data['sku']));
                
                $this->processMessageWithRetry($messageId, $data);
                
                $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
                
                $duration = round(microtime(true) - $startTime, 2);
                $this->logger->info("[$messageId] 处理完成 耗时: {$duration}s");
                
            } catch (InvalidMessageException $e) {
                $this->logger->error("[$messageId] 消息格式错误: ".$e->getMessage());
                $msg->delivery_info['channel']->basic_reject($msg->delivery_info['delivery_tag'], false);
                
            } catch (RecoverableException $e) {
                $this->logger->error("[$messageId] 可重试错误: ".$e->getMessage());
                $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag']);
                
            } catch (Exception $e) {
                $this->logger->error("[$messageId] 不可恢复错误: ".$e->getMessage());
                $msg->delivery_info['channel']->basic_reject($msg->delivery_info['delivery_tag'], false);
            }
            
            $this->checkResourceUsage($messageId);
        };
        
        $this->channel->basic_consume('task_queue', '', false, false, false, false, $callback);
        
        $this->logger->info("消费者启动，等待消息...");
        
        while (count($this->channel->callbacks)) {
            $this->channel->wait();
        }
    }
    
    private function parseMessage($body) {
        $data = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidMessageException("无效的JSON数据");
        }
        
        if (empty($data['type'])) {
            throw new InvalidMessageException("消息缺少type字段");
        }
        
        return $data;
    }
    
    private function processMessageWithRetry($messageId, $data) {
        $retryCount = 0;
        $lastError = null;
        
        while ($retryCount < $this->maxRetries) {
            try {
                $this->processMessage($data);
                return;
                
            } catch (RecoverableException $e) {
                $retryCount++;
                $lastError = $e;
                $waitTime = pow(2, $retryCount); // 指数退避
                $this->logger->warning("[$messageId] 尝试 {$retryCount}/{$this->maxRetries} 失败，等待 {$waitTime}s 后重试");
                sleep($waitTime);
            }
        }
        
        throw new Exception("重试 {$this->maxRetries} 次后仍然失败: ".$lastError->getMessage());
    }
    
    private function processMessage($data) {
        try {
            switch ($data['type']) {
                case 'gmcron':
                    $this->handleGmcron($data);          #跟买任务
                    break;
                case 'getprice':
                    $this->handleGetPrice($data['id']);        #SKU获取价格
                    break;
                case 'Management_reupload':
                    $this->handleReupload($data['sku']);       #重新上架
                    break;
                default:
                    $this->processSlowTask($data);
                    break;
            }
        } catch (Exception $e) {
            throw new RecoverableException("处理 {$data['type']} 类型消息失败: ".$e->getMessage());
        }
    }
    
    private function handleGetPrice($sku) {
        $this->logger->info("开始获取价格: SKU {$sku}");
        
        $apiUrl = "http://43.139.204.216:5000/api/product?sku=$sku";
        $response = $this->makeHttpRequest($apiUrl);
        
        if (!$response) {
            throw new RecoverableException("获取价格API请求失败");
        }
        
        $json = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("解析价格API响应失败");
        }
        
        $priceData = $this->extractPriceData($json);
        $this->updateSellerPrice($sku, $priceData);
        
        $this->logger->info("价格更新成功: SKU {$sku}");
    }
    
    private function makeHttpRequest($url, $timeout = 30) {
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            throw new RecoverableException("HTTP请求失败: ".($error['message'] ?? '未知错误'));
        }
        
        return $response;
    }
    
    private function extractPriceData($json) {
        if (!isset($json['widgetStates']['webPrice-3121879-default-1'])) {
            throw new Exception("价格数据格式不符预期");
        }
        
        $priceInfo = json_decode($json['widgetStates']['webPrice-3121879-default-1'], true);
        $data = [];
        
        $isYen = strpos($priceInfo['price'], '¥') !== false;
        $rate = $isYen ? 1 : $this->fx['rmb']['num'];
        
        $data['zprice'] = $this->parseAmount($priceInfo['price']) * $rate;
        $data['originalPrice'] = $this->parseAmount($priceInfo['originalPrice']) * $rate;
        
        if (!empty($priceInfo['cardPrice'])) {
            $data['cardPrice'] = $this->parseAmount($priceInfo['cardPrice']) * $rate;
        }
        
        return $data;
    }
    
    private function updateSellerPrice($sku, $data) {
        try {
            $this->db->beginTransaction();
            
            $result = $this->db->update('seller', $data, ['sku' => $sku]);
            if (!$result) {
                throw new Exception("数据库更新失败: ".$this->db->error());
            }
            
            $this->db->commit();
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    private function processSlowTask($data) {
        $this->logger->info("开始处理慢速任务: 类型 {$data['type']}");
        
        try {
            // 根据不同类型处理慢速任务
            switch ($data['type']) {
                case 'image_processing':
                    return $this->processImageTask($data);
                case 'data_export':
                    return $this->processExportTask($data);
                case 'report_generation':
                    return $this->processReportTask($data);
                default:
                    throw new Exception("未知的慢速任务类型: {$data['type']}");
            }
        } catch (Exception $e) {
            $this->logger->error("慢速任务处理失败: ".$e->getMessage());
            throw new RecoverableException("慢速任务处理失败: ".$e->getMessage());
        }
    }
    
    private function processImages($images) {
        if (strpos($images, ';') === false) {
            return replaceImageUrl($images);
        }else{
            $processed = [];
            $images = explode(";",$images);
            foreach ($images as $image) {
                $image = replaceImageUrl($image);
                $processed[] = $image;
            }
            return implode(';', $processed);;
        }
    }
    
    private function processImageTask($data) {
        // 实现图片处理逻辑
        $this->logger->debug("处理图片任务", $data);
        return true;
    }
    
    private function processExportTask($data) {
        // 实现数据导出逻辑
        $this->logger->debug("处理导出任务", $data);
        return true;
    }
    
    private function processReportTask($data) {
        // 实现报告生成逻辑
        $this->logger->debug("处理报告任务", $data);
        return true;
    }
    
    /************************* 跟买任务 **************************************/
    private function handleGmcron($data) {
        $this->logger->info("开始处理GM Cron任务: ID {$data['id']}");
        try {
            // 1. 获取任务数据
            $task = $this->getGmcronTask($data);
            if (!$this->shouldProcessTask($task)) {
                if($task['apistatus']!=1){
                    $this->updateTask($task['id'], ['msg'=>'店铺异常请更新店铺状态','status'=>'no']);
                }else if(empty($task['ClientId'])){
                    $this->updateTask($task['id'], ['msg'=>'店铺不存在','status'=>'no']);
                }else{
                    
                }
                $this->logger->debug("任务不需要处理: ID {$data['id']}");
                return true;
            }
            // 2. 根据状态处理任务
            $result = $this->processTaskByStatus($task);
            
            // 3. 更新任务状态
            $this->updateTask($task['id'], $result);
            
            $this->logger->info("GM Cron任务处理完成: ID {$data['id']}");
            return $result;
            
        } catch (RecoverableException $e) {
            $this->logger->warning("GM Cron可恢复错误: ID {$data['id']} - ".$e->getMessage());
            throw $e;
            
        } catch (Exception $e) {
            $this->logger->error("GM Cron处理失败: ID {$data['id']} - ".$e->getMessage());
            $this->markTaskAsFailed($data['id'], $e->getMessage());
            throw $e;
        }
    }
    
    private function getGmcronTask($data) {
        $id = $data['id'];
        if(isset($data['data'])){
            return $data['data'];
        }
        $this->logger->debug("获取任务数据: ID {$id}");
        
        $sql = "SELECT A.*, B.ClientId, B.key, B.currency_code, B.apistatus 
                FROM ozon_cron A 
                LEFT JOIN ozon_store B ON A.storeid=B.id 
                WHERE A.id=:id LIMIT 1";
        
        $task = $this->db->getRow($sql, [':id' => $id]);
        if (!$task) {
            throw new Exception("找不到ID为 {$id} 的任务记录");
        }
        
        return $task;
    }
    
    private function shouldProcessTask($task) {
        // 检查任务是否应该被处理
        if (empty($task['ClientId']) || $task['apistatus'] != 1 || $task['status'] == 'no' || $task['status'] == 'ok') {
            return false;
        }
        return true;
    }
    
    private function processTaskByStatus($task) {
        $this->logger->info("处理任务状态: {$task['status']}");
        
        switch ($task['status']) {
            case '准备中':
                return $this->processPreparingTask($task);
                
            case '采集数据中':
                return $this->processDataCollectingTask($task);
                
            case '数据采集成功':
                return $this->processDataCollectedTask($task);
                
            case '水印处理':
                return $this->handleWatermark($task);
                
            case '数据处理中':
                return $this->processDataProcessingTask($task);
                
            case '数据上传中':
                return $this->processDataUploadingTask($task);
                
            case '等待添加库存':
                return $this->processStockWaitingTask($task);
                
            case '重新上架':
                return $this->processReuploadTask($task);
                
            case '重新上传中':
                return $this->processReuploadingTask($task);
                
            case '更新图片特征':
                return $this->processImageUpdateTask($task);
                
            default:
                throw new Exception("未知的任务状态: {$task['status']}");
        }
    }
    
    private function processPreparingTask($task) {
        $this->logger->info("处理准备中任务: ID {$task['id']}");
        
        $jsonDir = ROOT.'/assets/json/特征数据库';
        
        $jsonFiles = [
            'main' => $jsonDir . '/' . $task['sku'] . '.json',
            'attributes' => $jsonDir . '/' . $task['sku'] . '_Dataattributes.json',
            'fallback' => $jsonDir . '/' . $task['sku'] . '_2.json'
        ];
        
        // 检查是否有有效的缓存文件
        if ($this->hasValidCache($jsonFiles['main'])) {
            return $this->processWithCache($task, $jsonFiles['main']);
        }
        
        // 检查Redis中是否有备用数据
        $redisData = $this->getRedisData($task['sku']);
        if ($redisData && $task['nums'] == 0) {
            return $this->processWithRedisData($task, $jsonFiles['fallback'], $redisData);
        }
        
        // 需要从API获取数据
        return $this->fetchDataFromApi($task, $jsonFiles);
    }
    
    private function ensureDirectoryExists($dirPath) {
        if (!file_exists($dirPath)) {
            if (!mkdir($dirPath, 0755, true)) {
                throw new Exception("无法创建目录: {$dirPath}");
            }
            $this->logger->debug("已创建目录: {$dirPath}");
        }
    }
    
    private function hasValidCache($cacheFile) {
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $data = json_decode(file_get_contents($cacheFile), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->warning("缓存文件JSON解析失败", ['file' => $cacheFile]);
            return false;
        }
        
        // 检查文件不仅存在，还要包含必要字段
        $isValid = isset($data['name']) || isset($data['title']);
        $isRecent = (time() - filemtime($cacheFile)) < 86400;
        
        return $isValid && $isRecent;
    }
    
    
    private function getRedisData($sku) {
        try {
            $redis = new Redis();
            $redis->connect('127.0.0.1', 6379);
            $redis->setOption(Redis::OPT_READ_TIMEOUT, 5);
            
            $data = $redis->get($sku.'_ozonsku');
            $redis->close();
            
            return $data ? json_decode($data, true) : null;
            
        } catch (Exception $e) {
            $this->logger->warning("Redis连接失败: ".$e->getMessage());
            return null;
        }
    }
    
    private function processWithRedisData($task, $fallbackFile, $redisData) {
        // 首先尝试从Redis数据中获取标题
        if (isset($redisData['name'])) {
            $title = $redisData['name'];
        } elseif (isset($redisData['title'])) {
            $title = $redisData['title'];
        } else {
            $title = null;
        }
        
        // 如果Redis中没有标题，再尝试从备用文件获取
        if (empty($title) && file_exists($fallbackFile)) {
            $jsondata = file_get_contents($fallbackFile);
            if ($jsondata) {
                $json = json_decode($jsondata, true);
                $title = $json['name'] ?? $json['title'] ?? null;
            }
        }
        
        // 记录调试信息
        $this->logger->debug("Redis数据处理结果", [
            'sku' => $task['sku'],
            'has_title' => !empty($title),
            'redis_data_keys' => array_keys($redisData)
        ]);
        $jsondata = file_get_contents($fallbackFile);
        
        if ($jsondata) {
            $json = json_decode($jsondata, true);
            error_log($task['id']."准备中备用数据标题获取：".$json['name']); // 调试日志
            if($task['watermark']==0){
                return [
                    'status' => '数据处理中',
                    'msg' => '',
                    'primary_image' => $json['primary_image'] ?? null,
                    'height' => $json['height'] ?? null,
                    'depth' => $json['depth'] ?? null,
                    'width' => $json['width'] ?? null,
                    'weight' => $json['weight'] ?? null,
                    'time' => time() + 1,
                    'title' => $json['name'] ?? $title
                ];
            }else{
                return [
                    'status' => '水印处理',
                    'msg' => '',
                    'primary_image' => $json['primary_image'] ?? null,
                    'height' => $json['height'] ?? null,
                    'depth' => $json['depth'] ?? null,
                    'width' => $json['width'] ?? null,
                    'weight' => $json['weight'] ?? null,
                    'time' => time() + 1,
                    'title' => $json['name'] ?? $title
                ];
            }
        }
        
        $jsondata = $redisData;
        if ($jsondata['items'][0]['variantId']) {
            $data = apigetattributes($task['sku'], $jsondata['items'][0]['variantId']);
            error_log($task['id']."准备中备用数据新特征标题获取1：".$data['name']); // 调试日志
            if ($data) {
                $jsons = $this->processAttributeData($data);
                error_log($task['id']."准备中备用数据新特征标题获取2：".$jsons['name']); // 调试日志
                file_put_contents($fallbackFile, json_encode($jsons, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                if($task['watermark']==0){
                    return [
                        'status' => '数据处理中',
                        'msg' => '',
                        'primary_image' => $jsons['primary_image'] ?? null,
                        'title' => $jsons['name'] ?? null,
                        'height' => $jsons['height'] ?? null,
                        'depth' => $jsons['depth'] ?? null,
                        'width' => $jsons['width'] ?? null,
                        'weight' => $jsons['weight'] ?? null
                    ];
                }else{
                    return [
                        'status' => '水印处理',
                        'msg' => '',
                        'primary_image' => $jsons['primary_image'] ?? null,
                        'title' => $jsons['name'] ?? null,
                        'height' => $jsons['height'] ?? null,
                        'depth' => $jsons['depth'] ?? null,
                        'width' => $jsons['width'] ?? null,
                        'weight' => $jsons['weight'] ?? null
                    ];
                }
            }
        }
        if($task['nums']<=3){
            $task['nums']++;
            return [
                'status' => '准备中',
                'nums' => $task['nums']
            ];
        }else{
            return [
                'status' => 'no',
                'msg' => '采集特征数据失败000001'
            ];
        }
        
    }
    
    private function processAttributeData($data) {
        $jsons = [];
        $complex_attributes = [];
        $attributes = [];
        
        $jsons['name'] = $data['name'] ?? '';
        $jsons['description_category_id'] = $data['description_category_id'] ?? '';
        
        if (isset($data['categories'][2]['id'])) {
            $categoryId = $data['categories'][2]['id'];
        }
        
        foreach ($data['attributes'] as $item) {
            $key = intval($item['key']);
            
            if ($key === 4194) { // 产品主图
                $jsons['primary_image'] = $item['value'];
                continue;
            } elseif ($key === 4195) { // 图片数组
                $jsons['images'] = $item['collection'];
                continue;
            } elseif ($key === 9456) { // 高 (毫米)
                $jsons['height'] = intval($item['value']);
                continue;
            } elseif ($key === 9455) { // 宽 (毫米)
                $jsons['width'] = intval($item['value']);
                continue;
            } elseif ($key === 9454) { // 高 (毫米)
                $jsons['depth'] = intval($item['value']);
                continue;
            } elseif ($key === 4497) { // 商品重量 (克)
                $jsons['weight'] = intval($item['value']);
                continue;
            } elseif ($key === 100001) {
                foreach ($item['complex'] as $complex) {
                    if ($complex['id'] == 21837 || $complex['id'] == 21841) {
                        $values = [['dictionary_value_id' => 0, 'value' => $complex['value']]];
                        $complex_attributes[] = [
                            'id' => $complex['id'],
                            'complex_id' => 100001,
                            'values' => $values
                        ];
                    }
                }
                continue;
            } elseif ($key === 8229) {
                $jsons['typeName'] = $item['value'];
            } elseif ($key === 85) {
                $hasBrand = true;
            }
            
            $values = [];
            if (!empty($item['collection'])) {
                foreach ($item['collection'] as $x) {
                    $attribute_id_value = attribute_id_names($item['key'], $x);
                    $values[] = $attribute_id_value ?: ['dictionary_value_id' => 0, 'value' => $x];
                }
            } elseif (!empty($item['value'])) {
                $attribute_id_value = attribute_id_names($item['key'], $item['value']);
                $values[] = $attribute_id_value ?: ['dictionary_value_id' => 0, 'value' => $item['value']];
            }
            
            if (!empty($values)) {
                $attributes[] = [
                    'id' => $key,
                    'complex_id' => 0,
                    'values' => $values
                ];
            }
        }
        
        if (isset($categoryId) && isset($jsons['typeName'])) {
            $typeId = findTypeIdByCategoryAndName($categoryId, $jsons['typeName']);
            if ($typeId) {
                $jsons['type_id'] = $typeId;
            }
        }
        
        if (empty($hasBrand)) {
            $attributes[] = [
                'id' => 85,
                'complex_id' => 0,
                'values' => [['dictionary_value_id' => 126745801, 'value' => 'Нет бренда']]
            ];
        }
        
        $jsons['complex_attributes'] = $complex_attributes;
        $jsons['attributes'] = $attributes;
        $jsons['pdf_list'] = [];
        $jsons['color_image'] = "";
        
        return $jsons;
    }
    
    private function getClientInfo($clientId) {
        return $this->db->getRow("SELECT * FROM ozon_client WHERE id=:id LIMIT 1", [':id' => $clientId]);
    }
    
    /********************** 水印处理 **********************************/
    private function handleWatermark($task) {
        $this->logger->info("开始处理水印任务: ID {$task['id']}");
        
        $jsonFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '.json';
        $fallbackFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '_2.json';
        // 尝试读取备用文件
        if (file_exists($fallbackFile)) {
            $jsonData = file_get_contents($fallbackFile);
            $task['return'] = $jsonData;
        } 
        // 否则读取主文件
        elseif (file_exists($jsonFile)) {
            $jsonData = file_get_contents($jsonFile);
            $task['return'] = $jsonData;
        } else {
            throw new Exception("找不到JSON数据文件");
        }
        
        $json = json_decode($jsonData,true);
        if(isset($json['primary_image'])){
            $array['primary_image'] = $this->WatermarkApi($task,$json['primary_image']);
        }
        foreach ($json['images'] as $image){
            $array['images'][] = $this->WatermarkApi($task,$image);
        }
        if($array){
            $this->setRedis($task['sku'].'_'.$task['ClientId'].'_Watermark',json_encode($array));
            return [
                'time' => time() + rand(5, 50),
                'status' => '数据处理中',
                'msg' => ''
            ];
        }else{
            return [
                'status' => 'no',
                'msg' => "水印任务: ID {$task['id']} 处理失败。"
            ];
        }
    }
    
    
    private function setRedis($name, $value, $time = 3600)
    {
        $redis = new Redis();
        $redis->connect('127.0.0.1', 6379);
        $redis->set($name, $value, $time);
        $redis->close();
    }
    
    private function WatermarkApi($task,$image_url){
        $array = [
            'image_url'=>$image_url,
            'text_watermark'=>$task['ClientId'],
            'text_position'=>'repeat',
            'text_color'=>'#23292e',
            'date'=>$task['date']
        ];
        $curl = curl_init();

        curl_setopt_array($curl, array(
           CURLOPT_URL => 'https://cdn1.100b.cn/api.php',
           CURLOPT_RETURNTRANSFER => true,
           CURLOPT_ENCODING => '',
           CURLOPT_MAXREDIRS => 10,
           CURLOPT_TIMEOUT => 0,
           CURLOPT_FOLLOWLOCATION => true,
           CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
           CURLOPT_CUSTOMREQUEST => 'POST',
           CURLOPT_POSTFIELDS =>json_encode($array),
           CURLOPT_HTTPHEADER => array(
              'Content-Type: application/json'
           ),
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $json = json_decode($response,true);
        if(isset($json['status']) and $json['status']=='success'){
            return $json['watermarked_image'];
        }else{
            return false;
        }
    }
    
    private function fetchProductTitle($task, $attributesFile) {
        try {
            $res = apigetattributes(strval($task['sku']));
            
            // 增加更严格的验证
            if (!empty($res) && empty($res['msg']) && isset($res['name'])) {
                $this->logger->debug("成功获取商品标题", ['sku' => $task['sku'], 'title' => $res['name']]);
                return $res['name'];
            }
            
            // 记录API返回的原始数据以便调试
            if (!empty($res)) {
                file_put_contents($attributesFile, json_encode($res, JSON_PRETTY_PRINT));
                $this->logger->warning("API返回数据缺少标题字段", ['sku' => $task['sku'], 'response' => $res]);
            } else {
                $this->logger->warning("API返回空数据", ['sku' => $task['sku']]);
            }
        } catch (Exception $e) {
            $this->logger->error("获取商品标题异常", [
                'sku' => $task['sku'], 
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return null;
    }
    
    private function processWithCache($task, $cacheFile) {
        $jsonData = file_get_contents($cacheFile);
        $data = json_decode($jsonData, true);
        
        if($task['watermark']==0){
            $update = [
                'status' => '数据处理中',
                'msg' => '',
                'primary_image' => $data['primary_image'] ?? null,
                'height' => $data['height'] ?? null,
                'depth' => $data['depth'] ?? null,
                'width' => $data['width'] ?? null,
                'weight' => $data['weight'] ?? null,
                'time' => time() + 1
            ];
        }else{
            $update = [
                'status' => '水印处理',
                'msg' => '',
                'primary_image' => $data['primary_image'] ?? null,
                'height' => $data['height'] ?? null,
                'depth' => $data['depth'] ?? null,
                'width' => $data['width'] ?? null,
                'weight' => $data['weight'] ?? null,
                'time' => time() + 1
            ];
        }
        
        if (!empty($data['name'])) {
            error_log($task['id']."准备中缓存文件标题获取：".$data['name']); // 调试日志
            $update['title'] = $data['name'];
        }
        
        return $update;
    }
    
    private function fetchDataFromApi($task, $jsonFiles) {
        if ($task['task_id2'] > 0) {
            return ['status' => '采集数据中', 'msg' => ''];
        }
        
        $clientInfo = $this->getClientInfo($task['clientids']);
        if (!$clientInfo) {
            return ['status' => 'no', 'msg' => '本次任务的服务器已下架，请联系客服处理。'];
        }
        
        $client = new \lib\OzonApiClient($clientInfo['ClientId'], $clientInfo['key']);
        $apiResult = $client->OzonApiskufz($task);
        
        if (!empty($apiResult['result']['task_id'])) {
            $update = [
                'status' => '采集数据中',
                'time' => time() + rand(100, 180),
                'msg' => '',
                'nums' => 0,
                'task_id2' => $apiResult['result']['task_id']
            ];
            
            // 设置原价折扣
            if (empty($task['old_price'])) {
                $update['old_price'] = $this->calculateOriginalPrice($task['price']);
            }
            
            // 获取商品标题
            if (empty($task['title'])) {
                $update['title'] = $this->fetchProductTitle($task, $jsonFiles['attributes']);
            }
            
            return $update;
        }
        
        // 处理API错误
        if ($apiResult['code'] == 7) {
            $this->db->update('client', ['status' => 1], ['id' => $task['clientids']]);
            $this->getclient($task);
            return ['status' => '准备中', 'msg' => '服务器错误，为你重新分配服务器...'];
        }
        
        return ['status' => 'no', 'msg' => '数据获取失败，服务器ID:'.$task['clientids']];
    }
    
    private function processDataCollectedTask($task) {
        $this->logger->info("处理数据采集成功任务: ID {$task['id']}");
        
        $jsonFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '.json';
        $attributesFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '_Dataattributes.json';
        
        try {
            $jsonData = file_get_contents($jsonFile);
            $jsonReturn = json_decode($jsonData, true, 512, JSON_THROW_ON_ERROR);
            
            if($task['watermark']==0){
                $update = [
                    'status' => '数据处理中',
                    'msg' => ''
                ];
            }else{
                $update = [
                    'status' => '水印处理',
                    'msg' => ''
                ];
            }
            
            // 处理属性文件
            if (file_exists($attributesFile)) {
                $attributesData = file_get_contents($attributesFile);
                $attributes = json_decode($attributesData, true, 512, JSON_THROW_ON_ERROR);
                
                if (isset($attributes['items'])) {
                    foreach ($attributes['items'] as $item) {
                        switch ($item['key']) {
                            case 9456: // 高 (毫米)
                                $update['height'] = (int) $item['value'];
                                break;
                            case 9454: // 长 (毫米)
                                $update['depth'] = (int) $item['value'];
                                break;
                            case 9455: // 宽 (毫米)
                                $update['width'] = (int) $item['value'];
                                break;
                            case 4497: // 商品重量 (克)
                                $update['weight'] = (float) $item['value'];
                                break;
                            case '4194': // 商品主图
                                if (empty($jsonReturn['primary_image'])) {
                                    $jsonReturn['primary_image'] = $item['value'];
                                }
                                break;
                        }
                        
                        // 添加缺失的属性
                        if (!$this->hasAttributeId($jsonReturn, $item['key']) && 
                            !in_array($item['key'], [4194, 4195, 22967, 10098])) {
                            $values = [];
                            if ($item['collection']) {
                                foreach ($item['collection'] as $x) {
                                    $values[] = ['dictionary_value_id' => 0, 'value' => $x];
                                }
                            } else {
                                $values[] = ['dictionary_value_id' => 0, 'value' => $item['value']];
                            }
                            $jsonReturn['attributes'][] = [
                                'id' => (int) $item['key'],
                                'complex_id' => 0,
                                'values' => $values
                            ];
                        }
                    }
                    
                    // 更新主图
                    if (empty($task['primary_image'])) {
                        $update['primary_image'] = $jsonReturn['primary_image'];
                    }
                    
                    // 更新标题
                    if (empty($task['title']) && isset($attributes['name'])) {
                        $update['title'] = $attributes['name'];
                    }
                    
                    // 保存更新后的JSON
                    file_put_contents($jsonFile, json_encode(
                        $jsonReturn, 
                        JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
                    ));
                }
            }
            
            return $update;
            
        } catch (JsonException $e) {
            throw new Exception("JSON解析错误: " . $e->getMessage());
        }
    }
    
    private function hasAttributeId($data, $attributeId) {
        if (empty($data['attributes'])) {
            return false;
        }
        
        foreach ($data['attributes'] as $attribute) {
            if ($attribute['id'] == $attributeId) {
                return true;
            }
        }
        
        return false;
    }
    
    private function processDataProcessingTask($task) {
        $this->logger->info("处理数据处理中任务: ID {$task['id']}");
        
        $jsonFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '.json';
        $fallbackFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '_2.json';
        
        try {
            // 尝试读取备用文件
            if (file_exists($fallbackFile)) {
                $jsonData = file_get_contents($fallbackFile);
                $task['return'] = $jsonData;
            } 
            // 否则读取主文件
            elseif (file_exists($jsonFile)) {
                $jsonData = file_get_contents($jsonFile);
                $task['return'] = $jsonData;
            } else {
                throw new Exception("找不到JSON数据文件");
            }
            
            $client = new \lib\OzonApiClient($task['ClientId'], $task['key']);
            $result = $client->productimport($task);
            
            if ($result) {
                return [
                    'task_id' => $result[0],
                    'offer_id' => $result[1],
                    'time' => time() + rand(5, 50),
                    'status' => '数据上传中',
                    'msg' => ''
                ];
            }
            
            throw new Exception("不知名错误，上传不成功请联系客服反馈");
            
        } catch (Exception $e) {
            $this->logger->error("数据处理失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function processDataUploadingTask($row) {
        $this->logger->info("处理数据上传中任务: ID {$row['id']}");
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        if($row['task_id']){
            $data = $client->productimportinfo($row);    #查询商品添加或更新状态
            if($data){
                if(isset($data['errors'][0]['message'])){
                    $msg = Transmart($data['errors'][0]['message']);
                    if(!$msg){
                        $msg = $data['errors'][0]['message'];
                    }
                }
                if($data['status']=='imported'){
                    if($row['warehouse_id']>0 and $row['stock']>0){
                        $save['time'] = time()+rand(60,180);
                        $save['status']="等待添加库存";
                    }else{
                        $save['status']="ok";
                    }
                }else if($data['status']=='pending'){  //商品等待排队处理排队审核
                    $save['status']="数据上传中";
                }else if($data['status']=='skipped'){
                    if($row['warehouse_id']>0 and $row['stock']>0){
                        $save['time'] = time()+rand(60,180);
                        $save['status']="等待添加库存";
                    }else{
                        $save['status']="ok";
                    }
                }else if($data['errors'][0]['code']=='double_without_merger_offer'){ #无法与其他商品合并
                    if($row['warehouse_id']>0 and $row['stock']>0){
                        $save['time'] = time()+rand(60,180);
                        $save['status']="等待添加库存";
                    }else{
                        $save['status']="ok";
                    }
                }else if($data['errors'][0]['code']=='warning_attribute_values_out_of_range'){ #无法与其他商品合并
                    if($row['warehouse_id']>0 and $row['stock']>0){
                        $save['time'] = time()+rand(60,180);
                        $save['status']="等待添加库存";
                    }else{
                        $save['status']="ok";
                    }
                }else if($data['errors'][0]['code']=='BR_hashtag_validation'){ #每一标签必须分开的一个空间，并开始与一个#标志。 检查有没有空间内的标签-允许他们之间只有标签
                    if($row['warehouse_id']>0 and $row['stock']>0){
                        $save['time'] = time()+rand(60,180);
                        $save['status']="等待添加库存";
                    }else{
                        $save['status']="ok";
                    }
                }else if($data['errors'][0]['code']=='INVALID_LOOKUP_KEY_EMPTY'){ #无法更新商品。请再试一次：在商品列表中点击三个点，然后选择“编辑商品 → 提交审核”
                    if($row['warehouse_id']>0 and $row['stock']>0){
                        $save['time'] = time()+rand(60,180);
                        $save['status']="等待添加库存";
                    }else{
                        $save['status']="ok";
                    }
                }else if($data['errors'][0]['code']=='DAILY_CREATE_LIMIT_EXCEEDED'){
                    $save['status'] = 'no';
                    $save['msg'] = '已达到官方每日限制';
                }else if($data['errors'][0]['code']=='SPU_ALREADY_EXISTS_IN_ANOTHER_ACCOUNT'){
                    $save['status'] = 'no';
                    $save['msg'] = '同样的商品在以下卡片和账户中重复出现';
                }else if($data['errors'][0]['code']=='INTERNAL'){#技术错误。请稍后尝试完成请求，或联系"内容/使用产品卡"→"创建和编辑产品"类别中的支持服务
                    $save['status'] = 'no';
                }else if($data['errors'][0]['code']=='ML_INCORRECT_VOLUME_WEIGHT'){
                    $save['status'] = 'no';
                }else if($data['errors'][0]['code']=='DESCRIPTION_DECLINE'){
                    if($data['errors'][0]['state']=='declined'){
                        $save['status'] = 'no';
                    }
                }else if($data['errors'][0]['code']=='FB_KAPKAN'){
                    $save['status'] = 'no';
                }else{
                    file_put_contents('log.txt', "数据上传中\n".json_encode($data) . "\n\n", FILE_APPEND);
                    $save = ['msg'=>json_encode($data),'status' => 'no']; #腾讯交互翻译
                }
                if($msg && empty($save['msg'])){
                    $save['msg'] = $msg;
                }
                if ($row['clientids'] > 0 && $row['task_id2'] > 0) {
                    try {
                        $clientInfo = $this->getClientInfo($row['clientids']);
                        if ($clientInfo) {
                            $client = new \lib\OzonApiClient($clientInfo['ClientId'], $clientInfo['key']);
                            $archiveData = $client->productimportinfo(['task_id' => $row['task_id2']]);
                            
                            if (!empty($archiveData['product_id'])) {
                                $batchArray = [$archiveData['product_id']];
                                $client->productarchive($batchArray);
                            }
                        }
                    } catch (Exception $e) {
                        $this->logger->warning("归档产品失败: " . $e->getMessage());
                    }
                }
            }else{
                $save = ['msg'=>'服务器内部出错','status' => 'no']; #腾讯交互翻译
            }
        }else{
            $save = ['msg'=>'不充分','status' => 'no']; #腾讯交互翻译
        }
        return $save;
    }
    
    private function handleSuccessfulUpload($task) {
        if ($task['warehouse_id'] > 0 && $task['stock'] > 0) {
            return ['status' => '等待添加库存'];
        }
        return ['status' => 'ok'];
    }
    
    private function handleUploadError($data, $task) {
        $errorMap = [
            'double_without_merger_offer' => function() use ($task) {
                if ($task['warehouse_id'] > 0 && $task['stock'] > 0) {
                    return ['status' => '等待添加库存'];
                }
                return ['status' => 'ok'];
            },
            'warning_attribute_values_out_of_range' => function() use ($task) {
                if ($task['warehouse_id'] > 0 && $task['stock'] > 0) {
                    return ['status' => '等待添加库存'];
                }
                return ['status' => 'ok'];
            },
            'BR_hashtag_validation' => function() use ($data) {
                return [
                    'status' => 'no',
                    'msg' => $this->translateErrorMessage($data['errors'][0]['message'])
                ];
            },
            // 其他错误处理...
        ];
        
        if (isset($data['errors'][0]['code']) && isset($errorMap[$data['errors'][0]['code']])) {
            return $errorMap[$data['errors'][0]['code']]();
        }
        
        // 记录未知错误
        $this->logger->error("数据上传错误", $data);
        return [
            'msg' => $this->translateErrorMessage($data['errors'][0]['message'] ?? '未知错误'),
            'status' => 'no'
        ];
    }
    
    private function calculateOriginalPrice($currentPrice) {
        $discount = rand(30, 60) / 100; // 30-60%折扣
        return round($currentPrice * (1 + $discount), 2);
    }
    
    private function processDataCollectingTask($task) {
        $clientInfo = $this->getClientInfo($task['clientids']);
        $client = new \lib\OzonApiClient($clientInfo['ClientId'], $clientInfo['key']);
        
        $data = $client->characteristics($task);
        $jsonFile = ROOT.'/assets/json/特征数据库/' . $task['sku'] . '.json';
        
        if ($data and $data['result'][0]) {
            file_put_contents($jsonFile, json_encode($data['result'][0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            if ($row['clientids'] > 0 && $row['task_id2'] > 0) {
                try {
                    $client = new \lib\OzonApiClient($clientInfo['ClientId'], $clientInfo['key']);
                    $archiveData = $client->productimportinfo(['task_id' => $row['task_id2']]);
                    
                    if (!empty($archiveData['product_id'])) {
                        $batchArray = [$archiveData['product_id']];
                        $client->productarchive($batchArray);
                    }
                } catch (Exception $e) {
                    $this->logger->warning("归档产品失败: " . $e->getMessage());
                }
            }
            return [
                'status' => '数据采集成功',
                'msg' => '',
                'primary_image' => $data['result'][0]['primary_image']
            ];
        }
        
        // 处理失败情况
        if ($task['nums'] == 2) {
            return ['status' => 'no', 'msg' => '数据查询失败'];
        }
        
        // 检查是否有缓存文件
        if (file_exists($jsonFile)) {
            $jsonData = json_decode(file_get_contents($jsonFile), true);
            return [
                'status' => '数据采集成功',
                'msg' => '',
                'primary_image' => $jsonData['primary_image']
            ];
        }
        
        return [
            'status' => '采集数据中',
            'msg' => '数据查询失败',
            'nums' => $task['nums'] + 1
        ];
    }
    
    private function processStockWaitingTask($task) {
        $this->logger->info("处理等待添加库存任务: ID {$task['id']}");
        
        try {
            $client = new \lib\OzonApiClient($task['ClientId'], $task['key']);
            $result = $client->productsstocks($task);
            
            if ($result === true || (isset($result['result'][0]['updated']) && $result['result'][0]['updated'] === true)) {
                return ['status' => 'ok'];
            }
            
            if(isset($result['code']) && $result['code']==8){
                return ['time'=>time()+rand(30, 60)];
            }
            
            // 处理特定错误
            if (isset($result['result'][0]['errors'][0]['code'])) {
                $errorCode = $result['result'][0]['errors'][0]['code'];
                
                if ($errorCode === 'NOT_PASS_MODERATION') {
                    return [
                        'status' => 'no',
                        'msg' => '产品未创建成功'
                    ];
                }
                
                $errorMessage = $this->translateErrorMessage($result['result'][0]['errors'][0]['message']);
                return [
                    'status' => 'ok', // 某些错误仍标记为成功
                    'msg' => $errorMessage
                ];
            }
            
            // 记录未知响应
            $this->logger->error("未知的库存添加响应", $result);
            throw new RecoverableException("未知的库存添加响应");
            
        } catch (Exception $e) {
            $this->logger->error("添加库存失败: ".$e->getMessage());
            throw new RecoverableException("添加库存失败: ".$e->getMessage());
        }
    }
    
    private function processReuploadTask($task) {
        $this->logger->info("处理重新上架任务: ID {$task['id']} SKU {$task['sku']}");
    
        try {
            // 1. 检查必要字段
            if (empty($task['sku']) || empty($task['product_id'])) {
                throw new Exception("缺少必要字段: sku 或 product_id");
            }
    
            // 2. 获取商品特征数据
            $client = new \lib\OzonApiClient($task['ClientId'], $task['key']);
            if($task['product_id']){
                $client->productsstocks($task,'product_id',true);
            }
            $data = $client->characteristics2($task);
            
            if (!$data || empty($data['result'][0])) {
                throw new RecoverableException("获取商品特征数据失败");
            }
            
            // 3. 更新特征卖家代码
            $attributes[] = ['complex_id'=>0,'id'=>8292,'values'=>[['dictionary_value_id'=>0,'value'=>'hterp_'.time()]]];
            $attributes[] = ['complex_id'=>0,'id'=>9048,'values'=>[['dictionary_value_id'=>0,'value'=>'hterp_'.time()]]];
            $array[] = ['attributes'=>$attributes,'offer_id'=>$data['result'][0]['offer_id']];
            $client->attributesupdate($task,$array);
            
            // 4. 下架产品归档
            $client->productarchive([strval($task['product_id'])]);
    
            // 5. 保存特征数据到JSON文件
            $jsonDir = ROOT.'/assets/json/特征数据库';
            $this->ensureDirectoryExists($jsonDir);
            
            $jsonFile = $jsonDir . '/' . $task['sku'] . '.json';
            file_put_contents($jsonFile, json_encode(
                $data['result'][0], 
                JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
            ));
    
            // 6. 准备重新上传数据
            $update = [
                'status' => '重新上传中',
                'msg' => '',
                'primary_image' => $data['result'][0]['primary_image'] ?? null
            ];
    
            // 生成新的offer_id
            if (empty($task['offer_id'])) {
                $update['offer_id'] = $client->generate_offer_id();
            }
    
            $this->logger->info("重新上架任务处理完成", [
                'sku' => $task['sku'],
                'product_id' => $task['product_id']
            ]);
    
            return $update;
    
        } catch (RecoverableException $e) {
            $this->logger->warning("重新上架可恢复错误: ".$e->getMessage());
            throw $e;
            
        } catch (Exception $e) {
            $this->logger->error("重新上架处理失败: ".$e->getMessage());
            throw new Exception("重新上架处理失败: ".$e->getMessage());
        }
    }
    
    private function processReuploadingTask($task) {
        $this->logger->info("处理重新上传中任务: ID {$task['id']} SKU {$task['sku']}");
    
        try {
            // 1. 验证必要参数
            if (empty($task['sku']) || empty($task['ClientId']) || empty($task['key'])) {
                throw new Exception("缺少必要参数: sku, ClientId 或 key");
            }
    
            // 2. 准备JSON数据文件路径
            $jsonDir = ROOT.'/assets/json/特征数据库';
            $jsonFile = $jsonDir . '/' . $task['sku'] . '.json';
            
            if (!file_exists($jsonFile)) {
                throw new Exception("找不到特征数据文件: {$jsonFile}");
            }
    
            // 3. 读取特征数据
            $jsonData = file_get_contents($jsonFile);
            $task['return'] = $jsonData;
            
            // 4. 调用API重新上传商品
            $client = new \lib\OzonApiClient($task['ClientId'], $task['key']);
            $result = $client->productimport($task);
            
            if (!$result) {
                throw new Exception("商品重新上传API调用失败");
            }
    
            // 5. 返回更新后的任务状态
            $update = [
                'task_id' => $result[0],
                'offer_id' => $result[1],
                'time' => time() + rand(300, 600), // 5-10分钟后检查状态
                'status' => '等待添加库存',
                'msg' => ''
            ];
    
            $this->logger->info("商品重新上传成功", [
                'sku' => $task['sku'],
                'task_id' => $result[0],
                'offer_id' => $result[1]
            ]);
    
            return $update;
    
        } catch (RecoverableException $e) {
            $this->logger->warning("重新上传可恢复错误: ".$e->getMessage());
            throw $e;
            
        } catch (Exception $e) {
            $this->logger->error("重新上传处理失败: ".$e->getMessage());
            return [
                'status' => 'no',
                'msg' => $e->getMessage()
            ];
        }
    }
    
    private function processImageUpdateTask($task) {
        $this->logger->info("处理图片特征更新任务: ID {$task['id']} SKU {$task['sku']}");
    
        try {
            // 1. 验证必要参数
            if (empty($task['sku']) || empty($task['ClientId']) || empty($task['key'])) {
                throw new Exception("缺少必要参数: sku, ClientId 或 key");
            }
    
            // 2. 准备JSON数据文件路径
            $jsonDir = ROOT.'/assets/json/特征数据库';
            $jsonFile = $jsonDir . '/' . $task['sku'] . '.json';
            
            if (!file_exists($jsonFile)) {
                throw new Exception("找不到特征数据文件: {$jsonFile}");
            }
    
            // 3. 读取特征数据
            $jsonData = file_get_contents($jsonFile);
            $attributes = json_decode($jsonData, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("JSON解析错误: " . json_last_error_msg());
            }
    
            // 4. 验证图片数据是否存在
            if (empty($attributes['primary_image']) && empty($attributes['images'])) {
                throw new Exception("特征数据中缺少图片信息");
            }
    
            // 5. 调用API更新图片特征
            $client = new \lib\OzonApiClient($task['ClientId'], $task['key']);
            $result = $client->attributesimagesupdate($task);
            
            if (!$result) {
                throw new Exception("图片特征更新API调用失败");
            }
    
            // 6. 返回更新后的任务状态
            $update = [
                'status' => 'ok',
                'msg' => '',
                'time' => time() + 1
            ];
    
            $this->logger->info("图片特征更新成功", [
                'sku' => $task['sku'],
                'primary_image' => $attributes['primary_image'] ?? null,
                'image_count' => count($attributes['images'] ?? [])
            ]);
    
            return $update;
    
        } catch (RecoverableException $e) {
            $this->logger->warning("图片特征更新可恢复错误: ".$e->getMessage());
            throw $e;
            
        } catch (Exception $e) {
            $this->logger->error("图片特征更新处理失败: ".$e->getMessage());
            return [
                'status' => 'no',
                'msg' => $e->getMessage()
            ];
        }
    }
    
    private function translateErrorMessage($message) {
        // 这里可以实现您的翻译逻辑
        // 例如使用您之前代码中的 Transmart 函数
        return $message; // 暂时直接返回原消息
    }
    
    private function updateTask($id, $updateData) {
        if (empty($updateData)) {
            return;
        }
        
        if (!isset($updateData['time'])) {
            $updateData['time'] = time() + rand(5, 15);
        }
        
        $updateData['cron'] = 0;
        
        $this->db->update('cron', $updateData, ['id' => $id]);
    }
    
    private function markTaskAsFailed($id, $message) {
        $this->db->update('cron', [
            'status' => 'no',
            'msg' => substr($message, 0, 255),
            'cron' => 0,
            'time' => time() + 3600 // 1小时后重试
        ], ['id' => $id]);
    }
    /************************* 跟买任务结束 **************************************/
    
    /********************** 重新上架 **********************************/
    private function handleReupload($sku) {
        $this->logger->info("开始处理商品重新上架: SKU {$sku}");
        
        try {
            // 1. 获取商品基础信息
            $product = $this->getProductInfo($sku);
            if (!$product) {
                throw new Exception("找不到SKU为 {$sku} 的商品");
            }
            
            // 2. 获取店铺信息
            $store = $this->getStoreInfo($product['storeid']);
            if (!$store) {
                throw new Exception("找不到店铺ID为 {$product['storeid']} 的店铺信息");
            }
            
            // 3. 获取库存信息
            $stockInfo = $this->getStockInfo($sku, $store);
            if (!$stockInfo) {
                throw new RecoverableException("获取SKU {$sku} 的库存信息失败");
            }
            
            // 4. 准备重新上架数据
            $reuploadData = $this->prepareReuploadData($product, $store, $stockInfo);
            
            // 5. 记录重新上架任务
            $this->createReuploadTask($reuploadData);
            
            $this->logger->info("商品重新上架任务创建成功: SKU {$sku}");
            
        } catch (RecoverableException $e) {
            $this->logger->warning("商品重新上架可重试错误: ".$e->getMessage());
            throw $e;
            
        } catch (Exception $e) {
            $this->logger->error("商品重新上架失败: ".$e->getMessage());
            throw $e;
        }
    }
    
    private function getProductInfo($sku) {
        $this->logger->debug("获取商品信息: SKU {$sku}");
        
        try {
            $product = $this->db->find('products', '*', ['sku' => $sku]);
            if (!$product) {
                $this->logger->warning("数据库中没有找到SKU为 {$sku} 的商品");
                return false;
            }
            
            $requiredFields = ['uid', 'storeid', 'name', 'price', 'old_price', 'primary_image'];
            foreach ($requiredFields as $field) {
                if (empty($product[$field])) {
                    throw new Exception("商品缺少必要字段: {$field}");
                }
            }
            
            return $product;
            
        } catch (Exception $e) {
            $this->logger->error("获取商品信息失败: ".$e->getMessage());
            throw new RecoverableException("获取商品信息失败: ".$e->getMessage());
        }
    }
    
    private function getStoreInfo($storeId) {
        $this->logger->debug("获取店铺信息: 店铺ID {$storeId}");
        
        try {
            $store = $this->db->find('store', '*', ['id' => $storeId]);
            if (!$store) {
                throw new Exception("找不到店铺ID为 {$storeId} 的店铺");
            }
            
            if (empty($store['ClientId']) || empty($store['key'])) {
                throw new Exception("店铺API凭证不完整");
            }
            
            return $store;
            
        } catch (Exception $e) {
            $this->logger->error("获取店铺信息失败: ".$e->getMessage());
            throw $e;
        }
    }
    
    private function getStockInfo($sku, $store) {
        $this->logger->debug("获取库存信息: SKU {$sku}");
        
        try {
            $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
            $stockData = $client->stockswarehouse($sku);
            
            if (empty($stockData)) {
                $this->logger->warning("没有获取到SKU {$sku} 的库存数据");
                return false;
            }
            
            // 找出库存最多的仓库
            $maxStockItem = null;
            foreach ($stockData as $item) {
                if (!isset($item['present']) || !isset($item['warehouse_id']) || !isset($item['product_id'])) {
                    continue;
                }
                
                if (!$maxStockItem || $item['present'] > $maxStockItem['present']) {
                    $maxStockItem = [
                        'sku' => $item['sku'],
                        'stock' => $item['present'],
                        'warehouse_id' => $item['warehouse_id'],
                        'product_id' => $item['product_id']
                    ];
                }
            }
            
            if (!$maxStockItem) {
                throw new RecoverableException("没有有效的库存数据");
            }
            
            $this->logger->debug("库存信息", $maxStockItem);
            if($maxStockItem['product_id']){
                $client->productsstocks($maxStockItem,'product_id',true);
                $client->productarchive([strval($maxStockItem['product_id'])]);
            }
            return $maxStockItem;
            
        } catch (Exception $e) {
            $this->logger->error("获取库存信息失败: ".$e->getMessage());
            throw new RecoverableException("获取库存信息失败: ".$e->getMessage());
        }
    }
    
    private function prepareReuploadData($product, $store, $stockInfo) {
        return [
            'sku' => $product['sku'],
            'product_id' => $stockInfo['product_id'],
            'stock' => $stockInfo['stock'],
            'warehouse_id' => $stockInfo['warehouse_id'],
            'uid' => $product['uid'],
            'storeid' => $product['storeid'],
            'status' => '重新上架',
            'title' => $product['name'],
            'price' => $product['price'],
            'old_price' => $product['old_price'],
            'primary_image' => $product['primary_image'],
            'date' => date("Y-m-d"),
            'addtime' => date("Y-m-d H:i:s"),
            'type' => 'reupload'
        ];
    }
    
    private function createReuploadTask($data) {
        $this->logger->debug("创建重新上架任务", $data);
        
        try {
            $this->db->beginTransaction();
            
            $result = $this->db->insert('cron', $data);
            if (!$result) {
                throw new Exception("插入重新上架任务失败: ".$this->db->error());
            }
            
            $this->db->commit();
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->logger->error("创建重新上架任务失败: ".$e->getMessage());
            throw $e;
        }
    }
    /********************** 重新上架结束 **********************************/
    
    
    private function checkResourceUsage($messageId) {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryUsage > 100 * 1024 * 1024) { // 超过100MB
            $this->logger->warning("[$messageId] 内存使用过高: ".round($memoryUsage/1024/1024, 2)."MB");
        }
        
        // 可以添加更多资源检查...
    }
    
    public function __destruct() {
        try {
            if ($this->channel && $this->channel->is_open()) {
                $this->channel->close();
            }
            
            if ($this->connection && $this->connection->isConnected()) {
                $this->connection->close();
            }
        } catch (Exception $e) {
            $this->logger->error("关闭连接时出错: ".$e->getMessage());
        }
    }
}

// 自定义异常类
class InvalidMessageException extends Exception {}
class RecoverableException extends Exception {}

// 日志类
class ConsumerLogger {
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    private function log($level, $message, $context = []) {
        $logEntry = sprintf(
            "[%s] [%s] %s %s\n",
            date('Y-m-d H:i:s'),
            $level,
            $message,
            !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''
        );
        
        file_put_contents(__DIR__.'/logs/consumer.log', $logEntry, FILE_APPEND);
        
        // 同时输出到控制台
        echo $logEntry;
    }
}

// 启动消费者
try {
    $consumer = new SafeConsumer($Raconfig,$DB);
    $consumer->consume();
} catch (Exception $e) {
    echo "消费者启动失败: ".$e->getMessage()."\n";
    exit(1);
}
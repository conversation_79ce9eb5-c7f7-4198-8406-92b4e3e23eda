<?php
/**
 * Ozon订单数据同步脚本
 * 
 * 功能说明:
 * - 从Ozon API获取订单数据并同步到本地数据库
 * - 支持单进程和多进程模式
 * - 支持守护进程模式和单次执行模式
 * - 具备完善的错误处理和日志记录
 * - 支持数据去重和增量同步
 * 
 * 使用方法:
 * php ozon_order_sync.php [选项]
 * 
 * 选项:
 *   --daemon         守护进程模式（默认）
 *   --once           单次执行模式
 *   --single         单进程模式（默认）
 *   --multi          多进程模式
 *   --processes N    设置最大进程数（默认4）
 *   --interval N     设置同步间隔（秒，默认3600）
 *   --months N       设置同步月数（默认12）
 *   --help           显示此帮助信息
 * 
 * 数据去重机制:
 * - 基于posting_number进行去重
 * - 支持增量更新订单状态和信息
 * 
 * 宝塔进程守护管理器配置建议:
 * - 启动文件: /path/to/ozon_order_sync.php
 * - 启动参数: --daemon --multi --processes 4
 * - 进程数量: 1
 * - 自动重启: 是
 * - 日志文件: /path/to/worker/logs/order_sync_YYYY-MM-DD.log
 */

// 环境设置
ini_set('display_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('Asia/Shanghai');
ini_set('memory_limit', '512M');
set_time_limit(0);

// 设置正确的工作目录
chdir(__DIR__ . '/..');
require_once __DIR__ . '/../includes/common.php';

use lib\PdoHelper;
use lib\OzonApiClient;
use lib\JsonImporter;

// 兼容性修复：如果OzonApiClient中未使用全局命名空间，则定义一个临时的异常类
if (!class_exists('lib\InvalidArgumentException')) {
    class_alias('\\InvalidArgumentException', 'lib\InvalidArgumentException');
}

// 内置配置
$config = [
    'daemon_mode' => true,              // 守护进程模式
    'multi_process' => false,           // 多进程模式
    'max_processes' => 4,               // 最大进程数
    'process_timeout' => 1800,          // 子进程超时时间（秒）
    'sync_interval' => 3600,            // 同步间隔（秒）
    'max_execution_time' => 3000,       // 最大执行时间（秒）
    'api_delay' => 1,                   // API调用间隔（秒）
    'months_to_sync' => 12,             // 同步月数
    'page_size' => 1000,                // 每页数据量
    'log_level' => 'INFO',              // 日志级别
];

// 全局变量
$running = true;
$lockFileHandle = null;
$childProcesses = [];
$logDir = __DIR__ . '/logs';

// 日志目录检查
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}
$config['log_file'] = $logDir . '/order_sync_' . date('Y-m-d') . '.log';

/**
 * 获取全局锁，确保只有一个主实例在运行
 */
function acquireGlobalLock() {
    global $logDir, $lockFileHandle;
    
    $lockFilePath = $logDir . '/order_sync.lock';
    $lockFileHandle = fopen($lockFilePath, 'c+');
    
    if ($lockFileHandle === false) {
        writeLog('ERROR', '无法创建或打开锁文件', ['path' => $lockFilePath]);
        return false;
    }
    
    // 尝试以非阻塞方式获取排他锁
    if (!flock($lockFileHandle, LOCK_EX | LOCK_NB)) {
        // 检查锁是否已过期
        $pid = trim(fgets($lockFileHandle));
        if ($pid && function_exists('posix_getpgid') && posix_getpgid((int)$pid) === false) {
            // 进程不存在，强制获取锁
            writeLog('WARN', '发现过期的锁文件，强制获取', ['stale_pid' => $pid]);
            ftruncate($lockFileHandle, 0);
            rewind($lockFileHandle);
            if (flock($lockFileHandle, LOCK_EX)) {
                fwrite($lockFileHandle, getmypid());
                fflush($lockFileHandle);
                writeLog('INFO', '获取全局锁成功（强制）', ['pid' => getmypid()]);
                return true;
            }
        } else {
            writeLog('INFO', '另一个订单同步进程正在运行，退出当前进程', ['existing_pid' => $pid]);
            return false;
        }
    } else {
        // 成功获取锁
        ftruncate($lockFileHandle, 0);
        rewind($lockFileHandle);
        fwrite($lockFileHandle, getmypid());
        fflush($lockFileHandle);
        writeLog('INFO', '获取全局锁成功', ['pid' => getmypid()]);
        return true;
    }
    
    return false;
}

/**
 * 释放全局锁
 */
function releaseGlobalLock() {
    global $lockFileHandle;
    
    if ($lockFileHandle) {
        flock($lockFileHandle, LOCK_UN);
        fclose($lockFileHandle);
        $lockFileHandle = null;
        writeLog('INFO', '全局锁已释放');
    }
}

// 命令行参数解析
if (isset($argv)) {
    for ($i = 1; $i < count($argv); $i++) {
        switch ($argv[$i]) {
            case '--daemon':
                $config['daemon_mode'] = true;
                break;
            case '--once':
                $config['daemon_mode'] = false;
                break;
            case '--single':
                $config['multi_process'] = false;
                break;
            case '--multi':
                $config['multi_process'] = true;
                break;
            case '--processes':
                if (isset($argv[$i + 1]) && is_numeric($argv[$i + 1])) {
                    $config['max_processes'] = max(1, min(10, (int)$argv[$i + 1]));
                    $i++;
                }
                break;
            case '--interval':
                if (isset($argv[$i + 1]) && is_numeric($argv[$i + 1])) {
                    $config['sync_interval'] = max(60, (int)$argv[$i + 1]);
                    $i++;
                }
                break;
            case '--months':
                if (isset($argv[$i + 1]) && is_numeric($argv[$i + 1])) {
                    $config['months_to_sync'] = max(1, min(24, (int)$argv[$i + 1]));
                    $i++;
                }
                break;
            case '--help':
                echo "Ozon订单数据同步脚本\n";
                echo "用法: php ozon_order_sync.php [选项]\n";
                echo "选项:\n";
                echo "  --daemon         守护进程模式（默认）\n";
                echo "  --once           单次执行模式\n";
                echo "  --single         单进程模式（默认）\n";
                echo "  --multi          多进程模式\n";
                echo "  --processes N    设置最大进程数（默认4）\n";
                echo "  --interval N     设置同步间隔（秒，默认3600）\n";
                echo "  --months N       设置同步月数（默认12）\n";
                echo "  --help           显示此帮助信息\n";
                exit(0);
        }
    }
}

// 宝塔环境检测
if (isset($_SERVER['BT_PANEL'])) {
    writeLog('INFO', '检测到宝塔环境');
    $config['daemon_mode'] = true;
}

// 模式设置
if ($config['multi_process'] && !function_exists('pcntl_fork')) {
    writeLog('WARN', 'PCNTL扩展不可用，切换到单进程模式');
    $config['multi_process'] = false;
}

// 注册信号处理器
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, 'signalHandler');
    pcntl_signal(SIGINT, 'signalHandler');
    pcntl_signal(SIGHUP, 'signalHandler');
    if ($config['multi_process']) {
        pcntl_signal(SIGCHLD, 'childSignalHandler');
    }
}

/**
 * 主进程信号处理函数
 */
function signalHandler($signal) {
    global $running, $childProcesses;
    
    writeLog('INFO', '接收到信号', ['signal' => $signal]);
    
    switch ($signal) {
        case SIGTERM:
        case SIGINT:
            writeLog('INFO', '准备优雅退出');
            $running = false;
            
            // 终止所有子进程
            foreach ($childProcesses as $pid => $process) {
                writeLog('INFO', '终止子进程', ['pid' => $pid]);
                posix_kill($pid, SIGTERM);
            }
            
            // 等待子进程退出
            sleep(2);
            foreach ($childProcesses as $pid => $process) {
                if (posix_kill($pid, 0)) {
                    posix_kill($pid, SIGKILL);
                }
            }
            
            releaseGlobalLock();
            exit(0);
            break;
        case SIGHUP:
            writeLog('INFO', '重新加载配置');
            break;
    }
}

/**
 * 子进程信号处理函数
 */
function childSignalHandler($signal) {
    global $childProcesses;
    
    if ($signal === SIGCHLD) {
        // 处理子进程退出
        while (($pid = pcntl_waitpid(-1, $status, WNOHANG)) > 0) {
            if (isset($childProcesses[$pid])) {
                writeLog('DEBUG', '子进程退出', ['pid' => $pid, 'status' => $status]);
                unset($childProcesses[$pid]);
            }
        }
    }
}

/**
 * 日志记录函数
 */
function writeLog($level, $message, $data = null) {
    global $config;
    
    $levels = ['DEBUG' => 0, 'INFO' => 1, 'WARN' => 2, 'ERROR' => 3];
    $currentLevelValue = $levels[$config['log_level']] ?? 1;
    $messageLevelValue = $levels[$level] ?? 1;
    
    if ($messageLevelValue < $currentLevelValue) {
        return;
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $pid = getmypid();
    $logEntry = "[$timestamp] [PID:$pid] [$level] $message";
    
    if ($data) {
        $logEntry .= ' | Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($config['log_file'], $logEntry . "\n", FILE_APPEND | LOCK_EX);
    echo $logEntry . "\n";
}

/**
 * 清理旧日志文件
 */
function cleanOldLogs() {
    global $logDir;
    
    $files = glob($logDir . '/*.log');
    $cutoffTime = time() - (7 * 24 * 3600); // 7天前
    
    foreach ($files as $file) {
        if (filemtime($file) < $cutoffTime) {
            unlink($file);
            writeLog('INFO', '清理旧日志文件', ['file' => basename($file)]);
        }
    }
}

/**
 * API调用间隔控制
 */
function waitForApiInterval() {
    global $config, $logDir;
    
    $apiTimeFile = $logDir . '/api_last_call_order.time';
    
    if (file_exists($apiTimeFile)) {
        $lastCallTime = (int)file_get_contents($apiTimeFile);
        $timeSinceLastCall = time() - $lastCallTime;
        
        if ($timeSinceLastCall < $config['api_delay']) {
            $waitTime = $config['api_delay'] - $timeSinceLastCall;
            writeLog('DEBUG', "API调用间隔控制，等待 {$waitTime} 秒");
            sleep($waitTime);
        }
    }
    
    file_put_contents($apiTimeFile, time());
}

/**
 * 确保数据库表结构正确
 */
function ensureDatabaseTables() {
    global $DB;
    
    try {
        // 检查订单表是否存在必要字段
        $result = $DB->query("SHOW COLUMNS FROM t_order LIKE 'posting_number'");
        if (!$result || $DB->count($result) === 0) {
            writeLog('WARN', '订单表缺少posting_number字段');
        }
        
        writeLog('DEBUG', '数据库表结构检查完成');
        
    } catch (Exception $e) {
        writeLog('ERROR', '数据库表结构检查失败', ['error' => $e->getMessage()]);
        throw $e;
    }
}

/**
 * 主要同步执行函数
 */
function executeSync() {
    global $config;
    
    $startTime = time();
    writeLog('INFO', '开始订单数据同步任务', [
        'multi_process' => $config['multi_process'],
        'max_processes' => $config['max_processes'],
        'months_to_sync' => $config['months_to_sync']
    ]);
    
    try {
        // 确保数据库表结构
        ensureDatabaseTables();
        
        // 获取所有状态正常的店铺
        global $DB;
        $stores = $DB->getAll('SELECT * FROM `ozon_store` WHERE `apistatus` = 1');
        
        if (empty($stores)) {
            writeLog('WARN', '没有找到状态正常的店铺(apistatus=1)');
            return;
        }
        
        writeLog('INFO', '找到状态正常的店铺', ['count' => count($stores)]);
        
        // 根据配置选择执行模式
        if ($config['multi_process'] && count($stores) > 1) {
            $stats = executeMultiProcessSync($stores, $startTime);
        } else {
            $stats = executeSingleProcessSync($stores, $startTime);
        }
        
        $duration = time() - $startTime;
        writeLog('INFO', '订单数据同步任务完成', array_merge($stats, ['duration_seconds' => $duration]));
        
    } catch (Exception $e) {
        writeLog('ERROR', '订单数据同步任务执行失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    }
}

/**
 * 单进程同步执行
 */
function executeSingleProcessSync($stores, $startTime) {
    global $config;
    
    $totalStats = [
        'stores_processed' => 0,
        'total_orders' => 0,
        'orders_inserted' => 0,
        'orders_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    // 处理每个店铺
    foreach ($stores as $store) {
        if (time() - $startTime > $config['max_execution_time']) {
            writeLog('WARN', '达到最大执行时间，停止处理');
            break;
        }
        
        $storeStats = processSingleStore($store);
        
        // 累计统计
        foreach ($totalStats as $key => $value) {
            $totalStats[$key] += $storeStats[$key] ?? 0;
        }
    }
    
    return $totalStats;
}

/**
 * 多进程同步执行
 */
function executeMultiProcessSync($stores, $startTime) {
    global $config, $logDir, $childProcesses;
    
    writeLog('INFO', '启动多进程同步', ['max_processes' => $config['max_processes'], 'stores_count' => count($stores)]);
    
    $totalStats = [
        'stores_processed' => 0,
        'total_orders' => 0,
        'orders_inserted' => 0,
        'orders_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    // 将店铺分组分配给不同进程
    $storeChunks = array_chunk($stores, ceil(count($stores) / $config['max_processes']));
    $resultFiles = [];
    
    // 启动子进程
    foreach ($storeChunks as $index => $chunk) {
        if (empty($chunk)) continue;
        
        $resultFile = $logDir . "/process_result_{$index}_" . time() . ".json";
        $resultFiles[] = $resultFile;
        
        $pid = pcntl_fork();
        
        if ($pid == -1) {
            writeLog('ERROR', '无法创建子进程', ['chunk_index' => $index]);
            continue;
        } elseif ($pid == 0) {
            // 子进程 - 重置信号处理
            pcntl_signal(SIGTERM, SIG_DFL);
            pcntl_signal(SIGINT, SIG_DFL);
            pcntl_signal(SIGHUP, SIG_DFL);
            pcntl_signal(SIGCHLD, SIG_DFL);
            
            processStoreChunk($chunk, $resultFile, $index);
            exit(0);
        } else {
            // 父进程 - 记录子进程信息
            $childProcesses[$pid] = [
                'chunk_index' => $index,
                'start_time' => time(),
                'result_file' => $resultFile,
                'store_count' => count($chunk)
            ];
            writeLog('INFO', '启动子进程', ['pid' => $pid, 'chunk_index' => $index, 'stores' => count($chunk)]);
        }
    }
    
    // 等待所有子进程完成
    $completedProcesses = 0;
    $totalProcesses = count($childProcesses);
    
    while ($completedProcesses < $totalProcesses) {
        // 处理信号
        pcntl_signal_dispatch();
        
        $status = 0;
        $pid = pcntl_wait($status, WNOHANG);
        
        if ($pid > 0) {
            if (isset($childProcesses[$pid])) {
                $process = $childProcesses[$pid];
                $duration = time() - $process['start_time'];
                
                writeLog('INFO', '子进程完成', [
                    'pid' => $pid,
                    'chunk_index' => $process['chunk_index'],
                    'duration' => $duration,
                    'exit_status' => $status
                ]);
                
                // 读取子进程结果
                if (file_exists($process['result_file'])) {
                    $result = json_decode(file_get_contents($process['result_file']), true);
                    if ($result) {
                        foreach ($totalStats as $key => $value) {
                            $totalStats[$key] += $result[$key] ?? 0;
                        }
                    }
                    unlink($process['result_file']);
                }
                
                unset($childProcesses[$pid]);
                $completedProcesses++;
            }
        } elseif ($pid == 0) {
            // 检查超时的进程
            foreach ($childProcesses as $checkPid => $process) {
                if (time() - $process['start_time'] > $config['process_timeout']) {
                    writeLog('WARN', '子进程超时，强制终止', ['pid' => $checkPid, 'timeout' => $config['process_timeout']]);
                    posix_kill($checkPid, SIGTERM);
                    sleep(2);
                    if (posix_kill($checkPid, 0)) {
                        posix_kill($checkPid, SIGKILL);
                    }
                    unset($childProcesses[$checkPid]);
                    $completedProcesses++;
                }
            }
            usleep(100000); // 100ms
        } else {
            usleep(100000); // 100ms
        }
        
        // 检查总体超时
        if (time() - $startTime > $config['max_execution_time']) {
            writeLog('WARN', '达到最大执行时间，终止所有子进程');
            foreach ($childProcesses as $checkPid => $process) {
                posix_kill($checkPid, SIGTERM);
            }
            sleep(2);
            foreach ($childProcesses as $checkPid => $process) {
                if (posix_kill($checkPid, 0)) {
                    posix_kill($checkPid, SIGKILL);
                }
            }
            break;
        }
    }
    
    // 清理剩余的结果文件
    foreach ($resultFiles as $file) {
        if (file_exists($file)) {
            unlink($file);
        }
    }
    
    return $totalStats;
}

/**
 * 子进程处理店铺块
 */
function processStoreChunk($stores, $resultFile, $chunkIndex) {
    global $config, $DB;
    
    // 重新连接数据库（子进程需要独立连接）
    try {
        require_once __DIR__ . '/../includes/common.php';
    } catch (Exception $e) {
        writeLog('ERROR', '子进程数据库连接失败', ['chunk_index' => $chunkIndex, 'error' => $e->getMessage()]);
        file_put_contents($resultFile, json_encode(['errors' => 1]));
        return;
    }
    
    $stats = [
        'stores_processed' => 0,
        'total_orders' => 0,
        'orders_inserted' => 0,
        'orders_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    writeLog('INFO', '子进程开始处理店铺块', ['chunk_index' => $chunkIndex, 'stores_count' => count($stores)]);
    
    foreach ($stores as $store) {
        try {
            $storeStats = processSingleStore($store);
            
            // 累计统计
            foreach ($stats as $key => $value) {
                $stats[$key] += $storeStats[$key] ?? 0;
            }
            
        } catch (Exception $e) {
            writeLog('ERROR', '子进程处理店铺失败', [
                'chunk_index' => $chunkIndex,
                'store_id' => $store['id'],
                'error' => $e->getMessage()
            ]);
            $stats['errors']++;
        }
    }
    
    // 保存结果到文件
    file_put_contents($resultFile, json_encode($stats));
    writeLog('INFO', '子进程完成店铺块处理', array_merge(['chunk_index' => $chunkIndex], $stats));
}

/**
 * 处理单个店铺的订单数据
 */
function processSingleStore($store) {
    global $config, $DB;
    
    $storeStartTime = time();
    writeLog('INFO', "开始处理店铺订单数据: {$store['storename']}", ['store_id' => $store['id'], 'client_id' => $store['ClientId']]);
    
    $stats = [
        'stores_processed' => 1,
        'total_orders' => 0,
        'orders_inserted' => 0,
        'orders_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    try {
        // 创建API客户端
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        $jsonImporter = new \lib\JsonImporter(new \lib\PdoHelper($GLOBALS['dbconfig']));
        
        // 按月分批获取数据
        $currentDate = new \DateTime();
        for ($i = 0; $i < $config['months_to_sync']; $i++) {
            $endDate = clone $currentDate;
            $endDate->modify('last day of this month');
            $startDate = clone $currentDate;
            $startDate->modify('first day of this month');

            writeLog('INFO', '开始处理月份的订单数据', [
                'store_id' => $store['id'],
                'month' => $currentDate->format('Y-m'),
            ]);

            $monthStats = processMonthData($store, $client, $jsonImporter, $startDate, $endDate);

            // 累计月度统计数据
            $stats['total_orders'] += $monthStats['total_orders'];
            $stats['orders_inserted'] += $monthStats['orders_inserted'];
            $stats['orders_updated'] += $monthStats['orders_updated'];
            $stats['api_calls'] += $monthStats['api_calls'];
            $stats['errors'] += $monthStats['errors'];

            // 移至上一个月
            $currentDate->sub(new \DateInterval('P1M'));
        }
        
        $duration = time() - $storeStartTime;
        writeLog('INFO', "店铺订单数据处理完成: {$store['storename']}", array_merge($stats, ['duration_seconds' => $duration]));
        
    } catch (Exception $e) {
        writeLog('ERROR', "店铺订单数据处理失败: {$store['storename']}", ['error' => $e->getMessage()]);
        $stats['errors']++;
    }
    
    return $stats;
}

/**
 * 处理单个订单记录
 */
function processMonthData($store, $client, $jsonImporter, $startDate, $endDate) {
    global $config;

    $stats = [
        'total_orders' => 0,
        'orders_inserted' => 0,
        'orders_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];

        $offset = 0;
        $hasMore = true;

        while ($hasMore) {
            $filter = [
                'dir' => 'ASC',
                'limit' => $config['page_size'],
                'offset' => $offset,
                'since' => $startDate->format('Y-m-d\TH:i:s.v\Z'),
                'to' => $endDate->format('Y-m-d\TH:i:s.v\Z'),
                'status' => 'delivered',
                'with' => [
                    'analytics_data' => true,
                    'barcodes' => true,
                    'financial_data' => true,
                    'postings' => true,
                    'translit' => true
                ]
            ];

            try {
                waitForApiInterval();
                $response = $client->getFbsPostingListV3($filter);
                $stats['api_calls']++;

                if (isset($response['result']['postings']) && !empty($response['result']['postings'])) {
                    $postings = $response['result']['postings'];
                    $stats['total_orders'] += count($postings);

                    foreach ($postings as $posting) {
                        $importResult = $jsonImporter->importorder($posting, false, $store);
                        if (!empty($importResult['inserted'])) {
                            $stats['orders_inserted'] += $importResult['inserted'];
                        }
                        if (!empty($importResult['updated'])) {
                            $stats['orders_updated'] += $importResult['updated'];
                        }
                        if (!empty($importResult['errors'])) {
                            $stats['errors']++;
                            writeLog('ERROR', '订单导入失败', ['store_id' => $store['id'], 'posting_number' => $posting['posting_number'], 'error' => $importResult['errors']]);
                        }
                    }

                    if (count($postings) < $config['page_size']) {
                        $hasMore = false;
                    } else {
                        $offset += $config['page_size'];
                    }
                } else {
                    writeLog('INFO', '当前时间范围没有更多订单', ['store_id' => $store['id'], 'since' => $filter['since'], 'to' => $filter['to']]);
                    $hasMore = false;
                }
            } catch (Exception $e) {
                $stats['errors']++;
                writeLog('ERROR', 'API调用失败', ['store_id' => $store['id'], 'error' => $e->getMessage()]);
                $hasMore = false; // 发生错误时停止当前月份的同步
            }
        }

    return $stats;
}

/**
 * 处理多个订单记录
 */
function processOrderRecords($orders, $store, $jsonImporter) {
    $result = ['inserted' => 0, 'updated' => 0];
    foreach ($orders as $order) {
        $processResult = processOrder($order, $store, $jsonImporter);
        if ($processResult['success']) {
            if ($processResult['action'] === 'insert') {
                $result['inserted']++;
            } else {
                $result['updated']++;
            }
        }
    }
    return $result;
}

/**
 * 处理单个订单记录
 */
function processOrder($posting, $store, $jsonImporter) {
    try {
        // 提取基本信息
        $postingNumber = $posting['posting_number'] ?? '';
        
        if (empty($postingNumber)) {
            writeLog('WARN', "订单记录缺少posting_number");
            return ['success' => false, 'action' => 'skip'];
        }
        
        // 使用JsonImporter导入订单数据
        $result = $jsonImporter->importorder($posting, false, [
            'id' => $store['id'],
            'uid' => $store['uid']
        ]);
        
        if ($result) {
            writeLog('DEBUG', "订单处理成功", ['posting_number' => $postingNumber]);
            return ['success' => true, 'action' => 'insert'];
        } else {
            writeLog('WARN', "订单处理失败", ['posting_number' => $postingNumber]);
            return ['success' => false, 'action' => 'failed'];
        }
        
    } catch (Exception $e) {
        writeLog('ERROR', "处理订单记录异常", [
            'posting_number' => $posting['posting_number'] ?? 'unknown',
            'error' => $e->getMessage()
        ]);
        return ['success' => false, 'action' => 'exception'];
    }
}

/**
 * 守护进程循环
 */
function daemonLoop() {
    global $config, $running;
    
    writeLog('INFO', '启动订单数据同步守护进程', [
        'sync_interval' => $config['sync_interval'],
        'multi_process' => $config['multi_process'],
        'max_processes' => $config['max_processes']
    ]);
    
    while ($running) {
        try {
            executeSync();
            
            if ($running) {
                writeLog('INFO', "订单数据同步完成，等待下次执行", ['next_sync_in_seconds' => $config['sync_interval']]);
                sleep($config['sync_interval']);
            }
            
        } catch (Exception $e) {
            writeLog('ERROR', '守护进程执行异常', ['error' => $e->getMessage()]);
            sleep(60); // 出错时等待1分钟再重试
        }
        
        // 处理信号
        if (function_exists('pcntl_signal_dispatch')) {
            pcntl_signal_dispatch();
        }
    }
    
    writeLog('INFO', '订单数据同步守护进程正常退出');
}

// 主程序入口
try {
    writeLog('INFO', '程序启动', [
        'daemon_mode' => $config['daemon_mode'],
        'multi_process' => $config['multi_process'],
        'max_processes' => $config['max_processes'],
        'months_to_sync' => $config['months_to_sync']
    ]);

    if ($config['daemon_mode']) {
        // 守护进程循环
        while ($running) {
            executeSync();
            $sleepTime = $config['sync_interval'];
            writeLog('INFO', "任务周期完成，休眠 {$sleepTime} 秒");
            sleep($sleepTime);
        }
    } else {
        // 单次执行
        executeSync();
    }
} catch (Exception $e) {
    writeLog('ERROR', '程序执行失败', ['error' => $e->getMessage()]);
    exit(1);
}

writeLog('INFO', '订单数据同步程序执行完成');
?>
-- 设置打包员扣款配置脚本
-- 创建时间: 2025-08-27

-- 查看当前打包员配置（用于确认）
SELECT packerId, username, user_uid, deduction_per_order 
FROM packers 
WHERE user_uid IS NOT NULL AND user_uid != '';

-- 为指定打包员设置扣款金额（请根据实际情况修改packerId和金额）
-- 示例：为packerId=1的打包员设置每单扣款2.00元
UPDATE packers SET deduction_per_order = 2.00 WHERE packerId = 1;

-- 示例：为packerId=2的打包员设置每单扣款1.50元
-- UPDATE packers SET deduction_per_order = 1.50 WHERE packerId = 2;

-- 示例：为具有user_uid="10,10000,10079,10159"的打包员设置扣款金额
UPDATE packers SET deduction_per_order = 2.00 
WHERE user_uid LIKE '%10,10000,10079,10159%';

-- 验证配置结果
SELECT packerId, username, user_uid, deduction_per_order 
FROM packers 
WHERE deduction_per_order > 0;

-- 可选：为用户设置初始余额进行测试
-- UPDATE ozon_user SET balance = 100.00 WHERE uid IN (10, 10000, 10079, 10159);

-- 验证用户余额
SELECT uid, username, balance 
FROM ozon_user 
WHERE uid IN (10, 10000, 10079, 10159) 
ORDER BY balance DESC;
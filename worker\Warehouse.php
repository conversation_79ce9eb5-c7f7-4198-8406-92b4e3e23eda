<?php
// daily_sales_warehouse_processor.php

// 确保脚本通过命令行运行
if (php_sapi_name() !== 'cli') {
    die('本脚本只能通过命令行运行');
}

// 设置无限执行时间和足够的内存限制
set_time_limit(0);
ini_set('memory_limit', '1024M');

// 引入必要文件
require_once __DIR__ . '/../includes/common.php';

// 日志记录函数
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
}
$redis = new Redis();
try {
    $redis->connect('127.0.0.1', 6379);
    $redis->setOption(Redis::OPT_READ_TIMEOUT, 5); // 设置读取超时
    // 步骤1: 处理销售数据并更新Redis
    logMessage("开始处理销售数据...");
    $salesData = $DB->getAll("SELECT sku,COUNT(*) AS sales_volume FROM ozon_order WHERE status NOT IN ('cancelled', 'cancelled_from_split_pending') GROUP BY sku");
    $redisHashData = array_column($salesData, 'sales_volume', 'sku');
    
    // 批量写入Redis Hash
    $redis->hMSet('product_sales', $redisHashData);
    // 设置1小时缓存过期时间
    $redis->expire('product_sales', 86400);
    logMessage("销售数据处理完成，共更新 ".count($redisHashData)." 个SKU的销量数据");

    // 步骤2: 处理仓库数据
    logMessage("开始处理仓库数据...");
    $rs = $DB->getAll("SELECT * FROM `ozon_user` WHERE `warehouse_settings` IS NOT NULL");
    $totalWarehouses = count($rs);
    logMessage("共找到 {$totalWarehouses} 个需要处理的仓库");
    
    foreach ($rs as $warehouse) {
        $uid = $warehouse['uid'];
        if(empty(isset($warehouse['warehouse_settings']))){
            continue;
        }
        $warehouseSettings = json_decode($warehouse['warehouse_settings'], true);
        $up = false;
        
        foreach ($warehouseSettings as $i => $item) {
            $swarehouse = new \lib\Warehouse($item, $DB);
            
            if(empty($item['cookie'])) {
                logMessage("用户 {$uid} ，物流{$warehouseSettings[$i]['warehouse']}仓库 {$item['account']} 缺少cookie，尝试登录...");
                $cookie = $swarehouse->login();
                if($cookie) {
                    $warehouseSettings[$i]['cookie'] = $cookie;
                    $up = true;
                    logMessage("用户 {$uid} ，物流{$warehouseSettings[$i]['warehouse']}仓库 {$item['account']} 登录成功");
                    $swarehouse = new \lib\Warehouse($item, $DB);
                    $result = $swarehouse->order([$yesterday, date('Y-m-d')]);
                    logMessage("处理用户 {$uid} ，物流{$warehouseSettings[$i]['warehouse']}仓库 {$item['account']} 的订单数据，处理完成，一共处理了".$result['count']);
                } else {
                    logMessage("警告：用户 {$uid} ，物流{$warehouseSettings[$i]['warehouse']}仓库 {$item['account']} 登录失败");
                }
            } else {
                // 获取前一天的订单数据
                $yesterday = date('Y-m-d', strtotime('-1 day'));
                logMessage("处理用户 {$uid} ，物流{$warehouseSettings[$i]['warehouse']}仓库 {$item['account']} 的订单数据，日期: {$yesterday}");
                $result = $swarehouse->order([$yesterday, date('Y-m-d')]);
                logMessage("处理用户 {$uid} ，物流{$warehouseSettings[$i]['warehouse']}仓库 {$item['account']} 的订单数据，处理完成，一共处理了".$result['count']);
            }
        }
        
        if($up) {
            $DB->update('user', ['warehouse_settings'=>json_encode($warehouseSettings)], ['uid' => $uid]);
            logMessage("用户 {$uid} 的仓库设置已更新");
        }
    }
    
    logMessage("所有仓库数据处理完成");
    
} catch (Exception $e) {
    logMessage("错误发生: " . $e->getMessage());
    logMessage("堆栈跟踪: " . $e->getTraceAsString());
    exit(1); // 非零退出码表示错误
} finally {
    $redis->close();
}

logMessage("脚本执行完成");
exit(0);

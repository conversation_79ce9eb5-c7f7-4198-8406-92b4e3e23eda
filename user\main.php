<style>
    :root {
        --primary: #2c7be5;
        --secondary: #6e84a3;
        --success: #00d97e;
        --warning: #f6c343;
        --danger: #e63757;
        --border: #edf2f9;
    }
    body {background: #f9fbfd; color: #2a3547;}
    .dashboard-header {
        padding: 20px;
        background: #fff;
        margin: 20px;
        border-radius: 12px;
        box-shadow: 0 0 30px rgba(46,54,69,.03);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .data-card {
        background: #fff;
        border-radius: 12px;
        padding: 25px;
        margin: 10px;
        box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.03);
        border: 1px solid var(--border);
        transition: transform 0.3s;
    }
    .data-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.08);
    }
    .data-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
        margin-bottom: 15px;
    }
    .data-value {
        font-size: 28px;
        font-weight: 600;
        color: var(--primary);
        margin: 8px 0;
    }
    .data-label {
        color: var(--secondary);
        font-size: 14px;
        letter-spacing: 0.5px;
    }
    .chart-container {
        background: #fff;
        border-radius: 12px;
        padding: 20px;
        margin: 15px;
        border: 1px solid var(--border);
    }
    .layui-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.03);
    }
    .trend-indicator {
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
    }
    .trend-up {background: rgba(0,217,126,.1); color: var(--success);}
    .trend-down {background: rgba(230,55,87,.1); color: var(--danger);}
    
    /* 排行样式 */
    .rank-list {margin-bottom: 25px;}
    .rank-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--primary);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }
    .rank-title i {margin-right: 8px;}
    .rank-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px;
        background: #f8f9fc;
        border-radius: 6px;
        transition: transform 0.3s;
    }
    .rank-item:hover {
        transform: translateX(5px);
    }
    .rank-index {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--border);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 12px;
    }
    .progress-bar {
        height: 6px;
        background: var(--border);
        border-radius: 3px;
        margin-top: 4px;
        overflow: hidden;
    }
    .progress-value {
        height: 100%;
        border-radius: 3px;
        transition: width 0.5s ease;
    }
    .rank-value {
        font-weight: 600;
        margin-left: 10px;
        white-space: nowrap;
    }
    /* 时间切换 */
    .time-switch {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    .time-filter {
        display: flex;
        gap: 8px;
    }
    .time-btn {
        padding: 4px 12px;
        border-radius: 15px;
        border: 1px solid var(--border);
        cursor: pointer;
        transition: all 0.3s;
        font-size: 12px;
    }
    .time-btn.active {
        background: var(--primary);
        color: white;
        border-color: var(--primary);
    }
    .metric-select {
        border: 1px solid var(--border);
        border-radius: 4px;
        padding: 4px 8px;
    }
</style>

<div class="layui-fluid">
    <!-- 头部 -->
    <div class="dashboard-header">
        <div>
            <h2 style="margin:0;"><i class="fas fa-chart-pie"></i> 经营数据驾驶舱</h2>
            <span style="color: var(--secondary);font-size:14px;">更新时间：<span id="updateTime"></span></span>
        </div>
        <div class="layui-btn-group">
            <button class="layui-btn layui-btn-sm layui-btn-normal"><i class="fas fa-file-export"></i> 导出报表</button>
            <select id="currencyFormatSelect" class="layui-btn layui-btn-sm" style="height: 32px; line-height: 32px; padding: 0 10px; margin-left: 10px;">
                <option value="none" selected>默认金额格式</option>
                <option value="k">K (千)</option>
                <option value="w">W (万)</option>
                <option value="m">M (百万)</option>
            </select>
            <button class="layui-btn layui-btn-sm" id="refreshBtn"><i class="fas fa-sync-alt"></i> 刷新</button>
        </div>
    </div>

    <div class="layui-row layui-col-space15">
        <!-- 数据卡片 -->
        <div class="layui-col-md3">
            <div class="data-card">
                <div class="data-icon" style="background: linear-gradient(135deg, var(--primary), #4c9aff);">
                    <i class="fas fa-box"></i>
                </div>
                <div class="data-label">总订单量</div>
                <div class="data-value">0</div>
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up"></i> 0% (昨日)
                </div>
            </div>
        </div>

        <div class="layui-col-md3">
            <div class="data-card">
                <div class="data-icon" style="background: linear-gradient(135deg, var(--success), #00f2c3);">
                    <i class="fas fa-yen-sign"></i>
                </div>
                <div class="data-label">总销售额</div>
                <div class="data-value">¥0M</div>
                <div class="trend-indicator trend-down">
                    <i class="fas fa-arrow-down"></i> 0% (周同比)
                </div>
            </div>
        </div>

        <!-- Removed 总净利润 data card -->
 <div class="layui-col-md3">
            <div class="data-card">
                <div class="data-icon" style="background: linear-gradient(135deg, var(--warning), #ffd66e);">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="data-label">暂不开发</div>
                <div class="data-value">¥0K</div>
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up"></i> 0% (月同比)
                </div>
            </div>
        </div>
        
        <div class="layui-col-md3">
            <div class="data-card">
                <div class="data-icon" style="background: linear-gradient(135deg, var(--danger), #ff708d);">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="data-label">总采购金额</div>
                <div class="data-value">¥0M</div>
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up"></i> 0% (环比)
                </div>
            </div>
        </div>
       

        <!-- 主图表 -->
        <div class="layui-col-md8">
            <div class="chart-container">
                <div style="display: flex; justify-content: flex-end; margin-bottom: 15px;">
                    <div class="layui-btn-group" id="chartSwitch">
                        <button class="layui-btn layui-btn-sm active" data-type="sales">销售额</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" data-type="purchase">采购额</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" data-type="orders">订单量</button>
                    </div>
                </div>
                <div id="mainChart" style="height: 400px;"></div>
            </div>
        </div>

        <!-- 右侧排行 -->
        <div class="layui-col-md4">
            <div class="chart-container">
        <div class="time-switch">
            
            <div class="time-filter">
                <div class="time-btn active" data-range="today">今日</div>
                <div class="time-btn" data-range="7d">7日</div>
                <div class="time-btn" data-range="14d">14日</div>
                <div class="time-btn" data-range="month">本月</div>
                <div class="time-btn" data-range="30d">近30天</div>
                <div class="time-btn" data-range="lastmonth">上个月</div>
            </div>
        </div>
                <div id="rankContainer"></div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-col-md12">
            <div class="chart-container">
                <div style="margin-bottom: 10px;">
                    <label for="tableTimeRange">选择时间范围：</label>
                    <select id="tableTimeRange" style="padding: 4px 8px; border-radius: 4px; border: 1px solid var(--border);">
                        <option value="7d" selected>近7日</option>
                        <option value="today">今日</option>
                        <option value="14d">近14日</option>
                        <option value="month">本月</option>
                        <option value="30d">近30天</option>
                        <option value="lastmonth">上个月</option>
                        <option value="custom">自定义</option>
                    </select>
                    <input type="date" id="customStartDate" style="display:none; margin-left: 10px;">
                    <input type="date" id="customEndDate" style="display:none; margin-left: 5px;">
                    <button id="customDateApply" style="display:none; margin-left: 5px; padding: 4px 8px;">应用</button>
                </div>
                <table class="layui-table" lay-filter="dataTable">
                    <thead>
                        <tr>
                            <th lay-data="{field:'date'}">日期</th>
                            <th lay-data="{field:'orders'}">订单量</th>
                            <th lay-data="{field:'cancelled'}">取消订单</th>
                            <th lay-data="{field:'sales'}">销售额</th>
                            <th lay-data="{field:'purchase'}">采购额</th>
                <!-- Removed 利润率 column -->
                            <th lay-data="{field:'trend'}">趋势</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <tr>
                            <td>2025-04-01</td>
                            <td>1,245</td>
                            <td>¥356,200</td>
                            <td>¥128,500</td>
                            <td><span class="trend-indicator trend-up">28.6%</span></td>
                            <td><i class="fas fa-arrow-up trend-up"></i></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="/assets/js/echarts.min.js"></script>
<script>
layui.use(['table', 'jquery', 'layer'], function(){
    var table = layui.table;
    var $ = layui.$;
    var layer = layui.layer;

    // 主图表初始化
    var mainChart = echarts.init(document.getElementById('mainChart'));

    // 核心数据
    var chartData = {
        dates: [],
        sales: [],
        purchase: [],
        orders: []
    };

    // 店铺数据
    var shopData = [];

    // 获取汇总数据
    function fetchDashboardSummary(range) {
        $.ajax({
            url: 'ajax.php',
            method: 'GET',
            data: { act: 'dashboard_summary', range: range },
            dataType: 'json',
            success: function(res) {
                if (res.code === 0) {
                    var data = res.data;
                    data.totalOrders = Number(data.totalOrders) || 0;
                    $('.data-card').eq(0).find('.data-value').text(data.totalOrders.toLocaleString());
                    $('.data-card').eq(1).find('.data-value').text(formatRMB(data.totalSales));
                    $('.data-card').eq(2).find('.data-value').text(formatRMB(data.totalProfit));
                    $('.data-card').eq(3).find('.data-value').text(formatRMB(data.totalPurchase));

                    // Update trend indicators with real data
                    if(data.trends) {
                        var ordersTrends = data.trends.orders;
                        var salesTrends = data.trends.sales;
                        var purchaseTrends = data.trends.purchase;

                        // Orders card trend (yesterday)
                        var ordersTrendText = (ordersTrends.yesterday >= 0 ? '<i class="fas fa-arrow-up trend-up"></i> ' : '<i class="fas fa-arrow-down trend-down"></i> ') + Math.abs(ordersTrends.yesterday).toFixed(2) + '% (昨日)';
                        $('.data-card').eq(0).find('.trend-indicator').html(ordersTrendText);

                        // Sales card trend (week-over-week)
                        var salesTrendText = (salesTrends.week >= 0 ? '<i class="fas fa-arrow-up trend-up"></i> ' : '<i class="fas fa-arrow-down trend-down"></i> ') + Math.abs(salesTrends.week).toFixed(2) + '% (周同比)';
                        $('.data-card').eq(1).find('.trend-indicator').html(salesTrendText);

                        // Purchase card trend (month-over-month)
                        var purchaseTrendText = (purchaseTrends.month >= 0 ? '<i class="fas fa-arrow-up trend-up"></i> ' : '<i class="fas fa-arrow-down trend-down"></i> ') + Math.abs(purchaseTrends.month).toFixed(2) + '% (环比)';
                        $('.data-card').eq(3).find('.trend-indicator').html(purchaseTrendText);
                    }

                    // Update chartData with summary data if available
                    if (data.dates) {
                        console.log('Received dates:', data.dates);
                        chartData.dates = data.dates;
                    }
                    if (data.sales) {
                        chartData.sales = data.sales;
                    }
                    if (data.purchase) {
                        chartData.purchase = data.purchase;
                    }
                    if (data.orders) {
                        chartData.orders = data.orders;
                    }
                    console.log('baseOption before setOption:', baseOption);
                    switchMainChart(currentType);
                }
            }
        });
    }

    // 获取排行数据
    function fetchDashboardRankings(metric, range) {
        $.ajax({
            url: 'ajax.php',
            method: 'GET',
            data: { act: 'dashboard_rankings', metric: metric, range: range },
            dataType: 'json',
            success: function(res) {
                if (res.code === 0) {
                    shopData = [];
                    for(var i=0; i<res.data.length; i++){
                        var item = res.data[i];
                        var obj = {name: item.name};
                        obj[metric] = item.value;
                        shopData.push(obj);
                    }
                    generateRank();
                }
            }
        });
    }

    // 获取数据表格数据
    function fetchDashboardTableData(range, startDate, endDate) {
        var data = { act: 'dashboard_table_data' };
        if(range === 'custom') {
            data.startDate = startDate;
            data.endDate = endDate;
        } else {
            data.range = range;
        }
        $.ajax({
            url: 'ajax.php',
            method: 'GET',
            data: data,
            dataType: 'json',
            success: function(res) {
                if(res.code === 0) {
                    updateTableBody(res.data);
                } else {
                    $('#dataTableBody').html('<tr><td colspan="6">无数据</td></tr>');
                }
            },
            error: function() {
                $('#dataTableBody').html('<tr><td colspan="6">请求失败</td></tr>');
            }
        });
    }

    // 更新表格tbody内容
    function updateTableBody(data) {
        var html = '';
        data.forEach(function(item) {
            var trendClass = item.trend >= 0 ? 'trend-up' : 'trend-down';
            var trendIcon = item.trend >= 0 ? '<i class="fas fa-arrow-up trend-up"></i>' : '<i class="fas fa-arrow-down trend-down"></i>';
            var trendValue = Math.abs(item.trend).toFixed(2) + '%';
            
            html += '<tr>' +
                '<td>' + item.date + '</td>' +
                '<td>' + item.orders.toLocaleString() + '</td>' +
                '<td>' + item.cancelled.toLocaleString() + '</td>' +
                '<td>¥' + item.sales.toLocaleString() + '</td>' +
                '<td>¥' + item.purchase.toLocaleString() + '</td>' +
                <!-- Removed 利润率 data cell -->
                '<td>' + trendIcon + ' ' + trendValue + '</td>' +
                '</tr>';
        });
        $('#dataTableBody').html(html);
    }

    // 绑定时间范围选择事件
    $('#tableTimeRange').on('change', function() {
        var val = $(this).val();
        if(val === 'custom') {
            $('#customStartDate, #customEndDate, #customDateApply').show();
        } else {
            $('#customStartDate, #customEndDate, #customDateApply').hide();
            fetchDashboardTableData(val);
        }
    });

    $('#customDateApply').on('click', function() {
        var startDate = $('#customStartDate').val();
        var endDate = $('#customEndDate').val();
        if(!startDate || !endDate) {
            alert('请选择开始和结束日期');
            return;
        }
        if(startDate > endDate) {
            alert('开始日期不能晚于结束日期');
            return;
        }
        fetchDashboardTableData('custom', startDate, endDate);
    });

    // 初始化表格数据
    fetchDashboardTableData('7d');

    // 主图表配置
    var currentType = 'sales';
    var baseOption = {
        title: {
            text: '近7日数据趋势',
            left: 'center',
            textStyle: {color: '#2a3547'}
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + params[0].seriesName + '：' + formatMainValue(params[0].value);
            }
        },
        xAxis: {
            type: 'category',
            data: chartData.dates,
            axisLine: {lineStyle: {color: 'var(--border)'}},
            axisLabel: {
                color: 'var(--secondary)',
                formatter: function(value) {
                    // Debug: log the value to console
                    console.log('xAxis label value:', value);
                    // Expecting value in 'YYYY-MM-DD' format, parse manually
                    var parts = value.split('-');
                    if(parts.length === 3){
                        return parts[1] + '-' + parts[2];
                    }
                    return value;
                }
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {show: false},
            axisLabel: {
                color: 'var(--secondary)',
                formatter: function(value) {
                    return formatMainValue(value);
                }
            },
            splitLine: {lineStyle: {color: 'var(--border)'}}
        },
        series: [{
            name: '销售额',
            type: 'bar',
            data: chartData.sales,
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {offset: 0, color: '#2c7be5'},
                    {offset: 1, color: '#6c8ebf'}
                ]),
                borderRadius: [6, 6, 0, 0]
            },
            barWidth: '60%',
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(44,123,229,0.5)'
                }
            }
        }],
        grid: {
            left: '3%',
            right: '3%',
            bottom: '10%',
            containLabel: true
        }
    };

    var currentCurrencyFormat = 'none';

    // 人民币格式化函数
    function formatRMB(value) {
        value = Number(value) || 0;
        if(currentCurrencyFormat === 'none') {
            return '¥' + value.toLocaleString();
        } else if(currentCurrencyFormat === 'k') {
            return '¥' + (value / 1000).toFixed(1) + 'K';
        } else if(currentCurrencyFormat === 'w') {
            return '¥' + (value / 10000).toFixed(1) + 'W';
        } else if(currentCurrencyFormat === 'm') {
            return '¥' + (value / 1000000).toFixed(1) + 'M';
        } else {
            return '¥' + value.toLocaleString();
        }
    }

    // 监听金额格式选择变化
    $('#currencyFormatSelect').on('change', function() {
        currentCurrencyFormat = $(this).val();
        // 重新加载数据以应用格式
        fetchDashboardSummary(currentFilter.range);
        fetchDashboardRankings(currentFilter.metric, currentFilter.range);
        fetchDashboardTableData($('#tableTimeRange').val());
    });

    // 主图表数值格式化
    function formatMainValue(value) {
        value = Number(value) || 0;
        switch(currentType) {
            case 'sales':
            case 'purchase':
                return formatRMB(value);
            case 'orders':
                return value.toLocaleString();
            default:
                return value;
        }
    }

    // 主图表切换
    function switchMainChart(type) {
        currentType = type;
        var titleMap = { 
            sales: '销售额', 
            purchase: '采购额', 
            orders: '订单量' 
        };
        
        baseOption.series[0].data = chartData[type];
        baseOption.series[0].name = titleMap[type];
        baseOption.title.text = '近7日' + titleMap[type] + '趋势';
        baseOption.xAxis.data = chartData.dates; // Ensure xAxis data is updated
        mainChart.setOption(baseOption, true);
        mainChart.resize(); // Force chart resize to update layout
    }

    // 排行系统
    var metricConfig = {
        sales: {
            title: '销售额',
            formatter: function(val) { return formatRMB(val); },
            color: 'linear-gradient(90deg, #2c7be5, #4c9aff)'
        },
        purchase: {
            title: '采购额',
            formatter: function(val) { return formatRMB(val); },
            color: 'linear-gradient(90deg, #e63757, #ff708d)'
        },
        orders: {
            title: '订单量',
            formatter: function(val) { return val.toLocaleString(); },
            color: 'linear-gradient(90deg, #00d97e, #00f2c3)'
        },
        profit: {
            title: '利润',
            formatter: function(val) { return formatRMB(val); },
            color: 'linear-gradient(90deg, #f6c343, #ffd66e)'
        }
    };

    var currentFilter = {
        metric: 'sales',
        range: 'today'
    };

    // 生成排行榜
    function generateRank() {
        var metric = currentFilter.metric;
        var range = currentFilter.range;
        var config = metricConfig[metric];
        
        var sortedData = shopData.slice().sort(function(a, b) {
            return b[metric] - a[metric];
        });
        
        var maxValue = Math.max.apply(null, sortedData.map(function(item) { return item[metric]; }));
        var html = sortedData.map(function(item, index) {
            return '<div class="rank-item">' +
                '<div class="rank-index">' + (index + 1) + '</div>' +
                '<div class="rank-item-content">' +
                    '<div style="display: flex; justify-content: space-between;">' +
                        '<span>' + item.name + '</span>' +
                        '<span class="rank-value">' + config.formatter(item[metric]) + '</span>' +
                    '</div>' +
                    '<div class="progress-bar">' +
                        '<div class="progress-value" style="width: ' + (item[metric]/maxValue)*100 + '%; background: ' + config.color + ';"></div>' +
                    '</div>' +
                '</div>' +
            '</div>';
        }).join('');

        $('#rankContainer').html(html);
    }

    // 事件处理
    $('#chartSwitch button').on('click', function(){
        var type = $(this).data('type');
        $('#chartSwitch button').removeClass('active').addClass('layui-btn-primary');
        $(this).addClass('active').removeClass('layui-btn-primary');
        switchMainChart(type);
        // Also update currentFilter.metric and fetch rankings for the new metric
        currentFilter.metric = type;
        fetchDashboardRankings(currentFilter.metric, currentFilter.range);
    });

    

    // Mapping for time range display text
    var rangeTextMap = {
        today: '今日',
        '7d': '近7日',
        '14d': '近14日',
        month: '本月',
        '30d': '近30日',
        lastmonth: '上个月'
    };

    $('.time-btn').on('click', function(){
        $('.time-btn').removeClass('active');
        $(this).addClass('active');
        currentFilter.range = $(this).data('range');
        fetchDashboardRankings(currentFilter.metric, currentFilter.range);
        fetchDashboardSummary(currentFilter.range);

        // Update main chart title dynamically based on selected range and current metric
        var titleMap = { 
            sales: '销售额', 
            purchase: '采购额', 
            orders: '订单量' 
        };
        var rangeText = rangeTextMap[currentFilter.range] || '';
        baseOption.title.text = rangeText + titleMap[currentFilter.metric] + '趋势';
        mainChart.setOption(baseOption, true);
    });

    // 时间更新
    function updateTime() {
        $('#updateTime').text(new Date().toLocaleString());
    }
    setInterval(updateTime, 1000);

    // 初始化
    mainChart.setOption(baseOption);
    fetchDashboardSummary('7d');
    fetchDashboardRankings(currentFilter.metric, currentFilter.range);
    updateTime();

    // 响应式处理
    window.addEventListener('resize', function() {
        mainChart.resize();
    });
});
</script>

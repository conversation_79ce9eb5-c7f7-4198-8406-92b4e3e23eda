<div class="layui-container" style="margin-top: 10px;">
  <!-- 页面说明 -->
  <div class="layui-card" style="margin-bottom: 8px;">
    <div class="layui-card-body" style="padding: 8px; background-color: #f8f9fa;">
      <div style="color: #666; line-height: 1.4; font-size: 13px;">
        <i class="layui-icon layui-icon-tips" style="color: #1890ff; margin-right: 5px;"></i>
        <strong>系统设置说明：</strong>
        本页面所有配置项均为可选设置，您可以根据实际需要选择性填写。
        <span style="margin-left: 10px;">• 仓库设置：配置跨境巴士仓库信息</span>
        <span style="margin-left: 10px;">• 翻译密钥：配置各种翻译服务的API密钥</span>
        <span style="margin-left: 10px;">• 物流设置：配置默认物流商和计费规则</span>
      </div>
    </div>
  </div>
  
  <form class="layui-form">
   <!-- 仓库设置 -->
    <div class="layui-card">
      <div class="layui-card-header">
        仓库设置
        <span style="color: #999; font-size: 12px; margin-left: 10px;">(可选配置，不填写也可以正常使用)</span>
      </div>
      <div class="layui-card-body">
        <div class="warehouse-list">
          <div class="warehouse-item">
            <div class="layui-form-item">
              <label class="layui-form-label">选择仓库</label>
              <div class="layui-input-inline">
                <select name="warehouse">
                  <option value="">请选择仓库</option>
                  <option value="kuajing84">跨境巴士</option>
                  <option value="celdt">CEL仓库</option>
                </select>
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">仓库账号</label>
              <div class="layui-input-inline">
                <input type="text" name="account" placeholder="请输入账号" class="layui-input">
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">仓库密码</label>
              <div class="layui-input-inline">
                <input type="password" name="password" placeholder="请输入密码" class="layui-input">
              </div>
              <button type="button" class="layui-btn layui-btn-danger layui-btn-sm remove-warehouse">删除</button>
            </div>
          </div>
        </div>
        <button type="button" class="layui-btn layui-btn-primary" id="addWarehouse">+ 添加仓库</button>
      </div>
    </div>

    <!-- 翻译服务密钥设置 -->
    <div class="layui-card" style="margin-top: 8px;">
      <div class="layui-card-header">
        象寄翻译服务密钥设置
        <span style="color: #999; font-size: 12px; margin-left: 10px;">(可选配置，根据需要填写)</span>
        <a href="https://www.xiangjifanyi.com/console/register-invite/963e9e201d31f548" target="_blank" class="layui-btn layui-btn-sm layui-btn-normal" style="margin-left: 15px;">
          <i class="layui-icon layui-icon-user"></i> 立即注册获取
        </a>
      </div>
      <div class="layui-card-body">
        <div class="layui-form-item">
          <label class="layui-form-label">用户密钥</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="user_key" placeholder="请输入用户密钥" class="layui-input">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">阿里标识码</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="ali_code" placeholder="请输入阿里标识码" class="layui-input">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">谷歌标识码</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="google_code" placeholder="请输入谷歌标识码" class="layui-input">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">Papago标识码</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="papago_code" placeholder="请输入Papago标识码" class="layui-input">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">DeepL标识码</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="deepl_code" placeholder="请输入DeepL标识码" class="layui-input">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">ChatGPT标识码</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="chatgpt_code" placeholder="请输入ChatGPT标识码" class="layui-input">
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">百度标识码</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="baidu_code" placeholder="请输入百度标识码" class="layui-input">
          </div>
        </div>
      </div>
    </div>

    <!-- 默认物流计算设置 -->
    <div class="layui-card" style="margin-top: 8px;">
      <div class="layui-card-header">
        默认物流计算设置
        <span style="color: #999; font-size: 12px; margin-left: 10px;">(可选配置，可以稍后设置)</span>
      </div>
      <div class="layui-card-body">
        <div class="layui-form-item">
          <label class="layui-form-label">物流商</label>
          <div class="layui-input-inline" style="width: 300px;">
            <select name="logistics_providers" xm-select="providers" multiple lay-search>
             <option value="guoo">物流商1 - GUOO</option>
                <option value="ural">物流商2 - Ural</option>
                <option value="uni">物流商3 - UNI</option>
                <option value="cel">物流商4 - CEL</option>
                <option value="oyx">物流商5 - OYX</option>
                <option value="rets">物流商6 - RETS</option>
                <option value="atc">物流商7 - ATC</option>
                <option value="tanais">物流商8 - Tanais</option>
                <option value="abt">物流商9 - ABT</option>
                <option value="xy">物流商10 - XY</option>
                <option value="leader">物流商11 - Leader</option>
                <option value="zto">物流商12 - ZTO</option>
                <option value="iml">物流商13 - IML</option>
                <option value="gbs">物流商14 - GBS</option>
                <option value="china_post">物流商15 - China Post</option>
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">物流方式</label>
          <div class="layui-input-inline" style="width: 300px;">
            <select name="logistics_methods" xm-select="methods" multiple lay-search>
              <option value="Express">空运Express</option>
              <option value="Standard">陆空Standard</option>
              <option value="Economy">陆运Economy</option>
              <option value="ALL">默认全选</option>
            </select>
          </div>
        </div>

       <div class="layui-form-item">
          <label class="layui-form-label">贴单费用</label>
          <div class="layui-input-inline" style="width: 150px;">
            <input type="number" name="label_fee" step="0.01" placeholder="请输入贴单费用" class="layui-input">
          </div>
          <div class="layui-form-mid">元</div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">提现手续费 (%)</label>
          <div class="layui-input-inline" style="width: 150px;">
            <input type="number" name="withdraw_fee" step="0.01" placeholder="请输入提现手续费百分比" class="layui-input">
          </div>
          <div class="layui-form-mid">%</div>
        </div>
      </div>
    </div>

    <!-- 水印设置 -->
    <div class="layui-card" style="margin-top: 8px;">
      <div class="layui-card-header">
        水印设置
        <span style="color: #999; font-size: 12px; margin-left: 10px;">(可选配置，用于商品图片水印)</span>
      </div>
      <div class="layui-card-body">
        <!-- 文字水印设置 -->
        <div class="layui-form-item">
          <label class="layui-form-label">文字水印</label>
          <div class="layui-input-inline" style="width: 300px;">
            <input type="text" name="watermark_text" placeholder="请输入水印文字" class="layui-input">
          </div>
        </div>

        <!-- 水印位置 -->
        <div class="layui-form-item">
          <label class="layui-form-label">水印位置</label>
          <div class="layui-input-inline" style="width: 200px;">
            <select name="watermark_position">
              <option value="">请选择位置</option>
              <option value="top-left">左上角</option>
              <option value="top-right">右上角</option>
              <option value="bottom-left">左下角</option>
              <option value="bottom-right">右下角</option>
              <option value="center">居中</option>
            </select>
          </div>
        </div>

        <!-- 字体颜色 -->
        <div class="layui-form-item">
          <label class="layui-form-label">字体颜色</label>
          <div class="layui-input-inline" style="width: 100px;">
            <input type="color" name="watermark_color" value="#000000" class="layui-input">
          </div>
          <div class="layui-form-mid">选择字体颜色</div>
        </div>

        <!-- 透明度 -->
        <div class="layui-form-item">
          <label class="layui-form-label">透明度</label>
          <div class="layui-input-inline" style="width: 150px;">
            <input type="range" name="watermark_opacity" min="0" max="100" value="50" class="layui-input" style="height: 38px;">
          </div>
          <div class="layui-form-mid"><span id="opacity-value">50</span>%</div>
        </div>

        <!-- Logo上传 -->
        <div class="layui-form-item">
          <label class="layui-form-label">Logo水印</label>
          <div class="layui-input-inline" style="width: 300px;">
            <button type="button" class="layui-btn" id="watermark-logo-upload">
              <i class="layui-icon layui-icon-upload"></i>上传Logo
            </button>
            <div class="layui-upload-list" style="margin-top: 10px;">
              <img class="layui-upload-img" id="watermark-logo-preview" style="width: 100px; height: 100px; object-fit: contain; border: 1px solid #e6e6e6; display: none;">
              <p id="watermark-logo-text" style="margin: 5px 0; color: #999; font-size: 12px;">支持jpg、png格式，建议尺寸不超过200x200px</p>
            </div>
            <input type="hidden" name="watermark_logo" id="watermark-logo-path">
          </div>
        </div>

        <!-- Logo位置 -->
        <div class="layui-form-item">
          <label class="layui-form-label">Logo位置</label>
          <div class="layui-input-inline" style="width: 200px;">
            <select name="watermark_logo_position">
              <option value="">请选择位置</option>
              <option value="top-left">左上角</option>
              <option value="top-right">右上角</option>
              <option value="bottom-left">左下角</option>
              <option value="bottom-right">右下角</option>
              <option value="center">居中</option>
            </select>
          </div>
        </div>

        <!-- Logo透明度 -->
        <div class="layui-form-item">
          <label class="layui-form-label">Logo透明度</label>
          <div class="layui-input-inline" style="width: 150px;">
            <input type="range" name="watermark_logo_opacity" min="0" max="100" value="80" class="layui-input" style="height: 38px;">
          </div>
          <div class="layui-form-mid"><span id="logo-opacity-value">80</span>%</div>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="layui-form-item" style="margin-top: 10px;">
      <div class="layui-input-block">
        <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
        <button type="button" class="layui-btn layui-btn-primary" id="resetForm">重置表单</button>
      </div>
    </div>
  </form>
</div>

<script src="/assets/js/jquery-3.6.0.min.js"></script>
<script src="/assets/component/layui/layui.js"></script>
<script src="/assets/formSelects/formSelects-v4.js"></script>

<script>
layui.use(['form', 'layer', 'upload'], function(){
  var form = layui.form;
  var layer = layui.layer;
  var upload = layui.upload;
  var $ = layui.jquery;
  var formSelects = layui.formSelects;

  // 初始化多选组件
  formSelects.render('providers');
  formSelects.render('methods');

  // 透明度滑块实时显示
  $('input[name="watermark_opacity"]').on('input', function() {
    $('#opacity-value').text($(this).val());
  });

  $('input[name="watermark_logo_opacity"]').on('input', function() {
    $('#logo-opacity-value').text($(this).val());
  });

  // Logo上传功能
  upload.render({
    elem: '#watermark-logo-upload',
    url: 'ajax.php?act=upload_watermark_logo',
    accept: 'images',
    acceptMime: 'image/jpg,image/jpeg,image/png',
    size: 2048, // 2MB
    done: function(res){
      if(res.code === 0){
        $('#watermark-logo-preview').attr('src', res.data.url).show();
        $('#watermark-logo-path').val(res.data.path);
        $('#watermark-logo-text').text('上传成功');
        layer.msg('Logo上传成功', {icon: 1});
      } else {
        layer.msg(res.msg || 'Logo上传失败', {icon: 2});
      }
    },
    error: function(){
      layer.msg('Logo上传失败，请重试', {icon: 2});
    }
  });

  // 页面加载时获取现有的配置
  $.ajax({
    url: 'ajax.php?act=get_user_settings',
    type: 'GET',
    dataType: 'json',
    success: function(res) {
      if (res.code === 0 && res.data) {
        // 填充翻译密钥数据
        if (res.data.translation_keys) {
          $('input[name="user_key"]').val(res.data.translation_keys.user_key || '');
          $('input[name="ali_code"]').val(res.data.translation_keys.ali_code || '');
          $('input[name="google_code"]').val(res.data.translation_keys.google_code || '');
          $('input[name="papago_code"]').val(res.data.translation_keys.papago_code || '');
          $('input[name="deepl_code"]').val(res.data.translation_keys.deepl_code || '');
          $('input[name="chatgpt_code"]').val(res.data.translation_keys.chatgpt_code || '');
          $('input[name="baidu_code"]').val(res.data.translation_keys.baidu_code || '');
        }
        
        // 填充仓库设置数据
        if (res.data.warehouse_settings && res.data.warehouse_settings.length > 0) {
          $('.warehouse-list').empty();
          res.data.warehouse_settings.forEach(function(warehouse) {
            addWarehouseItem(warehouse.warehouse, warehouse.account, warehouse.password);
          });
        } else {
            // 确保至少有一个空的仓库项
            if ($('.warehouse-item').length === 0) {
                addWarehouseItem('', '', '');
            }
        }
        
        // 填充物流设置数据
        if (res.data.logistics_settings) {
          if (res.data.logistics_settings.providers) {
            formSelects.value('providers', res.data.logistics_settings.providers);
          }
          if (res.data.logistics_settings.methods) {
            formSelects.value('methods', res.data.logistics_settings.methods);
          }
          $('input[name="label_fee"]').val(res.data.logistics_settings.label_fee || '');
          $('input[name="withdraw_fee"]').val(res.data.logistics_settings.withdraw_fee || '');
        }
        
        // 填充水印设置数据
        if (res.data.watermark_settings) {
          $('input[name="watermark_text"]').val(res.data.watermark_settings.watermark_text || '');
          $('select[name="watermark_position"]').val(res.data.watermark_settings.watermark_position || '');
          $('input[name="watermark_color"]').val(res.data.watermark_settings.watermark_color || '#000000');
          $('input[name="watermark_opacity"]').val(res.data.watermark_settings.watermark_opacity || '50');
          $('#opacity-value').text(res.data.watermark_settings.watermark_opacity || '50');
          
          if (res.data.watermark_settings.watermark_logo) {
            $('#watermark-logo-path').val(res.data.watermark_settings.watermark_logo);
            $('#watermark-logo-preview').attr('src', res.data.watermark_settings.watermark_logo_url || res.data.watermark_settings.watermark_logo).show();
            $('#watermark-logo-text').text('已上传Logo');
          }
          $('select[name="watermark_logo_position"]').val(res.data.watermark_settings.watermark_logo_position || '');
          $('input[name="watermark_logo_opacity"]').val(res.data.watermark_settings.watermark_logo_opacity || '80');
          $('#logo-opacity-value').text(res.data.watermark_settings.watermark_logo_opacity || '80');
        }
        
        form.render(); // 重新渲染表单
      }
    }
  });

  // 添加仓库项目的函数
  function addWarehouseItem(warehouse, account, password) {
    var warehouseHtml = `
      <div class="warehouse-item">
        <div class="layui-form-item">
          <label class="layui-form-label">选择仓库</label>
          <div class="layui-input-inline">
            <select name="warehouse">
              <option value="">请选择仓库</option>
              <option value="kuajing84" ${warehouse == 'kuajing84' ? 'selected' : ''}>跨境巴士</option>
              <option value="celdt" ${warehouse == 'celdt' ? 'selected' : ''}>CEL仓库</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">仓库账号</label>
          <div class="layui-input-inline">
            <input type="text" name="account" placeholder="请输入账号" class="layui-input" value="${account || ''}">
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">仓库密码</label>
          <div class="layui-input-inline">
            <input type="password" name="password" placeholder="请输入密码" class="layui-input" value="${password || ''}">
          </div>
          <button type="button" class="layui-btn layui-btn-danger layui-btn-sm remove-warehouse">删除</button>
        </div>
      </div>
    `;
    $('.warehouse-list').append(warehouseHtml);
    form.render('select');
  }

  // 添加仓库按钮事件
  $('#addWarehouse').click(function() {
    addWarehouseItem('', '', '');
  });

  // 删除仓库按钮事件
  $(document).on('click', '.remove-warehouse', function() {
    var $item = $(this).closest('.warehouse-item');
    layer.confirm('确定要删除这个仓库设置吗？', {
      icon: 3,
      title: '确认删除'
    }, function(index) {
      $item.remove();
      layer.close(index);
      layer.msg('仓库设置已删除', {icon: 1, time: 1000});
    });
  });
  
  // 重置表单按钮事件
  $('#resetForm').click(function() {
    layer.confirm('确定要重置所有设置吗？这将清空当前填写的所有内容。', {
      icon: 3,
      title: '确认重置'
    }, function(index) {
      $('.layui-form')[0].reset();
      $('.warehouse-list').empty();
      addWarehouseItem('', '', '');
      formSelects.value('providers', []);
      formSelects.value('methods', []);
      
      // 重置水印设置
      $('#watermark-logo-preview').hide();
      $('#watermark-logo-path').val('');
      $('#watermark-logo-text').text('支持jpg、png格式，建议尺寸不超过200x200px');
      $('#opacity-value').text('50');
      $('#logo-opacity-value').text('80');
      $('input[name="watermark_opacity"]').val('50');
      $('input[name="watermark_logo_opacity"]').val('80');
      $('input[name="watermark_color"]').val('#000000');
      
      layer.close(index);
      layer.msg('表单已重置', {icon: 1, time: 1000});
    });
  });

  // 表单提交处理
  form.on('submit(formDemo)', function(data){
    var warehouses = [];
    var hasIncompleteWarehouse = false;
    var incompleteWarehouseMsg = '';
    
    $('.warehouse-item').each(function(index) {
      var warehouse = $(this).find('select[name="warehouse"]').val();
      var account = $(this).find('input[name="account"]').val();
      var password = $(this).find('input[name="password"]').val();
      
      var filledFields = [warehouse, account, password].filter(Boolean);
      
      if (filledFields.length > 0 && filledFields.length < 3) {
        hasIncompleteWarehouse = true;
        incompleteWarehouseMsg = '第' + (index + 1) + '个仓库信息不完整，请填写完整的仓库、账号和密码，或者删除该仓库设置';
        return false; // 跳出循环
      }
      
      if (filledFields.length === 3) {
        warehouses.push({
          warehouse: warehouse,
          account: account,
          password: password
        });
      }
    });
    
    if (hasIncompleteWarehouse) {
      layer.msg(incompleteWarehouseMsg, {icon: 2});
      return false;
    }
    
    var submitData = {
      warehouse_settings: JSON.stringify(warehouses),
      user_key: data.field.user_key.trim(),
      ali_code: data.field.ali_code.trim(),
      google_code: data.field.google_code.trim(),
      papago_code: data.field.papago_code.trim(),
      deepl_code: data.field.deepl_code.trim(),
      chatgpt_code: data.field.chatgpt_code.trim(),
      baidu_code: data.field.baidu_code.trim(),
      logistics_providers: formSelects.value('providers', 'val'),
      logistics_methods: formSelects.value('methods', 'val'),
      label_fee: data.field.label_fee,
      withdraw_fee: data.field.withdraw_fee,
      // 水印设置数据
      watermark_text: data.field.watermark_text ? data.field.watermark_text.trim() : '',
      watermark_position: data.field.watermark_position || '',
      watermark_color: data.field.watermark_color || '#000000',
      watermark_opacity: data.field.watermark_opacity || '50',
      watermark_logo: $('#watermark-logo-path').val() || '',
      watermark_logo_position: data.field.watermark_logo_position || '',
      watermark_logo_opacity: data.field.watermark_logo_opacity || '80'
    };
    
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});
    
    $.ajax({
      url: 'ajax.php?act=save_user_settings',
      type: 'POST',
      data: submitData,
      dataType: 'json',
      timeout: 10000,
      success: function(res) {
        layer.close(loadingIndex);
        if (res.code === 0) {
          layer.msg('设置保存成功！', {icon: 1, time: 2000});
        } else {
          layer.msg(res.msg || '保存失败，请检查输入信息', {icon: 2, time: 3000});
        }
      },
      error: function(xhr, status, error) {
        layer.close(loadingIndex);
        var errorMsg = '网络错误，请重试';
        if (status === 'timeout') {
          errorMsg = '请求超时，请检查网络连接';
        } else if (xhr.status === 500) {
          errorMsg = '服务器内部错误，请联系管理员';
        }
        layer.msg(errorMsg, {icon: 2, time: 3000});
      }
    });
    
    return false; // 阻止表单的默认提交行为
  });
});
</script>
</body>
</html>
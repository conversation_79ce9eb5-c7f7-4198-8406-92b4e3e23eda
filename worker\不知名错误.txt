

{
    "offer_id": "RHIAUBDASM",
    "product_id": 0,
    "status": "failed",
    "errors": [
        {
            "code": "TOTAL_CREATE_LIMIT_EXCEEDED_GLOBAL_BL",
            "field": "",
            "attribute_id": 0,
            "state": "new",
            "level": "error",
            "description": "Не получится добавить товар: вы исчерпали лимит. Чтобы увеличить его, увеличьте продажи.",
            "attribute_name": "",
            "message": "Не получится добавить товар: вы исчерпали лимит. Чтобы увеличить его, увеличьте продажи."
        }
    ]
}

{
    "offer_id": "OWDXKXJJIL",
    "product_id": 0,
    "status": "failed",
    "errors": [
        {
            "code": "TOTAL_CREATE_LIMIT_EXCEEDED_GLOBAL_BL",
            "field": "",
            "attribute_id": 0,
            "state": "new",
            "level": "error",
            "description": "Не получится добавить товар: вы исчерпали лимит. Чтобы увеличить его, увеличьте продажи.",
            "attribute_name": "",
            "message": "Не получится добавить товар: вы исчерпали лимит. Чтобы увеличить его, увеличьте продажи."
        }
    ]
}

{
    "offer_id": "SXPWSSYOMO",
    "product_id": 0,
    "status": "failed",
    "errors": [
        {
            "code": "storage_temperature_partial_range",
            "field": "",
            "attribute_id": 0,
            "state": "new",
            "level": "error",
            "description": "Заполните значениями оба атрибута температурного режима — «Максимальная температура» и «Минимальная температура»",
            "attribute_name": "",
            "message": "Заполните значениями оба атрибута температурного режима — «Максимальная температура» и «Минимальная температура»"
        },
        {
            "code": "DAILY_CREATE_LIMIT_EXCEEDED",
            "field": "",
            "attribute_id": 0,
            "state": "new",
            "level": "error",
            "description": "Не получится добавить товар: вы исчерпали суточный лимит. Добавить новые карточки можно будет после обновления лимита в 03:00 мск. Подробнее о лимите",
            "attribute_name": "",
            "message": "Не получится добавить товар: вы исчерпали суточный лимит. Добавить новые карточки можно будет после обновления лимита в 03:00 мск. Подробнее о лимите"
        }
    ]
}

{
    "offer_id": "MMZVEHKNVY",
    "product_id": 0,
    "status": "failed",
    "errors": [
        {
            "code": "FB_bestiality",
            "field": "",
            "attribute_id": 22508,
            "state": "new",
            "level": "error",
            "description": "Пропаганда половых сношений в отношении животного запрещены к продаже на Озон. Если товар ещё не создан, удалите его. Если создан — перенесите в архив",
            "attribute_name": "Общие недочеты",
            "message": "Пропаганда половых сношений в отношении животного запрещены к продаже на Озон. Если товар ещё не создан, удалите его. Если создан — перенесите в архив"
        },
        {
            "code": "DAILY_CREATE_LIMIT_EXCEEDED",
            "field": "",
            "attribute_id": 0,
            "state": "new",
            "level": "error",
            "description": "Не получится добавить товар: вы исчерпали суточный лимит. Добавить новые карточки можно будет после обновления лимита в 03:00 мск. Подробнее о лимите",
            "attribute_name": "",
            "message": "Не получится добавить товар: вы исчерпали суточный лимит. Добавить новые карточки можно будет после обновления лимита в 03:00 мск. Подробнее о лимите"
        }
    ]
}
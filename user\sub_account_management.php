<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="../assets/component/pear/css/pear.css">
<link rel="stylesheet" href="../assets/component/layui/css/layui.css">
<style>
    body {
        background-color: #f5f5f5;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .sub-account-container {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    /* 页面头部优化 */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .page-header h3 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
    }

    .page-header .header-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    /* 卡片头部布局修复 */
    .layui-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: white;
        border-bottom: 1px solid #f0f0f0;
    }

    .layui-card-header h3 {
        margin: 0;
        color: #333;
        font-size: 18px;
        font-weight: 600;
    }

    .layui-card-header .layui-btn-group {
        float: none !important;
    }

    /* 添加按钮美化 */
    #addSubAccount {
        background: linear-gradient(45deg, #1890ff, #36cfc9);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
    }

    #addSubAccount:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
    }

    /* 子账号卡片美化 */
    .account-card {
        border: none;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 16px;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .account-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .account-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    .account-card h4 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
    }

    .account-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .account-details p {
        margin: 4px 0;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
    }

    .account-actions {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
    }

    /* 状态标签美化 */
    .account-status {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
        color: white;
        text-align: center;
        min-width: 60px;
    }

    .status-active {
        background: linear-gradient(45deg, #52c41a, #73d13d);
        box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
    }

    .status-disabled {
        background: linear-gradient(45deg, #ff4d4f, #ff7875);
        box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
    }

    .status-pending {
        background: linear-gradient(45deg, #faad14, #ffc53d);
        box-shadow: 0 2px 4px rgba(250, 173, 20, 0.3);
    }

    /* 操作按钮美化 */
    .action-buttons {
        display: flex;
        gap: 8px;
    }

    .action-buttons .layui-btn-xs {
        border-radius: 4px;
        font-size: 12px;
        padding: 4px 8px;
        transition: all 0.2s ease;
    }

    .action-buttons .layui-btn:hover {
        transform: translateY(-1px);
    }

    /* 权限树美化 */
    .permission-module {
        margin-bottom: 16px;
        border: 1px solid #f0f0f0;
        padding: 16px;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.2s ease;
    }

    .permission-module:hover {
        border-color: #d9d9d9;
        background: #f5f5f5;
    }

    .permission-module label {
        font-weight: 600;
        color: #333;
        cursor: pointer;
    }

    .children-permissions {
        margin-left: 20px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 8px;
        margin-top: 12px;
    }

    .children-permissions label {
        font-size: 13px;
        color: #666;
        font-weight: normal;
        padding: 4px 0;
    }

    /* 店铺权限区域美化 */
    .store-permission-section {
        background: #fafafa;
        border-radius: 8px;
        padding: 16px;
    }

    .store-controls {
        background: white;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .store-stats {
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .store-item {
        transition: all 0.2s ease;
        border-radius: 6px;
        overflow: hidden;
    }

    .store-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* 表单美化 */
    .layui-form-label {
        font-weight: 500;
        color: #333;
    }

    .layui-input,
    .layui-select {
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        transition: all 0.2s ease;
    }

    .layui-input:focus,
    .layui-select:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    /* 弹窗美化 */
    .layui-layer-title {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
    }

    /* 空状态美化 */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #999;
    }

    .empty-state i {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 16px;
        display: block;
    }

    /* 加载状态美化 */
    .loading-state {
        text-align: center;
        padding: 40px 20px;
        color: #999;
    }

    .loading-state i {
        font-size: 24px;
        margin-right: 8px;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .sub-account-container {
            padding: 10px;
        }

        .account-info {
            flex-direction: column;
            gap: 16px;
        }

        .account-actions {
            align-items: flex-start;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
        }

        .layui-card-header {
            flex-direction: column;
            gap: 12px;
            align-items: flex-start;
        }

        .children-permissions {
            grid-template-columns: 1fr;
        }
    }

    /* 滚动条美化 */
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
<div class="layui-fluid sub-account-container">
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>子账号管理</h3>
                    <div class="layui-btn-group" style="float: right;">
                        <button class="layui-btn layui-btn-primary" id="addSubAccount">
                            <i class="layui-icon layui-icon-add-1"></i> 添加子账号
                        </button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <!-- 子账号列表 -->
                    <div id="subAccountList">
                        <!-- 动态加载子账号列表 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑子账号弹窗 -->
<div id="subAccountModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="subAccountForm">
        <input type="hidden" name="sub_uid" id="sub_uid">

        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="username" placeholder="请输入用户名" class="layui-input" lay-verify="required">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="password" name="password" placeholder="请输入密码" class="layui-input" lay-verify="required">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-block">
                <input type="email" name="email" placeholder="请输入邮箱" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
                <input type="text" name="phone" placeholder="请输入手机号" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">账号状态</label>
            <div class="layui-input-block">
                <select name="status">
                    <option value="1">正常</option>
                    <option value="0">禁用</option>
                    <option value="2">待激活</option>
                </select>
            </div>
        </div>



        <div class="layui-form-item">
            <label class="layui-form-label">功能权限</label>
            <div class="layui-input-block">
                <div id="permissionTree"
                    style="max-height: 300px; overflow-y: auto; border: 1px solid #e6e6e6; padding: 10px;">
                    <!-- 权限树将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">店铺权限</label>
            <div class="layui-input-block">
                <div class="store-permission-section">
                    <!-- 店铺筛选和操作区域 -->
                    <div class="store-controls">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-input-group">
                                    <div class="layui-input-split layui-input-prefix">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </div>
                                    <input type="text" id="storeSearchInput" placeholder="搜索店铺名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-btn-group">
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                        id="selectAllStores">
                                        <i class="layui-icon layui-icon-ok"></i> 全选
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                        id="unselectAllStores">
                                        <i class="layui-icon layui-icon-close"></i> 全不选
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                        id="toggleStoreList">
                                        <i class="layui-icon layui-icon-down"></i> 展开/收起
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 店铺权限统计 -->
                    <div class="store-stats layui-card">
                        <div class="layui-card-body" style="padding: 12px;">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md4">
                                    <span class="layui-badge layui-bg-blue">总店铺: <span
                                            id="totalStoreCount">0</span></span>
                                </div>
                                <div class="layui-col-md4">
                                    <span class="layui-badge layui-bg-green">已选择: <span
                                            id="selectedStoreCount">0</span></span>
                                </div>
                                <div class="layui-col-md4">
                                    <span class="layui-badge layui-bg-orange">未选择: <span
                                            id="unselectedStoreCount">0</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 店铺列表容器 -->
                    <div id="storePermissionList" class="layui-card" style="max-height: 300px; overflow-y: auto;">
                        <div class="layui-card-body" style="padding: 10px;">
                            <!-- 店铺列表将通过JavaScript动态加载 -->
                            <div class="loading-state">
                                <i
                                    class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                                正在加载店铺列表...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submitSubAccount">保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>

<script src="../assets/component/layui/layui.js"></script>
<script>
    layui.use(['layer', 'form', 'jquery'], function () {
        var layer = layui.layer,
            form = layui.form,
            $ = layui.jquery;

        // 权限配置
        var availablePermissions = {
            'store_manage': {
                'name': '店铺管理',
                'children': {
                    'store_list': '店铺列表',
                    'store_add': '添加店铺',
                    'store_edit': '编辑店铺',
                    'store_delete': '删除店铺'
                }
            },
            'product_manage': {
                'name': '商品管理',
                'children': {
                    'product_list': '商品列表',
                    'product_add': '添加商品',
                    'product_edit': '编辑商品',
                    'product_delete': '删除商品',
                    'product_import': '商品导入',
                    'product_export': '商品导出'
                }
            },
            'order_manage': {
                'name': '订单管理',
                'children': {
                    'order_list': '订单列表',
                    'order_detail': '订单详情',
                    'order_dropship': '代发采购',
                    'order_print': '打印面单',
                    'order_ship': '发货管理',
                    'order_refund': '退款处理'
                }
            },
            'purchase_manage': {
                'name': '采购管理',
                'children': {
                    'purchase_list': '采购列表',
                    'purchase_add': '创建采购',
                    'purchase_edit': '编辑采购',
                    'purchase_approve': '采购审批'
                }
            },
            'finance_manage': {
                'name': '财务管理',
                'children': {
                    'finance_overview': '财务概览',
                    'finance_income': '收入管理',
                    'finance_expense': '支出管理',
                    'finance_report': '财务报表'
                }
            },
            'data_analysis': {
                'name': '数据分析',
                'children': {
                    'data_overview': '数据概览',
                    'data_sales': '销售分析',
                    'data_product': '商品分析',
                    'data_export': '数据导出'
                }
            }
        };

        // 生成权限树
        function generatePermissionTree() {
            var html = '';

            for (var moduleKey in availablePermissions) {
                var module = availablePermissions[moduleKey];
                html += `
                    <div class="permission-module" style="margin-bottom: 15px; border: 1px solid #f0f0f0; padding: 10px; border-radius: 4px;">
                        <div style="margin-bottom: 8px;">
                            <input type="checkbox" class="module-checkbox" data-module="${moduleKey}" lay-skin="primary" title="${module.name}" id="module_${moduleKey}">
                            <label for="module_${moduleKey}" style="font-weight: bold; cursor: pointer; margin-left: 8px;">${module.name}</label>
                        </div>
                        <div class="children-permissions" style="margin-left: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 8px;">
                `;

                for (var childKey in module.children) {
                    html += `
                        <div style="display: flex; align-items: center; padding: 4px 0;">
                            <input type="checkbox" class="child-checkbox" name="permissions[]" value="${childKey}" data-parent="${moduleKey}" lay-skin="primary" title="" id="perm_${childKey}">
                            <label for="perm_${childKey}" style="font-size: 12px; color: #666; cursor: pointer; margin-left: 6px; flex: 1;">${module.children[childKey]}</label>
                        </div>
                    `;
                }

                html += `
                        </div>
                    </div>
                `;
            }

            $('#permissionTree').html(html);
            form.render();

            // 绑定父级复选框事件
            $('.module-checkbox').change(function () {
                var module = $(this).data('module');
                var checked = $(this).prop('checked');
                $(`.child-checkbox[data-parent="${module}"]`).prop('checked', checked);
                form.render();
            });

            // 绑定子级复选框事件
            $('.child-checkbox').change(function () {
                var parent = $(this).data('parent');
                var totalChildren = $(`.child-checkbox[data-parent="${parent}"]`).length;
                var checkedChildren = $(`.child-checkbox[data-parent="${parent}"]:checked`).length;

                var parentCheckbox = $(`.module-checkbox[data-module="${parent}"]`);
                if (checkedChildren === 0) {
                    parentCheckbox.prop('checked', false);
                } else if (checkedChildren === totalChildren) {
                    parentCheckbox.prop('checked', true);
                } else {
                    parentCheckbox.prop('indeterminate', true);
                }
                form.render();
            });
        }

        // 格式化权限显示
        function formatPermissions(permissions) {
            if (!permissions) return '权限: 无';

            try {
                // 如果是字符串，尝试解析；如果已经是对象，直接使用
                if (typeof permissions === 'string') {
                    permissions = JSON.parse(permissions);
                }

                console.log('formatPermissions 被调用，数据类型:', typeof permissions, '数据内容:', permissions);

                var result = [];

                if (Array.isArray(permissions)) {
                    // 旧格式：简单数组
                    result.push('功能权限: ' + (permissions.length > 0 ? permissions.join(', ') : '无'));
                } else if (typeof permissions === 'object') {
                    // 新格式：对象格式
                    if (permissions.modules && permissions.modules.length > 0) {
                        var moduleNames = [];
                        permissions.modules.forEach(function (perm) {
                            console.log('查找权限:', perm); // 调试信息
                            // 查找权限对应的中文名称
                            for (var moduleKey in availablePermissions) {
                                if (availablePermissions[moduleKey].children[perm]) {
                                    moduleNames.push(availablePermissions[moduleKey].children[perm]);
                                    console.log('找到权限:', perm, '->', availablePermissions[moduleKey].children[perm]);
                                    break;
                                }
                            }
                        });
                        result.push('功能权限: ' + (moduleNames.length > 0 ? moduleNames.join(', ') : '无 (未找到匹配权限)'));
                    } else {
                        result.push('功能权限: 无');
                    }

                    if (permissions.accessible_stores && permissions.accessible_stores.length > 0) {
                        result.push('可访问店铺: ' + permissions.accessible_stores.length + '个');
                    } else {
                        result.push('可访问店铺: 0个');
                    }
                }

                return result.length > 0 ? result.join('<br>') : '权限: 无';
            } catch (e) {
                console.error('权限格式化错误:', e, permissions);
                return '权限: 格式错误 (' + e.message + ')';
            }
        }

        // 全局变量存储店铺数据
        var allStores = [];
        var filteredStores = [];

        // 加载店铺列表
        function loadStoreList(callback) {
            console.log('开始加载店铺列表...');
            $.get('ajax.php?act=store_List&limit=1000', function (res) {
                console.log('店铺列表响应:', res);
                if (res.code === 0 && res.data) {
                    allStores = res.data;
                    filteredStores = res.data;
                    renderStoreList();
                    updateStoreStats();

                    console.log('店铺列表加载完成，共', res.data.length, '个店铺');

                    // 执行回调函数
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    $('#storePermissionList .layui-card-body').html('<div style="color: #999; text-align: center; padding: 20px;">暂无店铺</div>');
                    console.log('没有店铺数据');
                }
            }).fail(function (xhr, status, error) {
                console.error('加载店铺列表失败:', error);
                $('#storePermissionList .layui-card-body').html('<div style="color: #red; text-align: center; padding: 20px;">加载店铺失败</div>');
            });
        }

        // 渲染店铺列表 - 一行三个的布局
        function renderStoreList() {
            var html = '<div class="layui-row layui-col-space10">';

            filteredStores.forEach(function (store, index) {
                if (index % 3 === 0 && index > 0) {
                    html += '</div><div class="layui-row layui-col-space10" style="margin-top: 10px;">';
                }

                html += `
                    <div class="layui-col-md4">
                        <div class="layui-card store-item" data-store-id="${store.id}" style="margin-bottom: 0;">
                            <div class="layui-card-body" style="padding: 10px;">
                                <div class="layui-form-item" style="margin: 0;">
                                    <input type="checkbox" name="accessible_stores[]" value="${store.id}" lay-skin="primary" title="" class="store-checkbox" lay-filter="store-checkbox">
                                    <div class="layui-form-mid" style="padding: 0 10px 0 5px; font-size: 12px; color: #333; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${store.storename}">
                                        ${store.storename}
                                    </div>
                                </div>
                                <div class="layui-form-item" style="margin: 5px 0 0 0;">
                                    <div class="layui-input-block" style="margin-left: 0;">
                                        <select name="store_permission_${store.id}" lay-filter="store-permission">
                                            <option value="full">完全控制</option>
                                            <option value="view">查看</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            $('#storePermissionList .layui-card-body').html(html);

            // 简化的渲染方式
            form.render('checkbox');
            form.render('select');

            // 绑定复选框变化事件
            bindStoreEvents();
        }

        // 绑定店铺相关事件
        function bindStoreEvents() {
            // 监听店铺复选框变化
            form.on('checkbox(store-checkbox)', function (data) {
                console.log('店铺复选框状态改变:', data.elem.value, data.elem.checked);
                updateStoreStats();

                var storeId = data.elem.value;
                var permissionSelect = $('select[name="store_permission_' + storeId + '"]');

                if (!data.elem.checked) {
                    // 取消选中时，重置权限为查看
                    permissionSelect.val('view');
                    form.render('select');
                }
            });

            // 监听权限级别变化
            form.on('select(store-permission)', function (data) {
                console.log('店铺权限级别改变:', data.elem.name, data.value);
            });
        }

        // 更新店铺统计信息
        function updateStoreStats() {
            var totalCount = filteredStores.length;
            var selectedCount = $('input[name="accessible_stores[]"]:checked').length;
            var unselectedCount = totalCount - selectedCount;

            $('#totalStoreCount').text(totalCount);
            $('#selectedStoreCount').text(selectedCount);
            $('#unselectedStoreCount').text(unselectedCount);
        }

        // 店铺搜索功能
        $('#storeSearchInput').on('input', function () {
            var keyword = $(this).val().toLowerCase().trim();

            if (keyword === '') {
                filteredStores = allStores;
            } else {
                filteredStores = allStores.filter(function (store) {
                    return store.storename.toLowerCase().indexOf(keyword) !== -1;
                });
            }

            renderStoreList();
            updateStoreStats();
        });

        // 展开/收起店铺列表
        $('#toggleStoreList').click(function () {
            var $list = $('#storePermissionList');
            var $icon = $(this).find('i');

            if ($list.is(':visible')) {
                $list.slideUp();
                $icon.removeClass('layui-icon-down').addClass('layui-icon-right');
                $(this).find('span').text(' 展开');
            } else {
                $list.slideDown();
                $icon.removeClass('layui-icon-right').addClass('layui-icon-down');
                $(this).find('span').text(' 收起');
            }
        });

        // 绑定店铺复选框事件
        function bindStoreCheckboxEvents() {
            // 使用layui的form监听复选框变化
            form.on('checkbox()', function (data) {
                if (data.elem.name === 'accessible_stores[]') {
                    console.log('店铺复选框状态改变:', data.elem.value, data.elem.checked);

                    // 当取消选中时，可以重置权限级别为默认值
                    if (!data.elem.checked) {
                        var storeId = data.elem.value;
                        var select = $('select[name="store_permission_' + storeId + '"]');
                        if (select.length > 0) {
                            select.val('view');
                            form.render('select');
                        }
                    }
                }
            });
        }

        // 加载子账号列表
        function loadSubAccounts() {
            $.get('sub_account_ajax.php?act=get_sub_accounts', function (res) {
                console.log('AJAX响应数据:', res);
                if (res.code === 0) {
                    var html = '';
                    if (res.data.length === 0) {
                        html = '<div class="empty-state"><i class="layui-icon layui-icon-username"></i><div>暂无子账号</div><div style="font-size: 12px; margin-top: 8px;">点击上方"添加子账号"按钮创建第一个子账号</div></div>';
                    } else {
                        res.data.forEach(function (account) {
                            var statusClass = account.status == 1 ? 'status-active' :
                                (account.status == 0 ? 'status-disabled' : 'status-pending');
                            var statusText = account.status == 1 ? '正常' :
                                (account.status == 0 ? '禁用' : '待激活');

                            html += `
                                <div class="account-card">
                                    <div class="account-info">
                                        <div class="account-details">
                                            <h4><i class="layui-icon layui-icon-username" style="margin-right: 8px; color: #1890ff;"></i>${account.username}</h4>
                                            <p><i class="layui-icon layui-icon-email" style="margin-right: 6px; color: #52c41a;"></i>邮箱: ${account.email || '未设置'}</p>
                                            <p><i class="layui-icon layui-icon-cellphone" style="margin-right: 6px; color: #fa8c16;"></i>手机: ${account.phone || '未设置'}</p>
                                            <p style="margin-top: 8px;"><i class="layui-icon layui-icon-key" style="margin-right: 6px; color: #722ed1;"></i>${formatPermissions(account.permissions)}</p>
                                        </div>
                                        <div class="account-actions">
                                            <span class="account-status ${statusClass}">${statusText}</span>
                                            <div class="action-buttons">
                                                <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="editSubAccount(${account.uid})">
                                                    <i class="layui-icon layui-icon-edit"></i> 编辑
                                                </button>
                                                <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteSubAccount(${account.uid})">
                                                    <i class="layui-icon layui-icon-delete"></i> 删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }
                    $('#subAccountList').html(html);
                } else {
                    layer.msg(res.msg || '加载失败');
                }
            });
        }

        // 店铺权限全选/全不选 - 兼容Layui
        $(document).on('click', '#selectAllStores', function () {
            console.log('点击全选按钮');
            var checkboxes = $('input[name="accessible_stores[]"]');
            console.log('找到复选框数量:', checkboxes.length);

            checkboxes.each(function () {
                this.checked = true;
            });

            // 重新渲染Layui复选框
            form.render('checkbox');

            // 更新统计
            updateStoreStats();

            var checkedCount = $('input[name="accessible_stores[]"]:checked').length;
            console.log('全选完成，选中数量:', checkedCount);
        });

        $(document).on('click', '#unselectAllStores', function () {
            console.log('点击全不选按钮');
            var checkboxes = $('input[name="accessible_stores[]"]');
            console.log('找到复选框数量:', checkboxes.length);

            checkboxes.each(function () {
                this.checked = false;
                // 同时重置权限级别为查看
                var storeId = $(this).val();
                $('select[name="store_permission_' + storeId + '"]').val('view');
            });

            // 重新渲染Layui表单元素
            form.render('checkbox');
            form.render('select');

            // 更新统计
            updateStoreStats();

            var checkedCount = $('input[name="accessible_stores[]"]:checked').length;
            console.log('全不选完成，选中数量:', checkedCount);
        });

        // 添加子账号
        $('#addSubAccount').click(function () {
            $('#subAccountForm')[0].reset();
            $('#sub_uid').val('');
            generatePermissionTree();
            loadStoreList();
            form.render();
            layer.open({
                type: 1,
                title: '添加子账号',
                content: $('#subAccountModal'),
                area: ['900px', '85%'],
                maxmin: true
            });
        });

        // 编辑子账号
        window.editSubAccount = function (uid) {
            $.get('sub_account_ajax.php?act=get_sub_account_detail&sub_uid=' + uid, function (res) {
                if (res.code === 0) {
                    var data = res.data;
                    $('#sub_uid').val(data.uid);
                    $('input[name="username"]').val(data.username);
                    $('input[name="email"]').val(data.email);
                    $('input[name="phone"]').val(data.phone);
                    $('select[name="status"]').val(data.status);

                    // 生成权限树
                    generatePermissionTree();

                    // 加载店铺列表并设置权限
                    loadStoreList(function () {
                        console.log('店铺列表加载完成，开始设置权限');
                        console.log('当前权限数据:', data.permissions);

                        // 清空所有权限选择
                        $('input[name="permissions[]"]').prop('checked', false);
                        $('.module-checkbox').prop('checked', false);

                        var permissionsData = null;
                        if (data.permissions) {
                            try {
                                permissionsData = JSON.parse(data.permissions);
                            } catch (e) {
                                // 兼容旧格式
                                permissionsData = data.permissions;
                            }
                        }

                        if (permissionsData) {
                            if (Array.isArray(permissionsData)) {
                                // 旧格式：简单数组，需要转换为新格式的子权限
                                permissionsData.forEach(function (perm) {
                                    // 查找对应的子权限并选中
                                    for (var moduleKey in availablePermissions) {
                                        if (moduleKey === perm) {
                                            // 如果是模块权限，选中所有子权限
                                            for (var childKey in availablePermissions[moduleKey].children) {
                                                $('input[name="permissions[]"][value="' + childKey + '"]').prop('checked', true);
                                            }
                                            break;
                                        }
                                    }
                                });
                            } else if (permissionsData.modules) {
                                // 新格式：包含modules, accessible_stores, store_permissions
                                permissionsData.modules.forEach(function (perm) {
                                    $('input[name="permissions[]"][value="' + perm + '"]').prop('checked', true);
                                });

                                // 设置店铺权限
                                if (permissionsData.accessible_stores) {
                                    console.log('设置店铺权限:', permissionsData.accessible_stores);
                                    console.log('店铺权限级别:', permissionsData.store_permissions);

                                    // 先清空所有店铺选择
                                    $('input[name="accessible_stores[]"]').prop('checked', false);

                                    permissionsData.accessible_stores.forEach(function (storeId) {
                                        var checkbox = $('input[name="accessible_stores[]"][value="' + storeId + '"]');
                                        if (checkbox.length > 0) {
                                            checkbox.prop('checked', true);
                                            console.log('选中店铺:', storeId);

                                            // 设置店铺权限级别
                                            if (permissionsData.store_permissions && permissionsData.store_permissions[storeId]) {
                                                var select = $('select[name="store_permission_' + storeId + '"]');
                                                if (select.length > 0) {
                                                    select.val(permissionsData.store_permissions[storeId]);
                                                    console.log('设置店铺权限级别:', storeId, '->', permissionsData.store_permissions[storeId]);
                                                }
                                            }
                                        } else {
                                            console.log('未找到店铺复选框:', storeId);
                                        }
                                    });
                                }
                            }
                        }

                        // 更新父级复选框状态
                        $('.child-checkbox').trigger('change');

                        form.render();
                    });

                    layer.open({
                        type: 1,
                        title: '编辑子账号',
                        content: $('#subAccountModal'),
                        area: ['900px', '85%'],
                        maxmin: true
                    });
                } else {
                    layer.msg(res.msg || '获取账号信息失败');
                }
            });
        };

        // 删除子账号
        window.deleteSubAccount = function (uid) {
            layer.confirm('确定要删除这个子账号吗？', function (index) {
                $.post('sub_account_ajax.php?act=delete_sub_account', { sub_uid: uid }, function (res) {
                    if (res.code === 0) {
                        layer.msg('删除成功');
                        loadSubAccounts();
                    } else {
                        layer.msg(res.msg || '删除失败');
                    }
                    layer.close(index);
                });
            });
        };

        // 提交表单
        form.on('submit(submitSubAccount)', function (data) {
            var formData = data.field;

            // 收集功能权限
            var modules = [];
            $('input[name="permissions[]"]:checked').each(function () {
                modules.push($(this).val());
            });

            console.log('收集到的功能权限:', modules);

            // 收集店铺权限
            var accessible_stores = [];
            var store_permissions = {};
            $('input[name="accessible_stores[]"]:checked').each(function () {
                var storeId = $(this).val();
                accessible_stores.push(parseInt(storeId));

                var permission = $('select[name="store_permission_' + storeId + '"]').val();
                store_permissions[storeId] = permission;
            });

            console.log('收集到的店铺权限:', accessible_stores);

            // 构造新的权限格式
            var permissionsData = {
                modules: modules,
                accessible_stores: accessible_stores,
                store_permissions: store_permissions
            };

            console.log('构造的权限数据:', permissionsData);

            formData.permissions = JSON.stringify(permissionsData);

            console.log('最终发送的权限字符串:', formData.permissions);

            var act = formData.sub_uid ? 'update_sub_account' : 'create_sub_account';

            console.log('提交数据到服务器:', act, formData);

            $.post('sub_account_ajax.php?act=' + act, formData, function (res) {
                if (res.code === 0) {
                    layer.msg('保存成功');
                    layer.closeAll();
                    loadSubAccounts();
                } else {
                    layer.msg(res.msg || '保存失败');
                }
            });

            return false;
        });

        // 初始加载
        loadSubAccounts();
        loadStoreList();
        generatePermissionTree();
    });
</script>
-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-27 18:27:06
-- 服务器版本： 8.0.36
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `erp`
--

-- --------------------------------------------------------

--
-- 表的结构 `packing_records`
--

CREATE TABLE `packing_records` (
  `id` int NOT NULL COMMENT '记录ID',
  `uid` int NOT NULL COMMENT '订单归属uid',
  `posting_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单posting编号',
  `tracking_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '国际',
  `courierNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '国内单号',
  `packerId` int NOT NULL COMMENT '打包员ID',
  `packing_quantity` int DEFAULT '1' COMMENT '打包数量',
  `material_cost` decimal(10,2) DEFAULT NULL COMMENT '耗材费用(元)',
  `packing_status` tinyint NOT NULL DEFAULT '0' COMMENT ' 0=异常 1=打包完成  2=退回问题件 ',
  `completion_time` datetime DEFAULT NULL COMMENT '打包完成时间',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='打包记录表';

--
-- 转储表的索引
--

--
-- 表的索引 `packing_records`
--
ALTER TABLE `packing_records`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_posting` (`posting_number`) COMMENT '订单唯一索引',
  ADD KEY `idx_tracking` (`tracking_number`) COMMENT '快递单号索引',
  ADD KEY `idx_packer` (`packerId`) COMMENT '打包员索引',
  ADD KEY `idx_status` (`packing_status`) COMMENT '状态索引',
  ADD KEY `idx_completion` (`completion_time`) COMMENT '完成时间索引';

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `packing_records`
--
ALTER TABLE `packing_records`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID';
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

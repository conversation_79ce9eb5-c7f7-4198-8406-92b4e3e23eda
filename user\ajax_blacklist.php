<?php
include("../includes/common.php");

$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;

if (!checkRefererHost())
    exit('{"code":403}');

if ($islogin2 != 1) {
    exit('{"code":-3,"msg":"No Login"}');
}

@header('Content-Type: application/json; charset=UTF-8');

switch ($act) {
    case 'list': // 获取黑名单列表
        $page = isset($_GET['page']) ? intval($_GET['page']) : (isset($_POST['page']) ? intval($_POST['page']) : 1);
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : (isset($_POST['limit']) ? intval($_POST['limit']) : 20);
        $customer_name = isset($_GET['customer_name']) ? daddslashes($_GET['customer_name']) : (isset($_POST['customer_name']) ? daddslashes($_POST['customer_name']) : '');
        $reason_type = isset($_GET['reason_type']) ? intval($_GET['reason_type']) : (isset($_POST['reason_type']) ? intval($_POST['reason_type']) : 0);
        
        $offset = ($page - 1) * $limit;
        
        // 构建查询条件
        $where = "WHERE uid = ?";
        $params = [$uid];
        
        if (!empty($customer_name)) {
            $where .= " AND customer_name LIKE ?";
            $params[] = "%{$customer_name}%";
        }
        
        if ($reason_type > 0) {
            $where .= " AND reason_type = ?";
            $params[] = $reason_type;
        }
        
        // 获取总数
        $total = $DB->getColumn("SELECT COUNT(*) FROM blacklist {$where}", $params);
        
        // 获取数据
        $sql = "SELECT id, posting_number, customer_name, reason_type, reason_text, created_at 
                FROM blacklist {$where} 
                ORDER BY created_at DESC 
                LIMIT {$offset}, {$limit}";
        
        $list = $DB->getAll($sql, $params);
        
        // 处理原因类型文本
        $reason_types = [
            1 => '频繁退货退款',
            2 => '恶意差评', 
            3 => '货到付款拒收',
            4 => '其他'
        ];
        
        foreach ($list as &$item) {
            $item['reason_type_text'] = $reason_types[$item['reason_type']] ?? '未知';
        }
        
        exit(json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $list
        ]));
        break;
        
    case 'delete': // 删除单条记录
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if ($id <= 0) {
            exit(json_encode(['code' => -1, 'msg' => 'ID无效']));
        }
        
        // 检查记录是否属于当前用户
        $exists = $DB->getColumn("SELECT COUNT(*) FROM blacklist WHERE id = ? AND uid = ?", [$id, $uid]);
        if (!$exists) {
            exit(json_encode(['code' => -2, 'msg' => '记录不存在或无权限']));
        }
        
        $result = $DB->exec("DELETE FROM blacklist WHERE id = ? AND uid = ?", [$id, $uid]);
        
        if ($result) {
            exit(json_encode(['code' => 0, 'msg' => '删除成功']));
        } else {
            exit(json_encode(['code' => -3, 'msg' => '删除失败']));
        }
        break;
        
    case 'batch_delete': // 批量删除
        $ids = isset($_POST['ids']) ? $_POST['ids'] : '';
        
        if (empty($ids)) {
            exit(json_encode(['code' => -1, 'msg' => '请选择要删除的记录']));
        }
        
        $id_array = explode(',', $ids);
        $id_array = array_map('intval', $id_array);
        $id_array = array_filter($id_array, function($id) { return $id > 0; });
        
        if (empty($id_array)) {
            exit(json_encode(['code' => -2, 'msg' => 'ID格式错误']));
        }
        
        $placeholders = str_repeat('?,', count($id_array) - 1) . '?';
        $params = array_merge($id_array, [$uid]);
        
        $result = $DB->exec("DELETE FROM blacklist WHERE id IN ({$placeholders}) AND uid = ?", $params);
        
        if ($result) {
            exit(json_encode(['code' => 0, 'msg' => "成功删除 {$result} 条记录"]));
        } else {
            exit(json_encode(['code' => -3, 'msg' => '删除失败']));
        }
        break;
        
    case 'get_blacklist_count': // 获取黑名单总数（用于仪表板显示）
        $count = $DB->getColumn("SELECT COUNT(*) FROM blacklist WHERE uid = ?", [$uid]);
        exit(json_encode(['code' => 0, 'data' => ['count' => $count]]));
        break;
        
    case 'get_recent_blacklist': // 获取最近添加的黑名单（用于仪表板显示）
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 5;
        
        $list = $DB->getAll("SELECT customer_name, reason_type, created_at 
                            FROM blacklist 
                            WHERE uid = ? 
                            ORDER BY created_at DESC 
                            LIMIT ?", [$uid, $limit]);
        
        $reason_types = [
            1 => '频繁退货退款',
            2 => '恶意差评', 
            3 => '货到付款拒收',
            4 => '其他'
        ];
        
        foreach ($list as &$item) {
            $item['reason_type_text'] = $reason_types[$item['reason_type']] ?? '未知';
        }
        
        exit(json_encode(['code' => 0, 'data' => $list]));
        break;
        
    default:
        exit(json_encode(['code' => -404, 'msg' => '接口不存在']));
}
?>
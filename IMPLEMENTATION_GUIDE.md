# 用户余额管理和打包员扣款功能实现说明

## 功能概述
本次开发实现了用户余额管理、打包员自动扣款以及相关统计功能，包括：

1. 数据库结构扩展
2. 打印自动扣款逻辑
3. 用户余额信息显示
4. 打包员管理界面统计功能

## 数据库修改

### 1. 执行数据库修改脚本
需要执行 `database_modifications.sql` 文件中的SQL语句，包括：

- 在 `ozon_user` 表中添加 `balance` 字段（用户余额）
- 在 `packers` 表中添加 `deduction_per_order` 字段（每单扣款金额配置）
- 在 `packing_records` 表中添加余额相关字段：
  - `balance_before` - 扣款前余额
  - `balance_after` - 扣款后余额  
  - `deduction_amount` - 本次扣款金额
- 创建 `balance_changes` 表（余额变更记录表，用于审计）

```sql
-- 执行数据库修改脚本
SOURCE database_modifications.sql;
```

### 2. 配置打包员扣款金额
登录数据库，为每个打包员配置每单扣款金额：

```sql
-- 示例：设置打包员ID为1的每单扣款金额为2元
UPDATE packers SET deduction_per_order = 2.00 WHERE packerId = 1;
```

## 功能详细说明

### 1. 自动扣款功能 (ajax.php)
- 在 `print_label` 接口中实现了自动扣款逻辑
- 打印标签成功后自动从对应用户余额中扣除配置的金额
- 记录扣款前后余额到 `packing_records` 表
- 记录余额变更历史到 `balance_changes` 表
- 扣款失败时会返回错误信息，不影响打印流程

### 2. 统计功能增强 (ajax.php)
增强了 `get_statistics` 接口，新增：
- 每日/每周/每月打印数量统计
- 对应时间段的扣款金额统计
- 关联用户的余额信息显示
- 打包员的扣款配置信息

### 3. 用户余额信息接口 (ajax.php)
新增 `get_user_balances` 接口：
- 显示关联用户的当前余额
- 显示今日打印数量和扣款金额
- 检查余额是否足够下次打印
- 按余额从高到低排序

### 4. 前端界面更新 (Printer.php)
- 添加用户余额信息显示区域
- 实时显示每个用户的余额状态
- 显示今日打印统计和扣款信息
- 打印成功后自动刷新余额信息
- 余额不足时的视觉提示

## API接口说明

### 1. 获取统计数据（增强版）
```
GET/POST: ./ajax.php?act=get_statistics
返回格式：
{
  "code": 0,
  "data": {
    "print_stats": {
      "today": {"count": 10, "deduction": "20.00"},
      "yesterday": {"count": 8, "deduction": "16.00"},
      "week": {"count": 45, "deduction": "90.00"},
      "month": {"count": 180, "deduction": "360.00"}
    },
    "user_balances": [
      {
        "uid": 1,
        "username": "用户A", 
        "balance": "100.00",
        "today_print_count": 5,
        "today_deduction": "10.00",
        "can_print": true
      }
    ],
    "deduction_per_order": "2.00"
  }
}
```

### 2. 获取用户余额信息
```
GET/POST: ./ajax.php?act=get_user_balances
返回格式：
{
  "code": 0,
  "data": {
    "users": [用户余额数组],
    "deduction_per_order": "2.00"
  }
}
```

### 3. 打印标签（增强版）
```
POST: ./ajax.php?act=print_label
请求参数：
{
  "postingNumbers": ["订单号1", "订单号2"]
}

功能增强：
- 打印成功后自动扣款
- 扣款失败时返回详细错误信息
- 余额不足时阻止打印
```

## 部署步骤

### 1. 数据库更新
```bash
# 登录MySQL
mysql -u erp -p erp

# 执行SQL脚本
SOURCE /path/to/database_modifications.sql;

# 验证表结构
DESCRIBE ozon_user;
DESCRIBE packers;  
DESCRIBE packing_records;
DESCRIBE balance_changes;
```

### 2. 配置打包员扣款金额
```sql
-- 根据实际需求配置每个打包员的扣款金额
UPDATE packers SET deduction_per_order = 2.00 WHERE packerId = 1;
UPDATE packers SET deduction_per_order = 1.50 WHERE packerId = 2;
-- 可以为不同打包员设置不同的扣款金额
```

### 3. 用户余额初始化
```sql
-- 为现有用户设置初始余额（根据实际需求）
UPDATE ozon_user SET balance = 100.00 WHERE uid IN (1, 2, 3);
-- 或者为所有用户设置统一初始余额
UPDATE ozon_user SET balance = 50.00 WHERE status = 1;
```

### 4. 文件部署
确保以下文件已更新：
- `/packers/ajax.php` - 后端接口逻辑
- `/packers/Printer.php` - 前端界面
- `database_modifications.sql` - 数据库修改脚本

## 测试建议

### 1. 功能测试
1. 登录打包员系统
2. 查看余额信息是否正确显示
3. 执行打印操作，验证扣款是否正常
4. 检查数据库记录是否正确更新
5. 测试余额不足时的处理

### 2. 数据验证
```sql
-- 检查扣款记录
SELECT * FROM packing_records WHERE deduction_amount > 0 ORDER BY created_at DESC LIMIT 10;

-- 检查余额变更记录  
SELECT * FROM balance_changes ORDER BY created_at DESC LIMIT 10;

-- 检查用户余额
SELECT uid, username, balance FROM ozon_user WHERE balance > 0;
```

## 注意事项

1. **数据库事务**：扣款操作使用事务确保数据一致性
2. **权限控制**：打包员只能操作关联用户的订单
3. **错误处理**：余额不足或扣款失败时有详细错误提示
4. **审计日志**：所有余额变更都记录在 `balance_changes` 表中
5. **前端刷新**：打印成功后自动刷新余额信息显示

## 维护说明

### 余额充值
可通过直接更新数据库或开发充值接口为用户充值：
```sql
-- 直接充值示例
UPDATE ozon_user SET balance = balance + 100.00 WHERE uid = 1;
INSERT INTO balance_changes (uid, change_type, amount, balance_before, balance_after, notes) 
VALUES (1, 'recharge', 100.00, 旧余额, 新余额, '管理员充值');
```

### 扣款金额调整
```sql
-- 调整打包员扣款金额
UPDATE packers SET deduction_per_order = 3.00 WHERE packerId = 1;
```

### 数据查询
```sql
-- 查看打包员今日扣款统计
SELECT packerId, COUNT(*) as count, SUM(deduction_amount) as total 
FROM packing_records 
WHERE DATE(created_at) = CURDATE() AND packing_status = 1
GROUP BY packerId;

-- 查看用户余额消费记录
SELECT * FROM balance_changes WHERE uid = 1 ORDER BY created_at DESC;
```
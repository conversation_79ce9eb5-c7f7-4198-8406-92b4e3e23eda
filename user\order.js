layui.use(['table', 'layer', 'jquery', 'form', 'laydate', 'element'], function () {
    const table = layui.table;
    var layer = layui.layer;
    var form = layui.form;
    var $ = layui.$;
    var laydate = layui.laydate;
    var element = layui.element;

    // 全局状态管理
    var appState = {
        currentStatus: 'PPW',
        currentMainTab: 'pending', // 当前主标签
        currentSubTab: 'all', 
        searchParams: {
            text: '',
            money: '',
            moneys: '',
            storeid: '',
            date1: '',
            date2: '',
            groupid: '',
            paypt: ''
        },
        sortOrder: 'desc',
        isRequesting: false,
        pendingRequests: [],
        isChineseTitle: true
    };
    
    // 初始化标签功能
    function initTabs() {
        // 初始化主标签
        element.init('orderTab');
        
        // 监听主标签切换
        element.on('tab(orderTab)', function(data) {
            var tabId = this.getAttribute('lay-id');
            if (!tabId) {
                // 如果没有lay-id属性，则使用文本内容作为标识
                tabId = $(this).text().trim().replace(/\s+/g, '_').toLowerCase();
            }
            
            appState.currentMainTab = tabId;
            
            // 处理待处理订单的子标签显示
            if (tabId === 'pending') {
                $('#pendingSubTabs').show();
                // 使用当前子标签状态刷新表格
                renderTable(appState.currentSubTab, getSearchParams());
            } else {
                $('#pendingSubTabs').hide();
                // 使用主标签状态刷新表格
                renderTable(tabId, getSearchParams());
            }
        });
        
        // 监听子标签切换
        element.on('tab(pendingSubTabs)', function(data) {
            var subTabId = this.getAttribute('lay-id');
            if (!subTabId) {
                subTabId = $(this).text().trim().replace(/\s+/g, '_').toLowerCase();
            }
            
            appState.currentSubTab = subTabId;
            // 使用子标签状态刷新表格
            renderTable(subTabId, getSearchParams());
        });
    }
    
    // 获取当前搜索参数
    function getSearchParams() {
        return {
            text: appState.searchParams.text || '',
            money: appState.searchParams.money || '',
            moneys: appState.searchParams.moneys || '',
            storeid: appState.searchParams.storeid || '',
            date1: appState.searchParams.date1 || '',
            date2: appState.searchParams.date2 || '',
            groupid: appState.searchParams.groupid || '',
            paypt: appState.searchParams.paypt || ''
        };
    }

    // 请求队列管理
    var requestQueue = {
        add: function(requestFn) {
            appState.pendingRequests.push(requestFn);
            if (!appState.isRequesting) {
                this.processNext();
            }
        },
        processNext: function() {
            if (appState.pendingRequests.length > 0 && !appState.isRequesting) {
                appState.isRequesting = true;
                var nextRequest = appState.pendingRequests.shift();
                nextRequest();
            }
        },
        complete: function() {
            appState.isRequesting = false;
            this.processNext();
        }
    };

    // 防抖函数
    function debounce(func, wait) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // 初始化UI组件
    function initUIComponents() {
        // 返回顶部按钮
        $('#layuiBackToTop').on('click', function() {
            var scrollContainer = document.querySelector('.pear-page');
            if (scrollContainer && scrollContainer.scrollTop > 0) {
                scrollContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
            return false;
        });
        
        // 初始化主标签
        element.init('orderStatusTab');
        
        // 监听主标签切换
        element.on('tab(orderStatusTab)', function(data) {
            var status = this.getAttribute('lay-id');
            appState.currentStatus = status;
            
            // 处理待处理订单的子标签显示
            if (status === 'pending') {
                $('#pendingSubTabs').show();
                // 默认显示"未采购"子标签
                renderTable('not_purchased', 
                           appState.searchParams.text, 
                           appState.searchParams.money, 
                           appState.searchParams.moneys, 
                           appState.searchParams.storeid, 
                           appState.searchParams.date1, 
                           appState.searchParams.date2, 
                           appState.searchParams.groupid, 
                           'in_process_at', 
                           appState.sortOrder, 
                           appState.searchParams.paypt);
            } else {
                $('#pendingSubTabs').hide();
                renderTable(status, 
                           appState.searchParams.text, 
                           appState.searchParams.money, 
                           appState.searchParams.moneys, 
                           appState.searchParams.storeid, 
                           appState.searchParams.date1, 
                           appState.searchParams.date2, 
                           appState.searchParams.groupid, 
                           'in_process_at', 
                           appState.sortOrder, 
                           appState.searchParams.paypt);
            }
        });
        
        // 监听子标签切换
        element.on('tab(pendingSubTabs)', function(data) {
            var subStatus = this.getAttribute('lay-id');
            renderTable(subStatus, 
                       appState.searchParams.text, 
                       appState.searchParams.money, 
                       appState.searchParams.moneys, 
                       appState.searchParams.storeid, 
                       appState.searchParams.date1, 
                       appState.searchParams.date2, 
                       appState.searchParams.groupid, 
                       'in_process_at', 
                       appState.sortOrder, 
                       appState.searchParams.paypt);
        });

        // 日期选择器
        laydate.render({ elem: '#date1', format: 'yyyy-MM-dd' });
        laydate.render({ elem: '#date2', format: 'yyyy-MM-dd' });
        // 初始化下拉框
        initShopSelect();
        initGroupSelect();
    }

    // 初始化店铺下拉框
    function initShopSelect() {
        $.ajax({
            url: 'ajax_order.php?act=getShops',
            success: function(res) {
                if (res.code === 0 && res.data) {
                    var html = '<option value="">所有店铺</option>';
                    res.data.forEach(shop => {
                        html += `<option value="${shop.id}">${shop.storename}</option>`;
                    });
                    $('select[name="storeid"]').html(html);
                    form.render('select');
                }
            }
        });
    }

    // 初始化分组下拉框
    function initGroupSelect() {
        $.ajax({
            url: 'ajax.php?act=getShopGroups',
            success: function(res) {
                if (res.code === 0 && res.data) {
                    var $select = $('#groupSelect');
                    $select.empty();
                    $select.append('<option value="">所有分组</option>');
                    res.data.forEach(function(group) {
                        $select.append('<option value="' + group.id + '">' + group.name + '</option>');
                    });
                    form.render('select');
                }
            }
        });
    }

    // 更新勾选数量显示
    function updateSelectedCount() {
        var count = $('.order-checkbox:checked').length;
        var $tip = $('#selectedCountTip');
        if (count > 0) {
            $tip.text(count + '勾选').show();
        } else {
            $tip.hide();
        }
    }

    // 初始化订单状态统计
    var initOrderStatusCounts = debounce(function() {
        requestQueue.add(function() {
            $.ajax({
                url: 'ajax_order.php?act=order_status_counts',
                type: 'GET',
                success: function(res) {
                    requestQueue.complete();
                    if (res.code === 0 && res.data) {
                        updateOrderStatusCounts(res.data);
                    }
                },
                error: function() {
                    requestQueue.complete();
                }
            });
        });
    }, 300);

    // 更新订单状态计数显示
    function updateOrderStatusCounts(counts) {
        // 主标签计数
        const mainTabs = [
            'pending', 'awaiting_deliver', 'awaiting_deliver2', 
            'delivering', 'delivered', 'cancelled', 'all'
        ];
        
        mainTabs.forEach(status => {
            const count = counts[status] || 0;
            $(`#count-${status}`).text(count).toggle(count > 0);
        });
        
        // 子标签计数
        const subTabs = ['all', 'not_purchased', 'purchased', 'awaiting_verification'];
        subTabs.forEach(status => {
            const count = counts[status] || 0;
            $(`#count-${status}`).text(count).toggle(count > 0);
        });
    }


    // 渲染表格（带防抖）
    var renderTable = debounce(function(status, text, money, moneys, storeid, date1, date2, groupid, sort_field, sort_order_param, paypt) {
        requestQueue.add(function() {
            if (sort_order_param) {
                appState.sortOrder = sort_order_param;
            }

            table.render({
                elem: '#orderTable',
                url: 'ajax_order.php?act=orders_all',
                toolbar: '#toolbarOrde',
                defaultToolbar: ['exports', 'print'],
                where: {
                    status: status,
                    text: text || '',
                    money: money || '',
                    moneys: moneys || '',
                    storeid: storeid || '',
                    date1: date1 || '',
                    date2: date2 || '',
                    groupid: groupid || '',
                    sort_field: sort_field || 'in_process_at',
                    sort_order: appState.sortOrder,
                    paypt: paypt || ''
                },
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100, 200, 500],
                skin: 'row',
                even: true,
                css: '.layui-table-cell{height:auto;}',
                cols: [[
                    { field: 'content', title: '', templet: '#html', width: '100%' }
                ]],
                done: function(res, curr, count) {
                    requestQueue.complete();
                    if (res.data && res.data.status_counts) {
                        updateOrderStatusCounts(res.data.status_counts);
                    }
                    // 更新排序按钮状态
                    var $btn = $('#sortOrderTimeBtn');
                    if (appState.sortOrder === 'asc') {
                        $btn.attr('title', '按下单时间升序');
                        $btn.find('i').removeClass('layui-icon-down').addClass('layui-icon-up');
                    } else {
                        $btn.attr('title', '按下单时间降序');
                        $btn.find('i').removeClass('layui-icon-up').addClass('layui-icon-down');
                    }

                    form.render();
                    initOrderStatusCounts();
                    updateSelectedCount();
                }
            });
        });
    }, 300);

    // 更新单个订单
    var updateSingleOrder = debounce(function(posting_number, retryCount) {
        if (!posting_number) return;
        
        requestQueue.add(function() {
            var $orderDiv = $('#' + posting_number);
            if ($orderDiv.length > 0) {
                $orderDiv.css('opacity', '0.5');
            }

            $.ajax({
                url: 'ajax_order.php?act=get_single_order',
                type: 'POST',
                data: { posting_number: posting_number },
                timeout: 1000,
                success: function(res) {
                    requestQueue.complete();
                    if (res.code === 0 && res.data) {
                        var $orderDiv = $('#' + posting_number);
                        if ($orderDiv.length > 0) {
                            try {
                                var scrollTop = $('.pear-page').scrollTop();
                                res.data.weight = res.data.weight / 1000;
                                var safeData = res.data || {};
                                var criticalFields = ['posting_number', 'status', 'cost', 'purchase_orderSn', 'courierNumber', 'tracking_number', 'purchase_ok'];
                                criticalFields.forEach(function(field) {
                                    if (safeData[field] === null || safeData[field] === undefined) {
                                        safeData[field] = '';
                                    }
                                });

                                var newHtml = layui.laytpl($('#html').html()).render(safeData);
                                $orderDiv.replaceWith(newHtml);
                                form.render();
                                $('.pear-page').scrollTop(scrollTop);
                            } catch (e) {
                                console.error(e);
                            } finally {
                                $orderDiv.css('opacity', '1');
                            }
                        }
                    }
                },
                error: function(xhr, status, error) {
                    requestQueue.complete();
                    if ((status === 'timeout' || status === 'error') && (retryCount || 0) < 1) {
                        setTimeout(function() {
                            updateSingleOrder(posting_number, (retryCount || 0) + 1);
                        }, 300);
                    }
                },
                complete: function() {
                    var $orderDiv = $('#' + posting_number);
                    if ($orderDiv.length > 0) {
                        $orderDiv.css('opacity', '1');
                    }
                }
            });
        });
    }, 100);

    // 初始化事件委托
    function initEventDelegation() {
        // 订单操作按钮
        $(document).on('click', '.order_status', handleOrderStatus);
        $(document).on('click', '.Stocking_order', handleStockingOrder);
        $(document).on('click', '.order_purchase', handleOrderPurchase);
        $(document).on('click', '.order_notes', handleOrderNotes);
        $(document).on('click', '.order_self_only', handleOrderSelfOnly);
        $(document).on('click', '.cancel-purchase-btn', handleCancelPurchase);
        $(document).on('click', '.add-blacklist-btn', handleAddBlacklist);
        $(document).on('click', '.remove-blacklist-btn', handleRemoveBlacklist);
        $(document).on('click', '.view-blacklist-history', handleViewBlacklistHistory);
        $(document).on('click', '.send_order', handleSendOrder);
        $(document).on('click', '.btn-inventory', handleInventory);
        $(document).on('click', '.btn-inventory-multi', handleInventoryMulti);
        // 工具栏按钮
        $(document).on('click', '#toggleTitleBtn', handleToggleTitle);
        $(document).on('click', '#sortOrderTimeBtn', handleSortOrderTime);
        
        // 表单提交
        form.on('submit(search)', handleSearchSubmit);
        form.on('switch(user_auto_close_switch)', handleUserAutoSwitch);
        
        // 表格工具栏
        table.on('toolbar(orderTable)', handleTableToolbar);
    }

    // 各种事件处理函数
    function handleOrderStatus() {
        var $this = $(this),
            posting_number = $this.data('posting_number');
        if (!posting_number) return;
        
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=orders_updata',
                type: 'POST',
                data: { posting_number },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll();
                    if (res.code === 0) {
                        updateSingleOrder(posting_number);
                        layer.msg('同步成功', { icon: 1 });
                    } else {
                        layer.msg(res.msg || '同步失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 备货订单处理
    function handleStockingOrder() {
        var $this = $(this),
            posting_number = $this.data('posting_number');
        if (!posting_number) return;
    
        layer.confirm('确定要备货吗？', {
            icon: 3,
            title: '备货确认',
            btn: ['确定', '取消']
        }, function(index) {
            layer.close(index);
            requestQueue.add(function() {
                $.ajax({
                    url: 'ajax_order.php?act=order_package',
                    type: 'POST',
                    data: { posting_number },
                    success: function(res) {
                        requestQueue.complete();
                        if (res.code === 0) {
                            updateSingleOrder(posting_number);
                            layer.msg('备货成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '备货失败', { icon: 2 });
                        }
                    },
                    error: function(xhr) {
                        requestQueue.complete();
                        layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                    }
                });
            });
        });
    }
    
    // 订单采购处理
    function handleOrderPurchase() {
        var $this = $(this),
            orderId = $this.data('order_id'),
            posting_number = $this.data('posting_number');
    
        if (!orderId || !posting_number) {
            layer.msg('订单信息不完整', { icon: 2 });
            return;
        }
    
        requestQueue.add(function() {
            $.ajax({
                url: 'ajax_order.php?act=get_single_order',
                type: 'POST',
                data: { order_id: orderId, posting_number: posting_number },
                success: function(res) {
                    requestQueue.complete();
                    if (res.code === 0 && res.data) {
                        var data = res.data;
                        openEditWindow(
                            posting_number, 
                            data.purchaseCost, 
                            data.purchase_orderSn,
                            data.price,
                            data.delivery,
                            data.commissions,
                            data.profit,
                            data.percent,
                            data.weight,
                            data.out_weight,
                            data.courierNumber,
                            data.sku,
                            data.quantity,
                            data.offer_id,
                            posting_number
                        );
                    } else {
                        layer.msg(res.msg || '获取订单详情失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 订单备注处理
    function handleOrderNotes() {
        var $this = $(this),
            posting_number = $this.data('posting_number');
        if (!posting_number) return;
    
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=get_order_notes',
                type: 'POST',
                data: { posting_number: posting_number },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll();
                    if (res.code === 0) {
                        openNotesWindow(posting_number, res.data.OrderNotes || '');
                    } else {
                        layer.msg(res.msg || '获取备注失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 订单面单处理
    function handleOrderSelfOnly() {
        var $this = $(this),
            posting_number = $this.data('posting_number');
        if (!posting_number) {
            layer.msg('订单号不能为空', { icon: 2 });
            return;
        }
    
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=order_preview',
                type: 'POST',
                data: { posting_number },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll();
                    if (res.code === 0) {
                        directPrint(res.url);
                    } else {
                        layer.msg(res.msg || '获取面单失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 取消采购关联
    function handleCancelPurchase() {
        var $this = $(this),
            posting_number = $this.data('posting_number');
    
        layer.confirm('确定要取消关联的采购订单吗？<br><span style="color: #FF5722;">此操作将清空采购成本和采购单号！</span>', {
            icon: 3,
            title: '强制取消关联订单',
            btn: ['确定取消', '保持关联']
        }, function(index) {
            layer.close(index);
            requestQueue.add(function() {
                layer.load(2);
                $.ajax({
                    url: 'ajax_order.php?act=cancel_purchase_relation',
                    type: 'POST',
                    data: { posting_number: posting_number },
                    success: function(res) {
                        requestQueue.complete();
                        layer.closeAll();
                        if (res.code === 0) {
                            updateSingleOrder(posting_number);
                            layer.msg('采购关联已取消', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '取消关联失败', { icon: 2 });
                        }
                    },
                    error: function(xhr) {
                        requestQueue.complete();
                        layer.closeAll();
                        layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                    }
                });
            });
        });
    }
    
    // 添加到黑名单
    function handleAddBlacklist() {
        var $this = $(this),
            posting_number = $this.data('posting_number'),
            customer_name = $this.data('customer_name');
    
        if (!posting_number || !customer_name) {
            layer.msg('订单号或客户姓名不能为空', { icon: 2 });
            return;
        }
    
        showAddBlacklistModal(posting_number, customer_name);
    }
    
    // 从黑名单移除
    function handleRemoveBlacklist() {
        var $this = $(this),
            customer_name = $this.data('customer_name'),
            posting_number = $this.data('posting_number');
    
        if (!customer_name) {
            layer.msg('客户姓名不能为空', { icon: 2 });
            return;
        }
    
        requestQueue.add(function() {
            $.ajax({
                url: 'ajax_order.php?act=get_blacklist_count_by_customer',
                type: 'POST',
                data: { customer_name: customer_name },
                success: function(res) {
                    requestQueue.complete();
                    if (res.code === 0) {
                        var count = res.data.count;
                        var content = '';
    
                        if (count > 1) {
                            content = `
                                <div style="padding: 20px;">
                                    <p>客户 "${customer_name}" 在 ${count} 个订单中被添加到黑名单。</p>
                                    <p>请选择移除方式：</p>
                                    <div class="layui-form">
                                        <input type="radio" name="remove_type" value="single" title="仅移除当前订单的黑名单记录" checked>
                                        <input type="radio" name="remove_type" value="all" title="移除该客户的所有黑名单记录">
                                    </div>
                                </div>
                            `;
    
                            layer.open({
                                type: 1,
                                title: '选择移除方式',
                                area: ['450px', '250px'],
                                content: content,
                                btn: ['确定移除', '取消'],
                                btn1: function(index, layero) {
                                    var remove_type = layero.find('input[name="remove_type"]:checked').val();
                                    layer.close(index);
                                    performRemove(customer_name, posting_number, remove_type);
                                },
                                success: function(layero, index) {
                                    form.render();
                                }
                            });
                        } else {
                            layer.confirm('确定要将 "' + customer_name + '" 从黑名单中移除吗？', {
                                icon: 3,
                                title: '移除黑名单确认',
                                btn: ['确定移除', '取消']
                            }, function(index) {
                                layer.close(index);
                                performRemove(customer_name, posting_number, 'all');
                            });
                        }
                    } else {
                        layer.msg(res.msg || '查询失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 查看黑名单历史
    function handleViewBlacklistHistory() {
        var $this = $(this),
            customer_name = $this.data('customer_name');
    
        if (!customer_name) {
            layer.msg('客户姓名不能为空', { icon: 2 });
            return;
        }
    
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=get_customer_blacklist_history',
                type: 'POST',
                data: { customer_name: customer_name },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll();
                    if (res.code === 0) {
                        showBlacklistHistory(customer_name, res.data);
                    } else {
                        layer.msg(res.msg || '获取历史记录失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 申请出货处理
    function handleSendOrder() {
        var $this = $(this),
            posting_number = $this.data('posting_number');
        if (!posting_number) return;
    
        layer.confirm('确定要申请出货吗？', {
            icon: 3,
            title: '申请出货确认',
            btn: ['确定', '取消']
        }, function(index) {
            layer.close(index);
            requestQueue.add(function() {
                layer.load(2);
                $.ajax({
                    url: 'ajax_order.php?act=send_order',
                    type: 'POST',
                    data: { posting_number },
                    success: function(res) {
                        requestQueue.complete();
                        layer.closeAll();
                        if (res.code === 0) {
                            updateSingleOrder(posting_number);
                            layer.msg('申请出货成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '申请出货失败', { icon: 2 });
                        }
                    },
                    error: function(xhr) {
                        requestQueue.complete();
                        layer.closeAll();
                        layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                    }
                });
            });
        });
    }
    
    // 库存调用处理
    function handleInventory() {
        var $btn = $(this);
        var sku = $btn.data('sku');
        var posting_number = $btn.data('posting_number');
    
        if (!sku) {
            layer.msg('SKU为空，无法查询库存', { icon: 2 });
            return;
        }
    
        sku = String(sku);
        var skuList = sku.split(',').map(function(item) { return item.trim(); }).filter(Boolean).join(',');
        
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax.php',
                method: 'GET',
                data: { act: 'query_platform_sku', sku_list: skuList },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        if (res.data.length === 0) {
                            layer.msg('无匹配库存记录', { icon: 0 });
                            return;
                        }
                        showInventoryForm(res.data, posting_number, sku);
                    } else {
                        layer.msg(res.msg || '查询失败', { icon: 2 });
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });
    }
    
    // 多商品库存调用处理
    function handleInventoryMulti() {
        var $btn = $(this);
        var posting_number = $btn.data('posting_number');
    
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php',
                method: 'GET',
                data: { act: 'get_order_products', posting_number: posting_number },
                success: function(res) {
                    requestQueue.complete();
                    if (res.code === 0 && res.data && res.data.products_info) {
                        var products = res.data.products_info;
                        if (products.length === 0) {
                            layer.msg('订单中没有商品信息', { icon: 0 });
                            return;
                        }
    
                        var allSkus = products.map(function(item) { return item.sku; }).join(',');
                        
                        requestQueue.add(function() {
                            layer.load(2);
                            $.ajax({
                                url: 'ajax.php',
                                method: 'GET',
                                data: { act: 'query_platform_sku', sku_list: allSkus },
                                success: function(inventoryRes) {
                                    requestQueue.complete();
                                    layer.closeAll('loading');
                                    if (inventoryRes.code === 0) {
                                        if (inventoryRes.data.length === 0) {
                                            layer.msg('无匹配库存记录', { icon: 0 });
                                            return;
                                        }
                                        
                                        var missingSkus = [];
                                        products.forEach(function(product) {
                                            if (!inventoryRes.data.some(item => item.sku === product.sku)) {
                                                missingSkus.push(product.sku);
                                            }
                                        });
    
                                        if (missingSkus.length > 0) {
                                            layer.confirm('以下SKU没有找到库存记录: ' + missingSkus.join(', ') + '<br>是否继续?', {
                                                btn: ['继续', '取消']
                                            }, function(index) {
                                                layer.close(index);
                                                showMultiInventoryForm(inventoryRes.data, posting_number, allSkus, products);
                                            });
                                        } else {
                                            showMultiInventoryForm(inventoryRes.data, posting_number, allSkus, products);
                                        }
                                    } else {
                                        layer.msg(inventoryRes.msg || '查询库存失败', { icon: 2 });
                                    }
                                },
                                error: function() {
                                    requestQueue.complete();
                                    layer.closeAll('loading');
                                    layer.msg('请求失败，请检查网络', { icon: 2 });
                                }
                            });
                        });
                    } else {
                        layer.msg(res.msg || '获取订单商品信息失败', { icon: 2 });
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });
    }
    
    // 切换标题语言
    function handleToggleTitle() {
        appState.isChineseTitle = !appState.isChineseTitle;
        var btn = this;
        if (appState.isChineseTitle) {
            btn.innerText = '切换到俄文标题';
            $('[id^="title_chinese_"]').show();
            $('[id^="title_russian_"]').hide();
        } else {
            btn.innerText = '切换到中文标题';
            $('[id^="title_chinese_"]').hide();
            $('[id^="title_russian_"]').show();
        }
    }
    
    // 排序处理
    function handleSortOrderTime() {
        var newOrder = appState.sortOrder === 'desc' ? 'asc' : 'desc';
        appState.sortOrder = newOrder;
        
        renderTable(
            appState.currentStatus, 
            appState.searchParams.text, 
            appState.searchParams.money, 
            appState.searchParams.moneys, 
            appState.searchParams.storeid, 
            appState.searchParams.date1, 
            appState.searchParams.date2, 
            appState.searchParams.groupid, 
            'in_process_at', 
            appState.sortOrder, 
            appState.searchParams.paypt
        );
    }
    
    // 搜索表单提交
    function handleSearchSubmit(data) {
        var field = data.field;
        appState.searchParams = {
            text: field.text || '',
            money: field.money || '',
            moneys: field.moneys || '',
            storeid: field.storeid || '',
            date1: field.date1 || '',
            date2: field.date2 || '',
            groupid: field.groupid || '',
            paypt: field.paypt || ''
        };
        
        renderTable(
            appState.currentStatus, 
            appState.searchParams.text, 
            appState.searchParams.money, 
            appState.searchParams.moneys, 
            appState.searchParams.storeid, 
            appState.searchParams.date1, 
            appState.searchParams.date2, 
            appState.searchParams.groupid, 
            'in_process_at', 
            appState.sortOrder, 
            appState.searchParams.paypt
        );
        return false;
    }
    
    // 用户自动关闭开关处理
    function handleUserAutoSwitch(obj) {
        var id = $(this).data('id');
        var status = obj.elem.checked ? 1 : 0;
        
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=user_auto_switch',
                type: 'POST',
                data: { id: id, status: status },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code != 0) {
                        layer.msg(res.msg || '操作失败');
                        obj.elem.checked = !status;
                        form.render('checkbox');
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络');
                    obj.elem.checked = !status;
                    form.render('checkbox');
                }
            });
        });
    }

    // 表格工具栏处理
    // 表格工具栏处理
    function handleTableToolbar(obj) {
        if (obj.event === 'getCheckData') {
            var checkboxes = document.querySelectorAll('.order-checkbox:checked');
            var posting_numbers = [];
            checkboxes.forEach(function(checkbox) {
                posting_numbers.push(checkbox.getAttribute('data-posting-number'));
            });
    
            if (posting_numbers.length === 0) {
                layer.msg('请先选择要操作的订单', { icon: 2 });
                return;
            }
    
            // 显示批量操作菜单
            layer.open({
                type: 1,
                title: '批量操作 (' + posting_numbers.length + '个订单)',
                area: ['300px', '250px'],
                content: `
                    <div style="padding: 15px;">
                        <div class="layui-form">
                            <div class="layui-form-item" style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid" id="batchPrintBtn" style="margin-bottom: 10px;">
                                    <i class="layui-icon layui-icon-print"></i> 批量打印面单
                                </button>
                            </div>
                            <div class="layui-form-item" style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-warm" id="batchUpdateStatusBtn">
                                    <i class="layui-icon layui-icon-refresh"></i> 批量更新状态
                                </button>
                            </div>
                            <div class="layui-form-item">
                                <button class="layui-btn layui-btn-fluid layui-btn-danger" id="batchCancelBtn">
                                    <i class="layui-icon layui-icon-close"></i> 批量取消订单
                                </button>
                            </div>
                        </div>
                    </div>
                `,
                success: function(layero, index) {
                    // 批量打印按钮点击事件
                    $(layero).on('click', '#batchPrintBtn', function() {
                        layer.close(index);
                        
                        // 检查订单状态是否允许打印
                        requestQueue.add(function() {
                            layer.load(2);
                            $.ajax({
                                url: 'ajax_order.php?act=check_batch_print_status',
                                type: 'POST',
                                data: { posting_numbers: posting_numbers },
                                success: function(res) {
                                    requestQueue.complete();
                                    layer.closeAll('loading');
                                    
                                    if (res.code === 0) {
                                        if (res.data.invalid_orders && res.data.invalid_orders.length > 0) {
                                            // 有不符合条件的订单
                                            layer.confirm(
                                                '以下订单状态不符合打印条件:<br>' + 
                                                res.data.invalid_orders.join(', ') + 
                                                '<br>是否继续打印符合条件的订单?',
                                                {
                                                    btn: ['继续打印', '取消'],
                                                    area: '500px'
                                                },
                                                function(i) {
                                                    layer.close(i);
                                                    batchPrintWaybills(res.data.valid_orders);
                                                }
                                            );
                                        } else {
                                            // 全部订单都符合条件
                                            batchPrintWaybills(posting_numbers);
                                        }
                                    } else {
                                        layer.msg(res.msg || '检查订单状态失败', { icon: 2 });
                                    }
                                },
                                error: function(xhr) {
                                    requestQueue.complete();
                                    layer.closeAll('loading');
                                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                                }
                            });
                        });
                    });
    
                    // 批量更新状态按钮点击事件
                    $(layero).on('click', '#batchUpdateStatusBtn', function() {
                        layer.close(index);
                        showBatchStatusUpdateDialog(posting_numbers);
                    });
    
                    // 批量取消按钮点击事件
                    $(layero).on('click', '#batchCancelBtn', function() {
                        layer.close(index);
                        showBatchCancelDialog(posting_numbers);
                    });
                }
            });
        }
    }
    
    // 显示批量更新状态对话框
    function showBatchStatusUpdateDialog(posting_numbers) {
        var content = `
            <div style="padding: 20px;">
                <form class="layui-form" lay-filter="batchStatusForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">目标状态</label>
                        <div class="layui-input-block">
                            <select name="target_status" lay-verify="required">
                                <option value="">请选择状态</option>
                                <option value="packaged">已打包</option>
                                <option value="shipped">已发货</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" placeholder="请输入状态变更备注(可选)" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        `;
    
        layer.open({
            type: 1,
            title: '批量更新状态 (' + posting_numbers.length + '个订单)',
            area: ['500px', '300px'],
            content: content,
            btn: ['确认更新', '取消'],
            btn1: function(index, layero) {
                var formData = form.val('batchStatusForm');
                if (!formData.target_status) {
                    layer.msg('请选择目标状态', { icon: 2 });
                    return false;
                }
    
                requestQueue.add(function() {
                    layer.load(2);
                    $.ajax({
                        url: 'ajax_order.php?act=batch_update_status',
                        type: 'POST',
                        data: {
                            posting_numbers: posting_numbers,
                            target_status: formData.target_status,
                            remark: formData.remark || ''
                        },
                        success: function(res) {
                            requestQueue.complete();
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.msg('成功更新 ' + res.data.success_count + ' 个订单状态', { 
                                    icon: 1,
                                    time: 2000
                                }, function() {
                                    table.reload('orderTable');
                                });
                                layer.close(index);
                            } else {
                                layer.msg(res.msg || '批量更新失败', { icon: 2 });
                            }
                        },
                        error: function(xhr) {
                            requestQueue.complete();
                            layer.closeAll('loading');
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                });
            },
            success: function(layero, index) {
                form.render();
            }
        });
    }
    
    // 显示批量取消对话框
    function showBatchCancelDialog(posting_numbers) {
        var content = `
            <div style="padding: 20px;">
                <form class="layui-form" lay-filter="batchCancelForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">取消原因</label>
                        <div class="layui-input-block">
                            <select name="cancel_reason" lay-verify="required">
                                <option value="">请选择原因</option>
                                <option value="out_of_stock">缺货</option>
                                <option value="customer_request">客户要求</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text" id="cancelRemarkDiv" style="display: none;">
                        <label class="layui-form-label">详细说明</label>
                        <div class="layui-input-block">
                            <textarea name="cancel_remark" placeholder="请输入取消订单的详细说明" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        `;
    
        layer.open({
            type: 1,
            title: '批量取消订单 (' + posting_numbers.length + '个订单)',
            area: ['500px', '300px'],
            content: content,
            btn: ['确认取消', '返回'],
            btn1: function(index, layero) {
                var formData = form.val('batchCancelForm');
                if (!formData.cancel_reason) {
                    layer.msg('请选择取消原因', { icon: 2 });
                    return false;
                }
    
                if (formData.cancel_reason === 'other' && !formData.cancel_remark) {
                    layer.msg('选择其他原因时必须填写详细说明', { icon: 2 });
                    return false;
                }
    
                layer.confirm(
                    '确定要取消这 ' + posting_numbers.length + ' 个订单吗？<br><span style="color: red;">此操作不可撤销！</span>',
                    {
                        icon: 3,
                        title: '确认批量取消',
                        btn: ['确定取消', '返回']
                    },
                    function(i) {
                        layer.close(i);
                        
                        requestQueue.add(function() {
                            layer.load(2);
                            $.ajax({
                                url: 'ajax_order.php?act=batch_cancel_orders',
                                type: 'POST',
                                data: {
                                    posting_numbers: posting_numbers,
                                    cancel_reason: formData.cancel_reason,
                                    cancel_remark: formData.cancel_remark || ''
                                },
                                success: function(res) {
                                    requestQueue.complete();
                                    layer.closeAll('loading');
                                    if (res.code === 0) {
                                        layer.msg('成功取消 ' + res.data.success_count + ' 个订单', { 
                                            icon: 1,
                                            time: 2000
                                        }, function() {
                                            table.reload('orderTable');
                                        });
                                        layer.close(index);
                                    } else {
                                        layer.msg(res.msg || '批量取消失败', { icon: 2 });
                                    }
                                },
                                error: function(xhr) {
                                    requestQueue.complete();
                                    layer.closeAll('loading');
                                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                                }
                            });
                        });
                    }
                );
            },
            success: function(layero, index) {
                form.render();
                
                form.on('select(cancel_reason)', function(data) {
                    var $cancelRemarkDiv = layero.find('#cancelRemarkDiv');
                    if (data.value === 'other') {
                        $cancelRemarkDiv.show();
                    } else {
                        $cancelRemarkDiv.hide();
                    }
                });
            }
        });
    }

                
    // 显示添加黑名单弹窗
    function showAddBlacklistModal(posting_number, customer_name) {
        var content = `
            <div style="padding: 20px;">
                <form class="layui-form" lay-filter="blacklistForm">
                    <input type="hidden" name="posting_number" value="${posting_number}">
                    <input type="hidden" name="customer_name" value="${customer_name}">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">客户姓名</label>
                        <div class="layui-input-block">
                            <input type="text" value="${customer_name}" readonly class="layui-input layui-disabled">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">黑名单原因</label>
                        <div class="layui-input-block">
                            <input type="radio" name="reason_type" value="1" title="频繁退货退款" checked>
                            <input type="radio" name="reason_type" value="2" title="恶意差评">
                            <input type="radio" name="reason_type" value="3" title="货到付款拒收">
                            <input type="radio" name="reason_type" value="4" title="其他">
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-form-text" id="reasonTextDiv" style="display: none;">
                        <label class="layui-form-label">详细原因</label>
                        <div class="layui-input-block">
                            <textarea name="reason_text" placeholder="请输入详细原因..." class="layui-textarea" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        `;
    
        layer.open({
            type: 1,
            title: '添加到黑名单',
            area: ['500px', '400px'],
            content: content,
            btn: ['确定添加', '取消'],
            btn1: function(index, layero) {
                var formData = form.val('blacklistForm');
                if (formData.reason_type === '4' && !formData.reason_text.trim()) {
                    layer.msg('选择其他原因时必须填写详细原因', { icon: 2 });
                    return false;
                }
    
                requestQueue.add(function() {
                    layer.load(2);
                    $.ajax({
                        url: 'ajax_order.php?act=add_blacklist',
                        type: 'POST',
                        data: formData,
                        success: function(res) {
                            requestQueue.complete();
                            layer.closeAll();
                            if (res.code === 0) {
                                layer.msg('已添加到黑名单', { icon: 1 });
                                layer.close(index);
                                updateSingleOrder(posting_number);
                            } else {
                                layer.msg(res.msg || '添加失败', { icon: 2 });
                            }
                        },
                        error: function(xhr) {
                            requestQueue.complete();
                            layer.closeAll();
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                });
            },
            success: function(layero, index) {
                form.render();
                form.on('radio()', function(data) {
                    if (data.elem.name === 'reason_type') {
                        var $reasonTextDiv = layero.find('#reasonTextDiv');
                        if (data.value === '4') {
                            $reasonTextDiv.show();
                        } else {
                            $reasonTextDiv.hide();
                        }
                    }
                });
            }
        });
    }
    
    // 执行移除黑名单操作
    function performRemove(customer_name, posting_number, remove_type) {
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=remove_blacklist',
                type: 'POST',
                data: {
                    customer_name: customer_name,
                    posting_number: posting_number,
                    remove_type: remove_type
                },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll();
                    if (res.code === 0) {
                        layer.msg('已从黑名单中移除', { icon: 1 });
                        updateSingleOrder(posting_number);
                    } else {
                        layer.msg(res.msg || '移除失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 显示黑名单历史
    function showBlacklistHistory(customer_name, history) {
        var content = `
            <div style="padding: 20px;">
                <h3 style="margin-bottom: 15px; color: #333;">客户 "${customer_name}" 的黑名单历史</h3>
                <div style="max-height: 400px; overflow-y: auto;">
        `;
    
        if (history.length === 0) {
            content += '<p style="text-align: center; color: #999;">暂无黑名单记录</p>';
        } else {
            content += '<table class="layui-table" style="margin: 0;">';
            content += '<thead><tr><th>订单号</th><th>原因</th><th>详细说明</th><th>添加时间</th></tr></thead>';
            content += '<tbody>';
    
            history.forEach(function(item) {
                content += '<tr>';
                content += '<td>' + item.posting_number + '</td>';
                content += '<td><span style="color: #FF5722;">' + item.reason_type_text + '</span></td>';
                content += '<td>' + (item.reason_text || '-') + '</td>';
                content += '<td>' + item.created_at + '</td>';
                content += '</tr>';
            });
    
            content += '</tbody></table>';
        }
    
        content += `
                </div>
                <div style="text-align: center; margin-top: 15px; color: #999;">
                    共 ${history.length} 条记录
                </div>
            </div>
        `;
    
        layer.open({
            type: 1,
            title: '黑名单历史',
            area: ['700px', '500px'],
            content: content,
            btn: ['关闭'],
            btn1: function(index) {
                layer.close(index);
            }
        });
    }
    
    // 打开备注编辑窗口
    function openNotesWindow(posting_number, currentNotes) {
        layer.open({
            type: 1,
            title: '订单备注 - ' + posting_number,
            area: ['500px', '400px'],
            content: `
                <form class="layui-form" lay-filter="notesForm" style="padding: 20px;">
                    <input type="hidden" name="posting_number" value="${posting_number}">
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">订单备注</label>
                        <div class="layui-input-block">
                            <textarea name="notes" placeholder="请输入订单备注信息..." class="layui-textarea" style="height: 200px;">${currentNotes}</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="saveNotes">保存备注</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            `,
            success: function(layero, index) {
                form.render();
                form.on('submit(saveNotes)', function(formData) {
                    var field = formData.field;
                    requestQueue.add(function() {
                        layer.load(2);
                        $.ajax({
                            url: 'ajax_order.php?act=update_order_notes',
                            type: 'POST',
                            data: {
                                posting_number: field.posting_number,
                                notes: field.notes
                            },
                            success: function(res) {
                                requestQueue.complete();
                                layer.closeAll();
                                if (res.code === 0) {
                                    layer.msg('备注保存成功', { icon: 1 });
                                    layer.close(index);
                                    table.reload('orderTable');
                                } else {
                                    layer.msg(res.msg || '保存失败', { icon: 2 });
                                }
                            },
                            error: function(xhr) {
                                requestQueue.complete();
                                layer.closeAll();
                                layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                            }
                        });
                    });
                    return false;
                });
            }
        });
    }
    
    // 打开编辑窗口
    function openEditWindow(posting_number, purchaseCost, purchase_orderSn, price, delivery, commissions, profit, percent, weight, out_weight, courierNumber, sku, quantity, offer_id) {
        var courierNumbers = [];
        var purchaseOrderSns = [];
    
        if (courierNumber && typeof courierNumber === 'string') {
            courierNumbers = courierNumber.split(',').map(item => item.trim()).filter(Boolean);
        }
    
        if (purchase_orderSn && typeof purchase_orderSn === 'string') {
            purchaseOrderSns = purchase_orderSn.split(',').map(item => item.trim()).filter(Boolean);
        }
    
        var content = `
            <div style="padding: 12px;">
                <h3 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #1E9FFF; padding-bottom: 6px; font-size: 15px;">
                    <i class="layui-icon layui-icon-edit" style="color: #1E9FFF;"></i> 编辑采购信息
                </h3>
                
                <form class="layui-form" lay-filter="purchaseEditForm">
                    <input type="hidden" name="posting_number" value="${posting_number}">
                    
                    <!-- 基础信息 -->
                    <div class="layui-row layui-col-space10" style="margin-bottom: 12px;">
                        <div class="layui-col-md6">
                            <div class="layui-form-item" style="margin-bottom: 6px;">
                                <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">采购成本</label>
                                <div class="layui-input-block" style="margin-left: 80px;">
                                    <input type="text" name="cost" value="${purchaseCost || ''}" placeholder="采购成本" class="layui-input" id="editCostInput">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item" style="margin-bottom: 6px;">
                                <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">商品数量</label>
                                <div class="layui-input-block" style="margin-left: 80px;">
                                    <input type="text" value="${quantity || 1}" readonly class="layui-input layui-disabled">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 价格信息 -->
                    <div class="layui-row layui-col-space10" style="margin-bottom: 12px;">
                        <div class="layui-col-md6">
                            <div class="layui-form-item" style="margin-bottom: 6px;">
                                <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">单价 (¥)</label>
                                <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                    <input type="text" id="editMoneyInput" value="${price || ''}" class="layui-input" placeholder="单价" style="margin-right: 6px;">
                                    <button type="button" id="updatePriceBtn" class="layui-btn layui-btn-normal layui-btn-xs">
                                        <i class="layui-icon layui-icon-refresh"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-form-item" style="margin-bottom: 6px;">
                                <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">总价 (¥)</label>
                                <div class="layui-input-block" style="margin-left: 80px;">
                                      <input type="text" id="editPriceInput" value="${(parseFloat(price || 0) * (quantity || 1)).toFixed(2)}" class="layui-input" placeholder="总价">
                              
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 物流单号 -->
                    <div id="courierNumberContainer" style="margin-bottom: 12px;">
                        <div class="layui-form-item courier-item" style="margin-bottom: 6px;">
                            <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">物流单号</label>
                            <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                <input type="text" name="courierNumber[]" value="${courierNumbers[0] || ''}" placeholder="物流单号" class="layui-input" style="margin-right: 6px;">
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs add-courier">
                                    <i class="layui-icon layui-icon-add-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 采购单号 -->
                    <div id="purchaseOrderContainer" style="margin-bottom: 12px;">
                        <div class="layui-form-item purchase-item" style="margin-bottom: 6px;">
                            <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">采购单号</label>
                            <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                <input type="text" name="purchase_orderSn[]" value="${purchaseOrderSns[0] || ''}" placeholder="采购单号" class="layui-input" style="margin-right: 6px;">
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-xs add-purchase">
                                    <i class="layui-icon layui-icon-add-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 利润分析 -->
                    <div style="background: #f8f9fa; border-radius: 3px; padding: 8px; border-left: 3px solid #1E9FFF;">
                        <h4 style="margin: 0 0 8px 0; color: #333; font-size: 13px;">
                            <i class="layui-icon layui-icon-chart" style="color: #1E9FFF;"></i> 利润分析
                        </h4>
                        <div class="layui-row layui-col-space5">
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                    <div style="color: #666; font-size: 11px;">物流费</div>
                                    <div id="editDelivery" style="font-weight: bold; color: #333; font-size: 12px;">${delivery || '0.00'} ¥</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                    <div style="color: #666; font-size: 11px;">平台佣金</div>
                                    <div id="editCommissions" style="font-weight: bold; color: #333; font-size: 12px;">${commissions || '0.00'} ¥</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                    <div style="color: #666; font-size: 11px;">佣金比例</div>
                                    <div id="editPercent" style="font-weight: bold; color: #333; font-size: 12px;">${percent || '0'} %</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                    <div style="color: #666; font-size: 11px;">重量(kg)</div>
                                    <div id="editWeight" style="font-weight: bold; color: #333; font-size: 12px;">${weight || '0.00'}/${out_weight || '0.00'}</div>
                                </div>
                            </div>
                        </div>
                        <div style="background: #2c3e50; padding: 10px; border-radius: 3px; text-align: center; margin-top: 8px; border: 2px solid #34495e;">
                            <div style="color: #bdc3c7; font-size: 11px; margin-bottom: 3px;">预估利润</div>
                            <div id="editProfit" style="color: #f1c40f; font-size: 18px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">${profit || '0.00'} ¥</div>
                        </div>
                    </div>
                </form>
            </div>
        `;
    
        layer.closeAll();
        var editIndex = layer.open({
            type: 1,
            title: false,
            area: ['600px', '580px'],
            shade: [0.3, '#000'],
            closeBtn: 1,
            content: content,
            btn: ['保存修改', '取消'],
            btnAlign: 'c',
            btn1: function(index, layero) {
                savePurchaseInfo(layero, index);
            },
            success: function(layero, index) {
                // 初始化表单
                form.render();
                
                // 添加物流单号输入框
                $(layero).on('click', '.add-courier', function() {
                    var $container = $('#courierNumberContainer');
                    var $newItem = $(`
                        <div class="layui-form-item courier-item" style="margin-bottom: 6px;">
                            <label class="layui-form-label" style="width: 70px; padding: 8px 5px;"></label>
                            <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                <input type="text" name="courierNumber[]" placeholder="物流单号" class="layui-input" style="margin-right: 6px;">
                                <button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-courier">
                                    <i class="layui-icon layui-icon-delete"></i>
                                </button>
                            </div>
                        </div>
                    `);
                    $container.append($newItem);
                    form.render();
                });
    
                // 移除物流单号输入框
                $(layero).on('click', '.remove-courier', function() {
                    if ($('#courierNumberContainer .courier-item').length > 1) {
                        $(this).closest('.courier-item').remove();
                    } else {
                        layer.msg('至少保留一个物流单号输入框', { icon: 0 });
                    }
                });
    
                // 添加采购单号输入框
                $(layero).on('click', '.add-purchase', function() {
                    var $container = $('#purchaseOrderContainer');
                    var $newItem = $(`
                        <div class="layui-form-item purchase-item" style="margin-bottom: 6px;">
                            <label class="layui-form-label" style="width: 70px; padding: 8px 5px;"></label>
                            <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                <input type="text" name="purchase_orderSn[]" placeholder="采购单号" class="layui-input" style="margin-right: 6px;">
                                <button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-purchase">
                                    <i class="layui-icon layui-icon-delete"></i>
                                </button>
                            </div>
                        </div>
                    `);
                    $container.append($newItem);
                    form.render();
                });
    
                // 移除采购单号输入框
                $(layero).on('click', '.remove-purchase', function() {
                    if ($('#purchaseOrderContainer .purchase-item').length > 1) {
                        $(this).closest('.purchase-item').remove();
                    } else {
                        layer.msg('至少保留一个采购单号输入框', { icon: 0 });
                    }
                });
    
                // 实时计算利润
                $(layero).on('input', '#editCostInput, #editMoneyInput', function() {
                    calculateProfit(layero);
                });
    
                // 更新价格按钮
                $(layero).on('click', '#updatePriceBtn', function() {
                    requestQueue.add(function() {
                        layer.load(2);
                        $.ajax({
                            url: 'ajax_order.php?act=get_latest_price',
                            type: 'POST',
                            data: { 
                                posting_number: posting_number,
                                offer_id: offer_id
                            },
                            success: function(res) {
                                requestQueue.complete();
                                layer.closeAll('loading');
                                if (res.code === 0) {
                                    $('#editMoneyInput').val(res.data.price);
                                    $('#editPriceInput').val(res.data.price * (quantity || 1));
                                    calculateProfit(layero);
                                    layer.msg('价格更新成功', { icon: 1 });
                                } else {
                                    layer.msg(res.msg || '获取最新价格失败', { icon: 2 });
                                }
                            },
                            error: function(xhr) {
                                requestQueue.complete();
                                layer.closeAll('loading');
                                layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                            }
                        });
                    });
                });
    
                // 初始计算利润
                calculateProfit(layero);
            }
        });
    
        // 计算利润函数
        function calculateProfit(layero) {
            var cost = parseFloat($('#editCostInput').val()) || 0;
            var price = parseFloat($('#editMoneyInput').val()) || 0;
            var deliveryCost = parseFloat(delivery) || 0;
            var commissionRate = parseFloat(percent) || 0;
            
            // 计算佣金
            var commission = price * (commissionRate / 100);
            
            // 计算利润
            var calculatedProfit = price - cost - deliveryCost - commission;
            
            // 更新显示
            $('#editProfit').text(calculatedProfit.toFixed(2) + ' ¥');
            
            // 根据利润值设置颜色
            if (calculatedProfit > 0) {
                $('#editProfit').css('color', '#5FB878');
            } else if (calculatedProfit < 0) {
                $('#editProfit').css('color', '#FF5722');
            } else {
                $('#editProfit').css('color', '#f1c40f');
            }
        }
    }

            
    // 显示库存表单
    function showInventoryForm(inventoryData, posting_number, sku) {
        var content = `
            <div style="padding: 15px;">
                <h3 style="margin: 0 0 15px 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 8px;">
                    <i class="layui-icon layui-icon-form" style="color: #1E9FFF;"></i> 库存信息 - ${sku}
                </h3>
                
                <table class="layui-table" lay-size="sm" style="margin: 0;">
                    <thead>
                        <tr>
                            <th>平台</th>
                            <th>SKU</th>
                            <th>库存</th>
                            <th>价格</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
    
        inventoryData.forEach(function(item) {
            content += `
                <tr>
                    <td>${item.platform_name || '-'}</td>
                    <td>${item.sku || '-'}</td>
                    <td>${item.stock || 0}</td>
                    <td>${item.price || '0.00'} ¥</td>
                    <td>
                        <button class="layui-btn layui-btn-xs layui-btn-normal use-inventory-btn" 
                                data-platform="${item.platform}" 
                                data-sku="${item.sku}" 
                                data-price="${item.price}">
                            使用此库存
                        </button>
                    </td>
                </tr>
            `;
        });
    
        content += `
                    </tbody>
                </table>
                
                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">手动输入</label>
                            <div class="layui-input-inline">
                                <input type="text" id="manualCostInput" placeholder="输入采购成本" class="layui-input">
                            </div>
                            <div class="layui-input-inline">
                                <button class="layui-btn layui-btn-primary" id="manualSubmitBtn">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    
        layer.open({
            type: 1,
            title: false,
            area: ['700px', '500px'],
            shade: [0.3, '#000'],
            closeBtn: 1,
            content: content,
            success: function(layero, index) {
                // 使用库存按钮点击事件
                $(layero).on('click', '.use-inventory-btn', function() {
                    var $btn = $(this);
                    var platform = $btn.data('platform');
                    var sku = $btn.data('sku');
                    var price = $btn.data('price');
                    
                    layer.confirm(`确定使用 ${platform} 平台的库存 (价格: ${price} ¥) 吗？`, {
                        icon: 3,
                        title: '确认使用库存',
                        btn: ['确定', '取消']
                    }, function(index) {
                        layer.close(index);
                        updateOrderWithInventory(posting_number, sku, price, platform);
                    });
                });
    
                // 手动输入提交
                $(layero).on('click', '#manualSubmitBtn', function() {
                    var manualCost = $('#manualCostInput').val();
                    if (!manualCost || isNaN(manualCost)) {
                        layer.msg('请输入有效的采购成本', { icon: 2 });
                        return;
                    }
                    
                    updateOrderCost(posting_number, parseFloat(manualCost));
                });
            }
        });
    }
    
    // 显示多商品库存表单
    function showMultiInventoryForm(inventoryData, posting_number, skus, products) {
        var content = `
            <div style="padding: 15px;">
                <h3 style="margin: 0 0 15px 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 8px;">
                    <i class="layui-icon layui-icon-form" style="color: #1E9FFF;"></i> 多商品库存信息 - ${posting_number}
                </h3>
                
                <div style="margin-bottom: 15px;">
                    <table class="layui-table" lay-size="sm" style="margin: 0;">
                        <thead>
                            <tr>
                                <th>商品SKU</th>
                                <th>数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
    
        products.forEach(function(product) {
            content += `
                <tr>
                    <td>${product.sku || '-'}</td>
                    <td>${product.quantity || 1}</td>
                    <td>
                        <button class="layui-btn layui-btn-xs view-single-inventory" 
                                data-sku="${product.sku}" 
                                data-quantity="${product.quantity}">
                            查看库存
                        </button>
                    </td>
                </tr>
            `;
        });
    
        content += `
                        </tbody>
                    </table>
                </div>
                
                <div class="layui-tab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">批量设置</li>
                        <li>按平台设置</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div style="padding: 15px 0;">
                                <form class="layui-form">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">统一成本</label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="uniformCostInput" placeholder="输入统一采购成本" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <button class="layui-btn" id="uniformSubmitBtn">应用</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="layui-tab-item">
                            <div style="padding: 15px 0;">
                                <table class="layui-table" lay-size="sm" style="margin: 0;">
                                    <thead>
                                        <tr>
                                            <th>平台</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
        `;
    
        // 获取所有唯一平台
        var platforms = [...new Set(inventoryData.map(item => item.platform))];
        platforms.forEach(function(platform) {
            content += `
                <tr>
                    <td>${platform}</td>
                    <td>
                        <button class="layui-btn layui-btn-xs use-platform-inventory" 
                                data-platform="${platform}">
                            使用此平台所有库存
                        </button>
                    </td>
                </tr>
            `;
        });
    
        content += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    
        layer.open({
            type: 1,
            title: false,
            area: ['800px', '600px'],
            shade: [0.3, '#000'],
            closeBtn: 1,
            content: content,
            success: function(layero, index) {
                element.init();
                
                // 查看单个SKU库存
                $(layero).on('click', '.view-single-inventory', function() {
                    var sku = $(this).data('sku');
                    var quantity = $(this).data('quantity');
                    var skuInventory = inventoryData.filter(item => item.sku === sku);
                    
                    if (skuInventory.length > 0) {
                        showInventoryForm(skuInventory, posting_number, sku);
                    } else {
                        layer.msg('没有找到该SKU的库存信息', { icon: 0 });
                    }
                });
    
                // 统一成本提交
                $(layero).on('click', '#uniformSubmitBtn', function() {
                    var uniformCost = $('#uniformCostInput').val();
                    if (!uniformCost || isNaN(uniformCost)) {
                        layer.msg('请输入有效的采购成本', { icon: 2 });
                        return;
                    }
                    
                    layer.confirm(`确定将所有商品的采购成本设置为 ${uniformCost} ¥ 吗？`, {
                        icon: 3,
                        title: '确认批量设置',
                        btn: ['确定', '取消']
                    }, function(index) {
                        layer.close(index);
                        batchUpdateOrderCost(posting_number, parseFloat(uniformCost));
                    });
                });
    
                // 使用平台所有库存
                $(layero).on('click', '.use-platform-inventory', function() {
                    var platform = $(this).data('platform');
                    var platformInventory = inventoryData.filter(item => item.platform === platform);
                    
                    if (platformInventory.length === 0) {
                        layer.msg('该平台没有可用库存', { icon: 0 });
                        return;
                    }
                    
                    layer.confirm(`确定使用 ${platform} 平台的所有匹配库存吗？`, {
                        icon: 3,
                        title: '确认使用平台库存',
                        btn: ['确定', '取消']
                    }, function(index) {
                        layer.close(index);
                        usePlatformInventory(posting_number, platform, platformInventory, products);
                    });
                });
            }
        });
    }
    
    // 更新订单库存信息
    function updateOrderWithInventory(posting_number, sku, price, platform) {
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=update_order_with_inventory',
                type: 'POST',
                data: {
                    posting_number: posting_number,
                    sku: sku,
                    cost: price,
                    platform: platform
                },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg('库存信息更新成功', { icon: 1 });
                        updateSingleOrder(posting_number);
                        layer.closeAll('page');
                    } else {
                        layer.msg(res.msg || '更新失败', { icon: 2 });
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });
    }
    
    // 更新订单成本
    function updateOrderCost(posting_number, cost) {
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=update_order_cost',
                type: 'POST',
                data: {
                    posting_number: posting_number,
                    cost: cost
                },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg('采购成本更新成功', { icon: 1 });
                        updateSingleOrder(posting_number);
                        layer.closeAll('page');
                    } else {
                        layer.msg(res.msg || '更新失败', { icon: 2 });
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });
    }
    
    // 批量更新订单成本
    function batchUpdateOrderCost(posting_number, cost) {
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=batch_update_order_cost',
                type: 'POST',
                data: {
                    posting_number: posting_number,
                    cost: cost
                },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg('批量更新采购成本成功', { icon: 1 });
                        updateSingleOrder(posting_number);
                        layer.closeAll('page');
                    } else {
                        layer.msg(res.msg || '更新失败', { icon: 2 });
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });
    }
    
    // 使用平台库存
    function usePlatformInventory(posting_number, platform, inventoryData, products) {
        requestQueue.add(function() {
            layer.load(2);
            
            // 构建匹配数据
            var matchData = products.map(product => {
                var matchedInventory = inventoryData.find(item => item.sku === product.sku);
                return {
                    sku: product.sku,
                    quantity: product.quantity,
                    cost: matchedInventory ? matchedInventory.price : 0
                };
            });
    
            $.ajax({
                url: 'ajax_order.php?act=use_platform_inventory',
                type: 'POST',
                data: {
                    posting_number: posting_number,
                    platform: platform,
                    products: JSON.stringify(matchData)
                },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg('平台库存应用成功', { icon: 1 });
                        updateSingleOrder(posting_number);
                        layer.closeAll('page');
                    } else {
                        layer.msg(res.msg || '应用失败', { icon: 2 });
                    }
                },
                error: function() {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });
    }
    
    // 直接打印
    function directPrint(url) {
        var iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = url;
        document.body.appendChild(iframe);
        
        iframe.onload = function() {
            setTimeout(function() {
                iframe.contentWindow.print();
                setTimeout(function() {
                    document.body.removeChild(iframe);
                }, 100);
            }, 500);
        };
    }
    
    // 保存采购信息
    function savePurchaseInfo(layero, index) {
        var formData = new FormData(layero.find('form')[0]);
        var posting_number = formData.get('posting_number');
        
        // 收集所有物流单号
        var courierNumbers = [];
        layero.find('input[name="courierNumber[]"]').each(function() {
            var val = $(this).val().trim();
            if (val) courierNumbers.push(val);
        });
        
        // 收集所有采购单号
        var purchaseOrderSns = [];
        layero.find('input[name="purchase_orderSn[]"]').each(function() {
            var val = $(this).val().trim();
            if (val) purchaseOrderSns.push(val);
        });
        
        // 验证必填字段
        var cost = formData.get('cost');
        if (!cost || isNaN(cost)) {
            layer.msg('请输入有效的采购成本', { icon: 2 });
            return false;
        }
        
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=update_purchase_info',
                type: 'POST',
                data: {
                    posting_number: posting_number,
                    cost: cost,
                    courierNumber: courierNumbers.join(','),
                    purchase_orderSn: purchaseOrderSns.join(',')
                },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg('采购信息保存成功', { icon: 1 });
                        layer.close(index);
                        updateSingleOrder(posting_number);
                    } else {
                        layer.msg(res.msg || '保存失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 打印面单
    function printWaybill(posting_number) {
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=print_waybill',
                type: 'POST',
                data: { posting_number: posting_number },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        directPrint(res.url);
                    } else {
                        layer.msg(res.msg || '获取面单失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }
    
    // 批量打印面单
    function batchPrintWaybills(posting_numbers) {
        if (!posting_numbers || posting_numbers.length === 0) {
            layer.msg('请先选择要打印的订单', { icon: 2 });
            return;
        }
    
        requestQueue.add(function() {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=batch_print_waybills',
                type: 'POST',
                data: { posting_numbers: posting_numbers.join(',') },
                success: function(res) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        if (res.data && res.data.url) {
                            directPrint(res.data.url);
                        } else {
                            layer.msg('批量打印准备完成', { icon: 1 });
                        }
                    } else {
                        layer.msg(res.msg || '批量打印失败', { icon: 2 });
                    }
                },
                error: function(xhr) {
                    requestQueue.complete();
                    layer.closeAll('loading');
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });
    }


    // 其他事件处理函数...
    // 这里应该包含所有上面绑定的事件处理函数，为了简洁我先省略
    // 实际代码中需要完整实现每个处理函数

    // 初始化函数
    function init() {
        initUIComponents();
        initTabs(); // 初始化标签功能
        initEventDelegation();
        
        // 默认显示"待处理"标签下的"全部"子标签
        element.tabChange('orderTab', 'pending');
        element.tabChange('pendingSubTabs', 'all');
        
        // 初始渲染表格
        renderTable('all', getSearchParams());
    }

    // 启动应用
    init();
});

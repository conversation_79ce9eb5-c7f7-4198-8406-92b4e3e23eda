<?php
require_once __DIR__ . '/../includes/common.php';

// 检查并释放9501端口
exec('lsof -i :9501 -t', $output, $return);
if ($return === 0 && !empty($output)) {
    foreach ($output as $pid) {
        posix_kill($pid, 9); // 强制终止占用端口的进程
    }
    sleep(1); // 等待端口释放
}

// 创建 WebSocket 服务器
$server = new Swoole\WebSocket\Server("0.0.0.0", 9501);

// 监听连接打开事件
$server->on('open', function (Swoole\WebSocket\Server $server, $request) {
    echo json_encode($request);
    echo "客户端 {$request->fd} 已连接\n";
    file_put_contents('connections.log', print_r($request, true), FILE_APPEND);
    // 向客户端发送欢迎消息
    $server->push($request->fd, json_encode([
        'type' => 'welcome',
        'message' => '欢迎连接到 WebSocket 服务器',
        'time' => date('Y-m-d H:i:s')
    ]));
});

// 监听消息事件
$server->on('message', function (Swoole\WebSocket\Server $server, $frame) {
    echo "收到来自客户端 {$frame->fd} 的消息\n";
    if (empty($frame->data)) {
        echo '空数据 received';
        return;
    }

    $json = json_decode($frame->data, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'JSON 解析错误: ' . json_last_error_msg() . "\n";
        echo '原始数据: ' . $frame->data . "\n";
    }else {
        $type = $json['type'];
        if($type=='commissionData'){
            // 定义JSON文件路径
            $commissionData = $json['data'];
            $jsonFile = ROOT.'/assets/ozon/精准类目佣金8.5.json';
            // 读取现有数据
            $jsonContent = file_get_contents($jsonFile);
            $data = json_decode($jsonContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $data = [];
            }else{
                foreach ($commissionData as $item){
                    if($item['type_id']==0 or empty($item['type_id'])){
                        continue;
                    }
                    if(!isset($data[$item['type_id']])){
                        $data[$item['type_id']]['name'] = $item['type_name'];
                        $fx = getRedis('fx');
                        if($item['currency'] == 'CNY') {
                            $hl = $fx['ru']['num'];
                            $price = $item['price'] * $hl;
                        } else if($item['currency'] == 'RUB') {
                            $price = $item['price'];
                        }
                        if($price <= 1500) {
                            $data[$item['type_id']]['rFBS'] = $item['percent'];
                        } else if($price <= 5000) {
                            $data[$item['type_id']]['rFBS_1500'] = $item['percent'];
                        } else {
                            $data[$item['type_id']]['rFBS_5000'] = $item['percent'];
                        }
                    }else{
                        $data[$item['type_id']]['name'] = $item['type_name'];
                        $fx = getRedis('fx');
                        if($item['currency'] == 'CNY') {
                            $hl = $fx['ru']['num'];
                            $price = $item['price'] * $hl;
                        } else if($item['currency'] == 'RUB') {
                            $price = $item['price'];
                        }
                        if($price <= 1500) {
                            $data[$item['type_id']]['rFBS'] = $item['percent'];
                        } else if($price <= 5000) {
                            $data[$item['type_id']]['rFBS_1500'] = $item['percent'];
                        } else {
                            $data[$item['type_id']]['rFBS_5000'] = $item['percent'];
                        }
                    }
                }
            }
            // 4. 保存回文件
            file_put_contents($jsonFile, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }
    }
    // 广播消息给所有客户端
    foreach ($server->connections as $fd) {
        // 检查连接是否有效
        if ($server->isEstablished($fd)) {
            $server->push($fd, json_encode([
                'type' => 'message',
                'from' => $frame->fd,
                'content' => $frame->data,
                'time' => date('Y-m-d H:i:s')
            ]));
        }
    }
});

// 监听连接关闭事件
$server->on('close', function ($server, $fd) {
    echo "客户端 {$fd} 已断开连接\n";
    
    // 通知其他客户端有人离开
    foreach ($server->connections as $clientFd) {
        if ($server->isEstablished($clientFd) && $clientFd != $fd) {
            $server->push($clientFd, json_encode([
                'type' => 'leave',
                'fd' => $fd,
                'time' => date('Y-m-d H:i:s')
            ]));
        }
    }
});

// 启动服务器
echo "WebSocket 服务器已启动，监听 ws://0.0.0.0:9501\n";
$server->start();
-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-27 18:29:50
-- 服务器版本： 8.0.36
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `erp`
--

-- --------------------------------------------------------

--
-- 表的结构 `blacklist`
--

CREATE TABLE `blacklist` (
  `id` int NOT NULL,
  `uid` int NOT NULL COMMENT '用户ID',
  `posting_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `customer_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `reason_type` tinyint NOT NULL COMMENT '原因类型：1频繁退货退款 2恶意差评 3货到付款拒收 4其他',
  `reason_text` text COLLATE utf8mb4_unicode_ci COMMENT '原因描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户黑名单表';

-- --------------------------------------------------------

--
-- 表的结构 `order_times`
--

CREATE TABLE `order_times` (
  `id` int NOT NULL COMMENT '自增主键ID',
  `order_id` varchar(32) NOT NULL COMMENT '关联的订单ID',
  `posting_number` varchar(32) DEFAULT NULL COMMENT '订单posting编号',
  `time_type` varchar(32) NOT NULL COMMENT '时间类型: purchase=采购,print=打印,delivery=交运,inspection=验货,packing=打包等',
  `time_value` datetime NOT NULL COMMENT '具体时间',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单时间记录表(支持时间类型扩展)';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_activation_log`
--

CREATE TABLE `ozon_activation_log` (
  `id` int NOT NULL,
  `uid` int NOT NULL COMMENT '用户ID',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '使用的兑换码',
  `days_added` int NOT NULL COMMENT '增加的天数',
  `activated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '激活时间',
  `new_expiry_date` date NOT NULL COMMENT '激活后的新到期日期'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员激活日志表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_chats`
--

CREATE TABLE `ozon_chats` (
  `id` int NOT NULL,
  `uid` int NOT NULL COMMENT '用户ID',
  `store_id` int NOT NULL COMMENT '店铺ID',
  `chat_id` varchar(255) NOT NULL COMMENT 'Ozon聊天ID',
  `title` varchar(255) DEFAULT NULL COMMENT '聊天标题',
  `chat_type` varchar(50) DEFAULT NULL COMMENT '聊天类型',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态',
  `unread_count` int DEFAULT '0' COMMENT '未读消息数',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `last_message_text` text COMMENT '最后消息内容',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `order_number` varchar(100) DEFAULT '' COMMENT '订单号',
  `sku` varchar(100) DEFAULT '' COMMENT 'SKU',
  `product_name` text COMMENT '商品名称',
  `order_info_json` text COMMENT '完整订单信息JSON',
  `extraction_source` varchar(50) DEFAULT '' COMMENT '提取来源：api,chatlist,text'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Ozon聊天会话表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_client`
--

CREATE TABLE `ozon_client` (
  `id` int NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `ClientId` int DEFAULT NULL,
  `key` varchar(64) DEFAULT NULL,
  `daily_create` int NOT NULL DEFAULT '0',
  `daily_update` int NOT NULL DEFAULT '0',
  `limit` int NOT NULL DEFAULT '0',
  `time` int NOT NULL DEFAULT '0',
  `deltime` int NOT NULL DEFAULT '0',
  `nums` int NOT NULL DEFAULT '0',
  `status` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_cnproducts`
--

CREATE TABLE `ozon_cnproducts` (
  `id` int NOT NULL,
  `variant_id` varchar(20) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `brand` varchar(100) DEFAULT NULL,
  `category1` varchar(100) DEFAULT NULL,
  `category3` varchar(100) DEFAULT NULL,
  `volume` decimal(10,3) DEFAULT NULL,
  `sessionCount` int DEFAULT NULL,
  `convToCart` decimal(5,2) DEFAULT NULL,
  `avgPrice` decimal(10,2) DEFAULT NULL,
  `sku` varchar(50) DEFAULT NULL,
  `convToCartPdp` decimal(5,2) DEFAULT NULL,
  `sources` json DEFAULT NULL,
  `blockedBySeller` varchar(10) DEFAULT 'true',
  `blockedAsYourGood` tinyint(1) DEFAULT NULL,
  `soldSum` decimal(12,2) DEFAULT NULL,
  `soldCount` int DEFAULT NULL,
  `minSellerPrice` decimal(10,2) DEFAULT NULL,
  `salesDynamics` decimal(10,2) DEFAULT NULL,
  `stock` varchar(10) DEFAULT NULL,
  `fbsStock` int DEFAULT NULL,
  `fboStock` int DEFAULT NULL,
  `cbStock` int DEFAULT NULL,
  `retailStock` int DEFAULT NULL,
  `gmvSum` decimal(12,2) DEFAULT NULL,
  `sumItemsInStock` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `daysInStock` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `avgGmv` decimal(10,3) DEFAULT NULL,
  `accessibility` int DEFAULT NULL,
  `avgGmvOnAccDays` decimal(10,2) DEFAULT NULL,
  `avgOrdersOnAccDays` decimal(10,2) DEFAULT NULL,
  `sumMissedGmv` decimal(10,2) DEFAULT NULL,
  `avgDeliveryDays` decimal(5,2) DEFAULT NULL,
  `qtyViewPdp` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `pdpToCartConversion` decimal(5,2) DEFAULT NULL,
  `skuName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `isSuper` tinyint(1) DEFAULT NULL,
  `sellerId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `countryCode` varchar(32) DEFAULT NULL,
  `salesSchema` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `sessionCountSearch` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `convToCartSearch` decimal(5,2) DEFAULT NULL,
  `drr` decimal(5,2) DEFAULT NULL,
  `nullableRedemptionRate` int DEFAULT NULL,
  `nullableCreateDate` datetime DEFAULT NULL,
  `localIndex` int DEFAULT NULL,
  `daysInPromo` int DEFAULT NULL,
  `promoRevenueShare` int DEFAULT NULL,
  `daysWithTrafarets` int DEFAULT NULL,
  `discount` decimal(5,3) DEFAULT NULL,
  `views` varchar(20) DEFAULT NULL,
  `convViewToOrder` decimal(5,2) DEFAULT NULL,
  `sellerName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `article` varchar(50) DEFAULT NULL,
  `bin` varchar(20) DEFAULT NULL,
  `avgDeliveryTime` int DEFAULT NULL,
  `accessibilityByDays` int DEFAULT NULL,
  `categories1` varchar(20) DEFAULT NULL,
  `categories2` varchar(20) DEFAULT NULL,
  `categories3` varchar(20) DEFAULT NULL,
  `sellerlength` int NOT NULL DEFAULT '0',
  `update_date` date DEFAULT NULL,
  `time` varchar(13) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_cron`
--

CREATE TABLE `ozon_cron` (
  `id` int NOT NULL,
  `uid` int DEFAULT NULL,
  `sku` varchar(20) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `old_price` decimal(10,2) DEFAULT NULL,
  `storeid` int DEFAULT NULL,
  `clientids` int NOT NULL DEFAULT '0',
  `offer_id` varchar(64) DEFAULT NULL,
  `type_id` varchar(32) DEFAULT NULL,
  `status` varchar(64) DEFAULT NULL,
  `title` varchar(200) DEFAULT NULL,
  `height` int DEFAULT NULL,
  `depth` int DEFAULT NULL,
  `width` int DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `description_category_id` varchar(32) DEFAULT NULL,
  `variantId` int DEFAULT NULL,
  `primary_image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `barcode` varchar(32) DEFAULT NULL,
  `warehouse_id` varchar(64) DEFAULT NULL,
  `product_id` int DEFAULT NULL,
  `data` text,
  `task_id` varchar(20) DEFAULT NULL,
  `task_id2` varchar(20) DEFAULT NULL,
  `offer_id2` varchar(32) DEFAULT NULL,
  `stock` int DEFAULT NULL,
  `newsku` int DEFAULT NULL COMMENT '新创建的sku\r\n',
  `time` int DEFAULT '0',
  `type` varchar(64) DEFAULT NULL,
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `watermark` int NOT NULL DEFAULT '0' COMMENT '0不使用1使用水印',
  `addtime` datetime DEFAULT NULL,
  `date` date DEFAULT NULL,
  `log` text,
  `cron` int NOT NULL DEFAULT '0',
  `nums` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_dailiip`
--

CREATE TABLE `ozon_dailiip` (
  `id` int NOT NULL,
  `type` int DEFAULT '0' COMMENT '0=通用,1=Cookie专用',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `proxy_server` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `proxy_port` int DEFAULT NULL,
  `proxy_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `proxy_pwd` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `proxy_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int NOT NULL DEFAULT '0',
  `nums` int NOT NULL DEFAULT '0',
  `time` int NOT NULL DEFAULT '0',
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `cookie` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `uptime` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_management`
--

CREATE TABLE `ozon_management` (
  `id` int NOT NULL,
  `uid` int DEFAULT NULL,
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `purchase_url` json DEFAULT NULL COMMENT '采购链接',
  `platform` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '采购平台，如淘宝、京东、1688等',
  `is_primary_link` tinyint(1) DEFAULT '0' COMMENT '是否为主要采购链接',
  `unit_price` decimal(10,2) NOT NULL COMMENT '采购单价',
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'CNY',
  `shipping_cost` decimal(10,2) DEFAULT '0.00' COMMENT '运费',
  `tax` decimal(10,2) DEFAULT '0.00' COMMENT '税费',
  `other_cost` decimal(10,2) DEFAULT '0.00' COMMENT '其他费用',
  `total_cost` decimal(10,2) GENERATED ALWAYS AS ((((`unit_price` + `shipping_cost`) + `tax`) + `other_cost`)) STORED,
  `promotion_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `promotion_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `promotion_start` datetime DEFAULT NULL,
  `promotion_end` datetime DEFAULT NULL,
  `discount_value` decimal(10,2) DEFAULT NULL COMMENT '折扣值',
  `discount_type` enum('percentage','fixed_amount','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '折扣类型',
  `sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '绑定的SKU编码',
  `sku_quantity` int DEFAULT '1' COMMENT '一个采购链接对应的SKU数量',
  `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `status` enum('active','inactive','discontinued') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_messages`
--

CREATE TABLE `ozon_messages` (
  `id` int NOT NULL,
  `chat_id` int NOT NULL COMMENT '聊天ID（关联ozon_chats.id）',
  `message_id` varchar(255) NOT NULL COMMENT 'Ozon消息ID',
  `sender_type` enum('seller','customer','system') NOT NULL COMMENT '发送者类型',
  `content` text NOT NULL COMMENT '消息内容',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `sent_at` datetime DEFAULT NULL COMMENT '发送时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Ozon聊天消息表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_order`
--

CREATE TABLE `ozon_order` (
  `posting_number` varchar(32) NOT NULL,
  `order_id` varchar(32) NOT NULL,
  `uid` int DEFAULT NULL,
  `storeid` int DEFAULT NULL,
  `order_name` varchar(500) DEFAULT NULL,
  `name2` varchar(200) DEFAULT NULL,
  `sku` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `products` json DEFAULT NULL,
  `products_num` int DEFAULT NULL,
  `offer_id` varchar(32) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `zmoney` decimal(10,2) NOT NULL,
  `quantity` int DEFAULT '1',
  `cost` decimal(10,2) DEFAULT NULL COMMENT '采购金额',
  `delivery` decimal(10,2) DEFAULT NULL,
  `profit` decimal(10,2) DEFAULT NULL,
  `weight` int NOT NULL DEFAULT '0',
  `out_weight` varchar(11) DEFAULT NULL,
  `currency_code` varchar(32) DEFAULT NULL,
  `warehouse` varchar(64) DEFAULT NULL,
  `kd_name` varchar(64) DEFAULT NULL,
  `tpl_provider` varchar(64) DEFAULT NULL,
  `tracking_number` varchar(64) NOT NULL,
  `status` varchar(64) DEFAULT NULL,
  `substatus` varchar(64) DEFAULT NULL,
  `previous_substatus` varchar(64) DEFAULT NULL,
  `delivering_date` datetime DEFAULT NULL,
  `in_process_at` datetime DEFAULT NULL COMMENT '开始时间\r\n',
  `shipment_date` datetime DEFAULT NULL COMMENT '截至时间\r\n',
  `Purchase_date` datetime DEFAULT NULL COMMENT '采购日期',
  `Outbound_data` datetime DEFAULT NULL COMMENT '调用库存时间',
  `date` date DEFAULT NULL COMMENT '添加日期',
  `primary_image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `dimensions` varchar(255) DEFAULT NULL COMMENT '尺寸',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `material` varchar(100) DEFAULT NULL COMMENT '材质',
  `size` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '尺码',
  `num` int DEFAULT NULL COMMENT '配套数量',
  `customer` json DEFAULT NULL,
  `time` int NOT NULL DEFAULT '0',
  `courierNumber` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '国内快递单号',
  `packagelabel` int NOT NULL DEFAULT '0' COMMENT '面单是否下载？',
  `packing_status` int NOT NULL COMMENT '打包状态',
  `OrderNotes` text NOT NULL COMMENT '订单备注',
  `purchase_id` varchar(32) DEFAULT NULL COMMENT '采购ID',
  `purchase_ok` int NOT NULL DEFAULT '0',
  `purchase_type` varchar(32) NOT NULL DEFAULT 'pdd',
  `purchase_orderSn` varchar(64) DEFAULT NULL,
  `purchase_kdname` varchar(64) DEFAULT NULL,
  `purchase_lus` varchar(200) DEFAULT NULL,
  `wl` varchar(64) DEFAULT NULL,
  `speed` varchar(64) DEFAULT NULL,
  `speedtx` varchar(64) DEFAULT NULL,
  `provider` varchar(64) DEFAULT NULL,
  `commission_percent` int DEFAULT NULL,
  `bubble` char(10) DEFAULT NULL,
  `specification` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_production`
--

CREATE TABLE `ozon_production` (
  `id` int NOT NULL,
  `uid` int DEFAULT NULL,
  `type` varchar(64) DEFAULT NULL,
  `title` text,
  `price` decimal(10,3) DEFAULT NULL,
  `old_price` decimal(10,3) DEFAULT NULL,
  `offer_id` varchar(32) DEFAULT NULL,
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '主图\r\n',
  `mainImage` varchar(200) DEFAULT NULL,
  `platformType` int DEFAULT NULL,
  `videos` varchar(200) DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `orderUrl` varchar(200) DEFAULT NULL,
  `detail_url` varchar(200) DEFAULT NULL,
  `json` json DEFAULT NULL,
  `attributes` json DEFAULT NULL,
  `attributesdata` json DEFAULT NULL,
  `category_chain` json DEFAULT NULL,
  `productAttributes` json DEFAULT NULL,
  `addtime` datetime DEFAULT NULL,
  `height` int DEFAULT NULL,
  `depth` int DEFAULT NULL,
  `width` int DEFAULT NULL,
  `task_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_products`
--

CREATE TABLE `ozon_products` (
  `uid` int NOT NULL DEFAULT '10000',
  `storeid` int DEFAULT NULL,
  `name` varchar(500) DEFAULT NULL,
  `offer_id` varchar(50) NOT NULL,
  `sku` varchar(20) DEFAULT NULL,
  `product_id` varchar(32) DEFAULT NULL,
  `description` text,
  `description_category_id` int DEFAULT NULL,
  `type_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `currency_code` varchar(3) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `old_price` decimal(10,2) DEFAULT NULL,
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价',
  `marketing_price` decimal(10,2) DEFAULT NULL,
  `volume_weight` decimal(10,2) DEFAULT NULL,
  `vat` decimal(5,2) DEFAULT NULL,
  `primary_image` varchar(255) DEFAULT NULL,
  `is_archived` tinyint(1) DEFAULT '0' COMMENT '手动归档',
  `is_autoarchived` tinyint(1) DEFAULT '0' COMMENT '自动归档',
  `is_prepayment_allowed` tinyint(1) DEFAULT '0',
  `is_discounted` tinyint(1) DEFAULT '0',
  `is_kgt` tinyint(1) DEFAULT '0',
  `is_super` tinyint(1) DEFAULT '0',
  `is_seasonal` tinyint(1) DEFAULT '0',
  `images` json DEFAULT NULL COMMENT '产品图片数组',
  `stocks` json DEFAULT NULL COMMENT '库存信息',
  `salesvolume` int DEFAULT NULL COMMENT '销量',
  `commissions` json DEFAULT NULL COMMENT '佣金信息',
  `attributes` json DEFAULT NULL COMMENT '产品属性',
  `price_indexes` json DEFAULT NULL COMMENT '价格指数',
  `status_info` json DEFAULT NULL COMMENT '状态信息',
  `status_name` varchar(64) DEFAULT NULL,
  `status_description` varchar(64) DEFAULT NULL,
  `errors` json DEFAULT NULL,
  `purchasingcost` decimal(10,2) DEFAULT NULL COMMENT '采购成本',
  `depth` int DEFAULT NULL COMMENT '包装深度(mm)',
  `width` int DEFAULT NULL COMMENT '包装宽度(mm)',
  `height` int DEFAULT NULL COMMENT '包装高度(mm)',
  `weight` int DEFAULT NULL COMMENT '含包装重量(g)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_redemption_codes`
--

CREATE TABLE `ozon_redemption_codes` (
  `id` int NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '兑换码',
  `user_level` int NOT NULL COMMENT '对应的会员等级',
  `days` int NOT NULL COMMENT '有效天数',
  `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否体验卡，用户仅可激活一次',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: 未使用, 1: 已使用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  `used_by_uid` int DEFAULT NULL COMMENT '使用者用户ID',
  `agent_uid` int DEFAULT NULL COMMENT '生成兑换码的代理用户ID',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员兑换码表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_regcode`
--

CREATE TABLE `ozon_regcode` (
  `id` int NOT NULL,
  `uid` int NOT NULL DEFAULT '0',
  `scene` varchar(20) NOT NULL DEFAULT '',
  `type` tinyint(1) NOT NULL DEFAULT '0',
  `code` varchar(32) NOT NULL,
  `to` varchar(32) DEFAULT NULL,
  `time` int NOT NULL,
  `ip` varchar(50) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `errcount` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_scheduled_products`
--

CREATE TABLE `ozon_scheduled_products` (
  `id` int NOT NULL COMMENT '主键ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `sku` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品SKU',
  `title` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品标题',
  `primary_image` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品图片URL',
  `price` decimal(10,2) DEFAULT NULL COMMENT '导入价格',
  `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '采购价格',
  `purchase_link` text COLLATE utf8mb4_unicode_ci COMMENT '采购链接',
  `average_price` decimal(10,2) DEFAULT NULL COMMENT '平均价格',
  `predicted_price` decimal(10,2) DEFAULT NULL COMMENT '预测价格',
  `task_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `storeid` int DEFAULT NULL COMMENT '分配的店铺ID',
  `warehouse_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分配的仓库ID',
  `price_preset` tinyint DEFAULT '0' COMMENT '价格预设 0:售价 1:最高价 2:预测价 3:平均价',
  `stock` int DEFAULT '0' COMMENT '库存数量',
  `scheduled_at` datetime DEFAULT NULL COMMENT '预定上架时间',
  `status` enum('pending','processing','success','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '任务状态',
  `msg` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `date` date DEFAULT NULL COMMENT '创建日期'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时上架商品表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_seller`
--

CREATE TABLE `ozon_seller` (
  `id` int UNSIGNED NOT NULL,
  `uid` int DEFAULT NULL,
  `variantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一变体ID',
  `sku` varchar(50) NOT NULL COMMENT '平台SKU',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `category1` varchar(50) DEFAULT NULL COMMENT '一级分类',
  `category3` varchar(50) DEFAULT NULL COMMENT '三级分类',
  `price` decimal(12,2) DEFAULT NULL COMMENT '平均价格',
  `cardPrice` decimal(10,2) DEFAULT NULL,
  `zprice` decimal(10,2) DEFAULT NULL COMMENT '前端价格\r\n',
  `originalPrice` decimal(10,2) DEFAULT NULL,
  `min_price` decimal(12,2) DEFAULT NULL COMMENT '最低售价',
  `stock` int DEFAULT '0' COMMENT '总库存',
  `fbs_stock` int DEFAULT '0' COMMENT 'FBS库存',
  `fbo_stock` int DEFAULT '0' COMMENT 'FBO库存',
  `volume` float DEFAULT NULL COMMENT '体积重量',
  `session_count` int DEFAULT '0' COMMENT '会话量',
  `conv_to_cart` float DEFAULT '0' COMMENT '加购转化率',
  `pdp_conv_rate` float DEFAULT '0' COMMENT 'PDP转化率',
  `sales_dynamics` float DEFAULT '0' COMMENT '销售动态',
  `drr` float DEFAULT '0' COMMENT '退货率',
  `gmv_sum` decimal(12,2) DEFAULT '0.00' COMMENT 'GMV总额',
  `sold_count` int DEFAULT '0' COMMENT '销量',
  `seller_id` varchar(20) DEFAULT NULL COMMENT '卖家ID',
  `sellerName` varchar(64) DEFAULT NULL COMMENT '卖家名称',
  `sales_schema` varchar(20) DEFAULT NULL COMMENT '销售模式',
  `promo_days` int DEFAULT '0' COMMENT '促销天数',
  `trafaret_days` int DEFAULT '0' COMMENT '模板使用天数',
  `discount` float DEFAULT '0' COMMENT '折扣率',
  `views` int DEFAULT '0' COMMENT '浏览数',
  `local_index` int DEFAULT '0' COMMENT '本地指数',
  `avg_delivery_days` float DEFAULT NULL COMMENT '平均配送天数',
  `photo_url` text COMMENT '主图URL',
  `product_url` text COMMENT '商品链接',
  `sources` json DEFAULT NULL COMMENT '来源渠道',
  `stock_info` json DEFAULT NULL COMMENT '库存详情',
  `status_info` json DEFAULT NULL COMMENT '状态信息',
  `analytics` json DEFAULT NULL COMMENT '分析数据',
  `platform` varchar(20) DEFAULT 'ozon' COMMENT '平台标识',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `gm` tinyint NOT NULL DEFAULT '0' COMMENT '是否上架',
  `gj_price` decimal(12,2) NOT NULL COMMENT '估算价格',
  `time` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Ozon商品数据表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_sellerjk`
--

CREATE TABLE `ozon_sellerjk` (
  `id` int NOT NULL,
  `uid` int NOT NULL,
  `seller_id` int NOT NULL,
  `sellerName` varchar(64) DEFAULT NULL,
  `addtime` datetime NOT NULL,
  `date` date DEFAULT '1999-01-01'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_store`
--

CREATE TABLE `ozon_store` (
  `id` int NOT NULL,
  `ClientId` int UNSIGNED NOT NULL,
  `uid` int DEFAULT NULL,
  `storename` varchar(64) DEFAULT NULL,
  `key` varchar(200) DEFAULT NULL,
  `cookie` text,
  `currency_code` varchar(8) DEFAULT NULL,
  `warehouses` text,
  `addtime` datetime DEFAULT NULL,
  `apistatus` int NOT NULL DEFAULT '1',
  `group_id` int NOT NULL DEFAULT '0' COMMENT '分组ID',
  `daily_create` int DEFAULT '0',
  `daily_update` int DEFAULT '0',
  `limit` int NOT NULL DEFAULT '0',
  `actions` int NOT NULL DEFAULT '0',
  `time` int NOT NULL DEFAULT '0',
  `updata_products` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_urlcron`
--

CREATE TABLE `ozon_urlcron` (
  `id` int NOT NULL,
  `sku` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int NOT NULL DEFAULT '0',
  `time` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_user`
--

CREATE TABLE `ozon_user` (
  `uid` int NOT NULL,
  `parent_uid` int DEFAULT NULL COMMENT '主账号UID，NULL表示主账号，有值表示子账号',
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL COMMENT '用户邮箱',
  `phone` varchar(50) DEFAULT NULL COMMENT '用户手机号',
  `addtime` datetime DEFAULT NULL,
  `user_level` int DEFAULT '0' COMMENT '0普通1-个人 2-团队 3-企业 4-定制',
  `agent_level` int DEFAULT '0' COMMENT '代理等级: 0-普通用户 1-一级代理 2-二级代理 3-总代理',
  `max_shops` int DEFAULT '0' COMMENT '最大店铺数量（新增店铺收费）',
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `expiry_date` datetime DEFAULT NULL COMMENT '会员到期时间',
  `permissions` text COMMENT '子账号权限设置（JSON格式，包含模块权限和店铺访问权限）',
  `package` int DEFAULT NULL,
  `1688auth` json DEFAULT NULL,
  `pddcookie` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `shop_groups` json DEFAULT NULL,
  `scheduled_upload_time` datetime DEFAULT NULL COMMENT '定时上架时间点',
  `translation_keys` text COMMENT '翻译服务密钥配置(JSON格式)',
  `logistics_settings` text COMMENT '物流计算设置(JSON格式)',
  `warehouse_settings` text COMMENT '仓库设置(JSON格式)',
  `watermark_settings` text COMMENT '水印设置(JSON格式)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `ozon_user_permissions`
--

CREATE TABLE `ozon_user_permissions` (
  `id` int NOT NULL COMMENT '主键，自增ID',
  `sub_uid` int NOT NULL COMMENT '子账号用户ID',
  `permission_key` varchar(255) NOT NULL COMMENT '权限标识键，如 can_view_orders',
  `permission_value` tinyint NOT NULL DEFAULT '0' COMMENT '权限值，0表示无权限，1表示有权限'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='子账号功能权限表';

-- --------------------------------------------------------

--
-- 表的结构 `ozon_weight`
--

CREATE TABLE `ozon_weight` (
  `sku` varchar(50) NOT NULL,
  `variant_id` int DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `depth` int DEFAULT NULL,
  `height` int DEFAULT NULL,
  `width` int DEFAULT NULL,
  `date` date DEFAULT NULL,
  `brokerage` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `packers`
--

CREATE TABLE `packers` (
  `packerId` int NOT NULL COMMENT '主键，自增ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '打包员账号，唯一且不能为空',
  `password` varchar(255) NOT NULL COMMENT '打包员密码，建议使用加密存储',
  `user_uid` text NOT NULL COMMENT '关联的用户UID，用于标识对应用户',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '打包员余额，默认为0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，默认当前时间',
  `printer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '打印机信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='打包员账号信息表';

-- --------------------------------------------------------

--
-- 表的结构 `packing_records`
--

CREATE TABLE `packing_records` (
  `id` int NOT NULL COMMENT '记录ID',
  `uid` int NOT NULL COMMENT '订单归属uid',
  `posting_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单posting编号',
  `tracking_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '国际',
  `courierNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '国内单号',
  `packerId` int NOT NULL COMMENT '打包员ID',
  `packing_quantity` int DEFAULT '1' COMMENT '打包数量',
  `material_cost` decimal(10,2) DEFAULT NULL COMMENT '耗材费用(元)',
  `packing_status` tinyint NOT NULL DEFAULT '0' COMMENT ' 0=异常 1=打包完成  2=退回问题件 ',
  `completion_time` datetime DEFAULT NULL COMMENT '打包完成时间',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='打包记录表';

-- --------------------------------------------------------

--
-- 表的结构 `product_inventory`
--

CREATE TABLE `product_inventory` (
  `id` int NOT NULL COMMENT '产品ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `product_type` varchar(20) NOT NULL DEFAULT 'single' COMMENT '产品类型',
  `title` varchar(200) NOT NULL COMMENT '产品标题',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品图片',
  `size_length` decimal(10,2) DEFAULT '0.00' COMMENT '尺寸-长(cm)',
  `size_width` decimal(10,2) DEFAULT '0.00' COMMENT '尺寸-宽(cm)',
  `size_height` decimal(10,2) DEFAULT '0.00' COMMENT '尺寸-高(cm)',
  `weight` decimal(10,3) DEFAULT '0.000' COMMENT '重量(kg)',
  `status` varchar(20) NOT NULL COMMENT '产品状态',
  `sku` varchar(150) NOT NULL COMMENT '产品SKU号',
  `platform_sku` varchar(150) DEFAULT NULL COMMENT '平台产品匹配SKU',
  `price` decimal(10,4) DEFAULT '0.0000' COMMENT '产品单价(元)',
  `remark` varchar(500) DEFAULT NULL COMMENT '产品备注',
  `warehouse` varchar(150) DEFAULT NULL COMMENT '仓库位置',
  `warehouse_stock` int DEFAULT '0' COMMENT '仓库库存数量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品库存表';

-- --------------------------------------------------------

--
-- 表的结构 `t_order_profit`
--

CREATE TABLE `t_order_profit` (
  `id` bigint NOT NULL COMMENT '主键ID，自增长',
  `posting_number` varchar(64) NOT NULL COMMENT '订单号，来自Ozon API的posting_number',
  `shop_id` int DEFAULT NULL COMMENT '店铺ID，关联ozon_store表的id字段',
  `uid` int DEFAULT NULL COMMENT '用户ID，关联ozon_user表的uid字段',
  `order_date` datetime NOT NULL COMMENT '下单日期，订单创建时间',
  `item_name` varchar(255) DEFAULT NULL COMMENT '商品中文名称，多个商品用逗号分隔',
  `sku` bigint DEFAULT NULL COMMENT 'SKU编码，商品唯一标识符',
  `income` decimal(12,2) DEFAULT '0.00' COMMENT '客户签收金额，正数表示收入',
  `commission` decimal(12,2) DEFAULT '0.00' COMMENT '销售佣金，负数表示平台扣费',
  `agency_fee` decimal(12,2) DEFAULT '0.00' COMMENT '国际物流代理佣金，负数表示费用',
  `delivery_fee` decimal(12,2) DEFAULT '0.00' COMMENT '物流分摊费用，负数表示费用',
  `acquiring_fee` decimal(12,2) DEFAULT '0.00' COMMENT '支付手续费，负数表示费用',
  `return_amount` decimal(12,2) DEFAULT '0.00' COMMENT '退货退款金额，负数表示退款',
  `operation_ids` text COMMENT '已处理的操作ID列表，逗号分隔',
  `net_profit` decimal(12,2) GENERATED ALWAYS AS ((((((`income` + `commission`) + `agency_fee`) + `delivery_fee`) + `acquiring_fee`) + `return_amount`)) STORED COMMENT '净利润，自动计算所有金额的总和',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单利润表 - 存储Ozon订单的详细财务数据和利润计算';

-- --------------------------------------------------------

--
-- 表的结构 `warehouse_stock_log`
--

CREATE TABLE `warehouse_stock_log` (
  `id` int NOT NULL,
  `product_id` int NOT NULL,
  `change_quantity` int NOT NULL,
  `change_type` enum('in','out') NOT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `uid` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- 转储表的索引
--

--
-- 表的索引 `blacklist`
--
ALTER TABLE `blacklist`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_uid` (`uid`),
  ADD KEY `idx_customer_name` (`customer_name`);

--
-- 表的索引 `order_times`
--
ALTER TABLE `order_times`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order` (`order_id`),
  ADD KEY `idx_posting` (`posting_number`);

--
-- 表的索引 `ozon_activation_log`
--
ALTER TABLE `ozon_activation_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_uid` (`uid`);

--
-- 表的索引 `ozon_chats`
--
ALTER TABLE `ozon_chats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_chat` (`uid`,`store_id`,`chat_id`),
  ADD KEY `idx_uid` (`uid`),
  ADD KEY `idx_store_id` (`store_id`),
  ADD KEY `idx_order_number` (`order_number`),
  ADD KEY `idx_sku` (`sku`);

--
-- 表的索引 `ozon_client`
--
ALTER TABLE `ozon_client`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`),
  ADD KEY `ClientId` (`ClientId`);

--
-- 表的索引 `ozon_cnproducts`
--
ALTER TABLE `ozon_cnproducts`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `ozon_cron`
--
ALTER TABLE `ozon_cron`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`,`sku`,`offer_id`,`product_id`),
  ADD KEY `storeid` (`storeid`),
  ADD KEY `variantId` (`variantId`);

--
-- 表的索引 `ozon_dailiip`
--
ALTER TABLE `ozon_dailiip`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `ozon_management`
--
ALTER TABLE `ozon_management`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sku` (`sku`),
  ADD KEY `idx_product_name` (`product_name`),
  ADD KEY `idx_supplier` (`supplier_name`),
  ADD KEY `uid` (`uid`);

--
-- 表的索引 `ozon_messages`
--
ALTER TABLE `ozon_messages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_message` (`message_id`),
  ADD KEY `idx_chat_id` (`chat_id`),
  ADD KEY `idx_sent_at` (`sent_at`),
  ADD KEY `idx_sender_type` (`sender_type`);

--
-- 表的索引 `ozon_order`
--
ALTER TABLE `ozon_order`
  ADD PRIMARY KEY (`posting_number`),
  ADD KEY `uid` (`uid`),
  ADD KEY `storeid` (`storeid`),
  ADD KEY `status` (`status`),
  ADD KEY `in_process_at` (`in_process_at`,`date`,`purchase_ok`),
  ADD KEY `purchase_type` (`purchase_type`);

--
-- 表的索引 `ozon_production`
--
ALTER TABLE `ozon_production`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`,`uid`,`offer_id`,`task_id`);

--
-- 表的索引 `ozon_products`
--
ALTER TABLE `ozon_products`
  ADD PRIMARY KEY (`offer_id`),
  ADD KEY `idx_created` (`created_at`);

--
-- 表的索引 `ozon_redemption_codes`
--
ALTER TABLE `ozon_redemption_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `idx_code` (`code`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_agent_uid` (`agent_uid`);

--
-- 表的索引 `ozon_regcode`
--
ALTER TABLE `ozon_regcode`
  ADD PRIMARY KEY (`id`),
  ADD KEY `code` (`to`,`type`);

--
-- 表的索引 `ozon_scheduled_products`
--
ALTER TABLE `ozon_scheduled_products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_uid` (`uid`),
  ADD KEY `idx_sku` (`sku`),
  ADD KEY `idx_storeid` (`storeid`),
  ADD KEY `idx_task_id` (`task_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_addtime` (`addtime`),
  ADD KEY `idx_scheduled_at` (`scheduled_at`);

--
-- 表的索引 `ozon_seller`
--
ALTER TABLE `ozon_seller`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_variant` (`variantId`),
  ADD KEY `idx_sku` (`sku`),
  ADD KEY `idx_category` (`category1`,`category3`),
  ADD KEY `idx_sales` (`sales_dynamics`),
  ADD KEY `idx_stock` (`stock`);

--
-- 表的索引 `ozon_sellerjk`
--
ALTER TABLE `ozon_sellerjk`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `ozon_store`
--
ALTER TABLE `ozon_store`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ClientId` (`ClientId`),
  ADD KEY `uid` (`uid`),
  ADD KEY `updata_products` (`updata_products`),
  ADD KEY `time` (`time`),
  ADD KEY `apistatus` (`apistatus`);

--
-- 表的索引 `ozon_urlcron`
--
ALTER TABLE `ozon_urlcron`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `ozon_user`
--
ALTER TABLE `ozon_user`
  ADD PRIMARY KEY (`uid`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `idx_parent_uid` (`parent_uid`);

--
-- 表的索引 `ozon_user_permissions`
--
ALTER TABLE `ozon_user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `sub_uid_permission_key` (`sub_uid`,`permission_key`);

--
-- 表的索引 `ozon_weight`
--
ALTER TABLE `ozon_weight`
  ADD PRIMARY KEY (`sku`);

--
-- 表的索引 `packers`
--
ALTER TABLE `packers`
  ADD PRIMARY KEY (`packerId`),
  ADD UNIQUE KEY `account` (`username`);

--
-- 表的索引 `packing_records`
--
ALTER TABLE `packing_records`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_posting` (`posting_number`) COMMENT '订单唯一索引',
  ADD KEY `idx_tracking` (`tracking_number`) COMMENT '快递单号索引',
  ADD KEY `idx_packer` (`packerId`) COMMENT '打包员索引',
  ADD KEY `idx_status` (`packing_status`) COMMENT '状态索引',
  ADD KEY `idx_completion` (`completion_time`) COMMENT '完成时间索引';

--
-- 表的索引 `product_inventory`
--
ALTER TABLE `product_inventory`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `t_order_profit`
--
ALTER TABLE `t_order_profit`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_posting_number_unique` (`posting_number`),
  ADD KEY `idx_posting` (`posting_number`) COMMENT '订单号索引，用于快速查询',
  ADD KEY `idx_date` (`order_date`) COMMENT '订单日期索引，用于按时间查询',
  ADD KEY `idx_sku` (`sku`) COMMENT 'SKU索引，用于按商品查询',
  ADD KEY `idx_net_profit` (`net_profit`) COMMENT '净利润索引，用于利润分析',
  ADD KEY `idx_shop_id` (`shop_id`) COMMENT '店铺ID索引，用于按店铺查询',
  ADD KEY `idx_uid` (`uid`) COMMENT '用户ID索引，用于按用户查询',
  ADD KEY `idx_uid_shop` (`uid`,`shop_id`) COMMENT '用户和店铺组合索引，用于多维度查询';

--
-- 表的索引 `warehouse_stock_log`
--
ALTER TABLE `warehouse_stock_log`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `blacklist`
--
ALTER TABLE `blacklist`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `order_times`
--
ALTER TABLE `order_times`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键ID';

--
-- 使用表AUTO_INCREMENT `ozon_activation_log`
--
ALTER TABLE `ozon_activation_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_chats`
--
ALTER TABLE `ozon_chats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_client`
--
ALTER TABLE `ozon_client`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_cnproducts`
--
ALTER TABLE `ozon_cnproducts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_cron`
--
ALTER TABLE `ozon_cron`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_dailiip`
--
ALTER TABLE `ozon_dailiip`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_management`
--
ALTER TABLE `ozon_management`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_messages`
--
ALTER TABLE `ozon_messages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_production`
--
ALTER TABLE `ozon_production`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_redemption_codes`
--
ALTER TABLE `ozon_redemption_codes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_regcode`
--
ALTER TABLE `ozon_regcode`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_scheduled_products`
--
ALTER TABLE `ozon_scheduled_products`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID';

--
-- 使用表AUTO_INCREMENT `ozon_seller`
--
ALTER TABLE `ozon_seller`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_sellerjk`
--
ALTER TABLE `ozon_sellerjk`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_store`
--
ALTER TABLE `ozon_store`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_urlcron`
--
ALTER TABLE `ozon_urlcron`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_user`
--
ALTER TABLE `ozon_user`
  MODIFY `uid` int NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `ozon_user_permissions`
--
ALTER TABLE `ozon_user_permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '主键，自增ID';

--
-- 使用表AUTO_INCREMENT `packers`
--
ALTER TABLE `packers`
  MODIFY `packerId` int NOT NULL AUTO_INCREMENT COMMENT '主键，自增ID';

--
-- 使用表AUTO_INCREMENT `packing_records`
--
ALTER TABLE `packing_records`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID';

--
-- 使用表AUTO_INCREMENT `product_inventory`
--
ALTER TABLE `product_inventory`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '产品ID';

--
-- 使用表AUTO_INCREMENT `t_order_profit`
--
ALTER TABLE `t_order_profit`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长';

--
-- 使用表AUTO_INCREMENT `warehouse_stock_log`
--
ALTER TABLE `warehouse_stock_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- 限制导出的表
--

--
-- 限制表 `ozon_messages`
--
ALTER TABLE `ozon_messages`
  ADD CONSTRAINT `fk_ozon_messages_chat` FOREIGN KEY (`chat_id`) REFERENCES `ozon_chats` (`id`) ON DELETE CASCADE;

--
-- 限制表 `ozon_user`
--
ALTER TABLE `ozon_user`
  ADD CONSTRAINT `fk_parent_uid` FOREIGN KEY (`parent_uid`) REFERENCES `ozon_user` (`uid`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

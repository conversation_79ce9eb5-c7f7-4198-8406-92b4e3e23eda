<?php
include("../includes/common.php");
$id = intval($_GET['id']);
$row=$DB->getRow("SELECT * FROM ozon_production WHERE id=:id AND uid=:uid limit 1", [':id'=>$id,':uid'=>$uid]);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/assets/component/layui/layui.js"></script>
    <script src="/assets/js/jquery-3.6.0.min.js"></script>
    <!-- 美图设计室图片编辑器SDK -->
    <script src="https://public.static.meitudata.com/xiuxiu-pc/image-editor-sdk/3.0.0/dist/index.min.js"></script>
    <title>商品详情编辑系统 - 支持图文混排</title>
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        .debug-bar {
            position: fixed;
            top: 0;
            right: 0;
            background: #333;
            padding: 10px 20px;
            color: white;
            z-index: 1000;
            display: flex;
            gap: 15px;
            border-radius: 0 0 0 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .debug-bar button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            background: #444;
            color: #fff;
            font-size: 14px;
        }
        
        .debug-bar button:hover {
            background: #555;
            transform: translateY(-1px);
        }
        
        /* 为不同功能按钮添加颜色区分 */
        .debug-bar button:nth-child(1) { background: #3498db; }
        .debug-bar button:nth-child(2) { background: #2ecc71; }
        .debug-bar button:nth-child(3) { background: #e67e22; }

        .gallery-container {
            max-width: 1200px;
            margin: 60px auto 0;
            padding: 20px;
        }

        .image-card, .text-card {
            position: relative;
            margin: 20px 0;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s;
        }

        .text-card {
            background: #f9f9f9;
            min-height: 80px;
            padding: 15px;
        }

        .text-content {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            min-height: 40px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .text-content:empty::before {
            content: "点击编辑按钮添加文本内容...";
            color: #999;
            font-style: italic;
        }

        .edit-text-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: #2ecc71;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            opacity: 0.85;
            transition: all 0.3s;
            z-index: 3;
        }
        
        .edit-text-btn:hover {
            background: #27ae60;
            opacity: 1;
            transform: scale(1.05);
        }

        /* 文本内容HTML渲染样式 */
        .text-content h1 {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }

        .text-content h2 {
            font-size: 20px;
            font-weight: bold;
            margin: 8px 0;
            color: #333;
        }

        .text-content p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .text-content strong {
            font-weight: bold;
        }

        .text-content em {
            font-style: italic;
        }

        .text-content u {
            text-decoration: underline;
        }

        /* 文本卡片悬停效果 */
        .text-card:hover {
            border-color: #2ecc71;
            box-shadow: 0 2px 8px rgba(46, 204, 113, 0.2);
        }

        .responsive-image {
            width: 100%;
            height: auto;
            display: block;
        }

        .delete-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff4757;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 4px;
            opacity: 0.9;
        }
        
        /* 编辑按钮样式 */
        .edit-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.85;
            transition: all 0.3s;
            z-index: 3;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .edit-btn:hover {
            background: #2980b9;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .edit-btn::before {
            content: "✏";
            font-size: 16px;
            font-weight: bold;
        }

        /* 翻译按钮样式 */
        .translate-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #2ecc71;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 4px;
            opacity: 0.9;
            z-index: 2;
        }
        
        /* 右上角位置调整按钮样式 */
        .move-controls {
            position: absolute;
            right: 50px;
            top: 10px;
            display: flex;
            flex-direction: column;
            gap: 2px;
            z-index: 4;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .image-card:hover .move-controls,
        .text-card:hover .move-controls {
            opacity: 1;
        }
        
        .move-btn {
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .move-btn:hover {
            background: rgba(0,0,0,0.9);
            transform: scale(1.1);
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }
        
        .move-btn:disabled {
            background: rgba(0,0,0,0.3);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .move-btn.up::before {
            content: "↑";
            font-weight: bold;
        }
        
        .move-btn.down::before {
            content: "↓";
            font-weight: bold;
        }
        
        /* 移动端适配 */
        @media (max-width: 767px) {
            .gallery-container {
                padding: 10px;
            }
            
            .image-card, .text-card {
                margin: 15px -10px;
                border-radius: 0;
            }
        }

        /* 调试模式 */
        .mobile-preview .image-card,
        .mobile-preview .text-card {
            max-width: 375px;
            margin: 15px auto;
        }
        
        /* 添加内容按钮 */
        .add-image-bar {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }
        
        .add-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            min-width: 120px;
        }
        
        .add-btn:hover {
            background: #219a52;
            transform: translateY(-2px);
        }
        
        /* 隐藏的文件输入 */
        #imageUpload {
            display: none;
        }
        
        /* 加载状态指示 */
        .image-card:not(.loaded),
        .text-card:not(.loaded) {
            background: #f5f6fa;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }
        
        /* 上传进度条 */
        .upload-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: #3498db;
            transition: width 0.3s;
            z-index: 5;
        }
        
        /* 拖拽上传区域样式 */
        #dragUploadArea {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        #dragUploadArea:hover {
            border-color: #3b82f6 !important;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59,130,246,0.15) !important;
        }
        
        #dragUploadArea:hover i {
            color: #3b82f6 !important;
            transform: scale(1.1);
        }
        
        #dragUploadArea:hover p {
            color: #1e40af !important;
        }
        
        /* 拖拽时的样式 */
        #dragUploadArea.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <!-- 调试工具栏 -->
    <div class="debug-bar">
        <button onclick="toggleDeviceMode()">切换设备模式</button>
        <button onclick="translateAllImages()">翻译所有图片</button>
        <button onclick="saveAllChanges()">保存所有修改</button>
    </div>

    <!-- 内容容器 -->
    <div class="gallery-container" id="gallery"></div>
    
    <!-- 拖拽上传区域 -->
    <div id="dragUploadArea" style="
        position: fixed;
        bottom: 100px;
        right: 20px;
        width: 200px;
        height: 120px;
        border: 2px dashed #cbd5e1;
        border-radius: 12px;
        background: #f8fafc;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 999;
    ">
        <i class="layui-icon layui-icon-upload" style="font-size: 32px; color: #94a3b8; margin-bottom: 8px;"></i>
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">拖拽图片到这里<br>或点击上传图片</p>
    </div>
    
    <!-- 添加内容按钮组 -->
    <div class="add-image-bar">
        <button class="add-btn" onclick="addTextContent()">📝 添加文本</button>
        <button class="add-btn" onclick="addImageByUpload()">📷 上传图片</button>
        <button class="add-btn" onclick="addImageByUrl()">🔗 链接添加</button>
    </div>
    
    <!-- 隐藏的文件上传输入 -->
    <input type="file" id="imageUpload" accept="image/*" multiple>

    <script>
        // 美图SDK初始化
        let isEditorInitialized = false;
        
        function initMeiTuEditor() {
            if (isEditorInitialized) return;
            
            try {
                MTImageEditor.init({
                    moduleName: 'image-editor-sdk',
                    accessKey: 'test_app_key_demo',
                    title: '美图设计室Web版',
                    fullscreen: true,
                    resizeAble: true
                });
                isEditorInitialized = true;
                console.log('美图SDK初始化成功');
            } catch (error) {
                console.error('美图SDK初始化失败:', error);
            }
        }
        
        // 等待SDK加载完成
        window.addEventListener('load', () => {
            setTimeout(initMeiTuEditor, 500);
        });
        
        // 打开美图编辑器
        function openImageEditor(imageUrl, index) {
            if (!isEditorInitialized) {
                layer.msg('美图编辑器初始化中，请稍候...', {icon: 16});
                initMeiTuEditor();
                setTimeout(() => openImageEditor(imageUrl, index), 1000);
                return;
            }
            
            try {
                MTImageEditor.open({
                    imgSrc: imageUrl,
                    onOk: function(result) {
                        saveEditedImage(result.data, index);
                    },
                    onError: function(error) {
                        layer.msg('编辑器打开失败: ' + error.message, {icon: 2});
                    },
                    onClose: function() {
                        console.log('编辑器已关闭');
                    }
                });
            } catch (error) {
                layer.msg('打开编辑器失败: ' + error.message, {icon: 2});
            }
        }
        
        // 保存编辑后的图片
        async function saveEditedImage(base64Data, index) {
            const loading = layer.msg('保存编辑后的图片...', {icon: 16, time: 0});
            
            try {
                const response = await fetch('./ajax.php?act=save_edited_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_data: base64Data,
                        product_id: '<?=$id?>'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 更新图片数据
                    const block = productData.content[0].blocks[index];
                    block.img.src = result.image_url;
                    block.img.srcMobile = result.image_url;
                    
                    // 重新渲染画廊
                    await initGallery();
                    
                    layer.msg('图片编辑并保存成功！', {icon: 1});
                } else {
                    throw new Error(result.message || '保存失败');
                }
            } catch (error) {
                layer.msg('保存失败: ' + error.message, {icon: 2});
            } finally {
                layer.close(loading);
            }
        }

        // 原始JSON数据
        const productData = <?=$row['json']?>;

        // 数据兼容性处理
        function ensureDataCompatibility() {
            if (productData && productData.content && productData.content[0] && productData.content[0].blocks) {
                productData.content[0].blocks.forEach(block => {
                    // 如果没有type字段，根据内容判断类型
                    if (!block.type) {
                        if (block.img) {
                            block.type = 'image';
                        } else if (block.text) {
                            block.type = 'text';
                        } else {
                            // 默认为图片类型（向后兼容）
                            block.type = 'image';
                        }
                    }
                });
            }
        }

        // 初始化函数
        async function initGallery() {
            const gallery = document.getElementById('gallery');
            gallery.innerHTML = '';
        
            // 按顺序创建容器占位
            const placeholderFragment = document.createDocumentFragment();
            productData.content[0].blocks.forEach((block, index) => {
                const card = document.createElement('div');
                card.className = block.type === 'text' ? 'text-card' : 'image-card';
                card.dataset.index = index;
                card.style.minHeight = '50px';
                placeholderFragment.appendChild(card);
            });
            gallery.appendChild(placeholderFragment);
        
            // 严格按顺序加载
            for (const [index, block] of productData.content[0].blocks.entries()) {
                try {
                    const card = gallery.querySelector(`[data-index="${index}"]`);
                    
                    if (block.type === 'text') {
                        // 处理文本块
                        await renderTextBlock(card, block, index);
                    } else {
                        // 处理图片块
                        await renderImageBlock(card, block, index);
                    }
        
                } catch (e) {
                    console.error(`内容块${index}加载失败:`, e);
                }
            }
        }

        // 渲染文本块
        async function renderTextBlock(card, block, index) {
            // 创建文本内容区域
            const textContent = document.createElement('div');
            textContent.className = 'text-content';
            textContent.innerHTML = block.text || '点击编辑文本内容...';
            
            // 添加编辑按钮
            const editBtn = document.createElement('button');
            editBtn.className = 'edit-text-btn';
            editBtn.innerHTML = '✏️ 编辑';
            editBtn.onclick = () => editTextContent(index);
            
            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-btn';
            deleteBtn.innerHTML = '删除';
            deleteBtn.onclick = () => deleteBlock(index);
            
            // 创建移动按钮
            const moveControls = createMoveControls(index);
            
            // 填充卡片内容
            card.innerHTML = '';
            card.appendChild(textContent);
            card.appendChild(moveControls);
            card.appendChild(editBtn);
            card.appendChild(deleteBtn);
            
            // 添加加载完成标记
            card.classList.add('loaded');
        }

        // 渲染图片块
        async function renderImageBlock(card, block, index) {
            // 创建图片元素
            const img = document.createElement('img');
            img.className = 'responsive-image';
            img.src = block.img.src;

            // 添加翻译按钮
            const translateBtn = document.createElement('button');
            translateBtn.className = 'translate-btn';
            translateBtn.innerHTML = '翻译';
            translateBtn.onclick = () => translateImage(index);

            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-btn';
            deleteBtn.innerHTML = '删除';
            deleteBtn.onclick = () => deleteBlock(index);
            
            // 编辑按钮
            const editBtn = document.createElement('button');
            editBtn.className = 'edit-btn';
            editBtn.onclick = () => openImageEditor(block.img.src, index);

            // 同步加载图片
            await new Promise(resolve => {
                img.onload = () => {
                    if (!block.img.width || !block.img.height) {
                        block.img.width = img.naturalWidth;
                        block.img.height = img.naturalHeight;
                    }
                    resolve();
                };
                img.onerror = resolve;
            });

            // 创建移动按钮
            const moveControls = createMoveControls(index);
            
            // 填充卡片内容
            card.innerHTML = '';
            card.appendChild(translateBtn);
            card.appendChild(img);
            card.appendChild(moveControls);
            card.appendChild(editBtn);
            card.appendChild(deleteBtn);

            // 添加尺寸标记
            card.dataset.size = `${block.img.width}x${block.img.height}`;

            // 添加加载完成标记
            card.classList.add('loaded');
        }

        // 创建移动控制按钮
        function createMoveControls(index) {
            const moveControls = document.createElement('div');
            moveControls.className = 'move-controls';
            
            const upBtn = document.createElement('button');
            upBtn.className = 'move-btn up';
            upBtn.title = '上移';
            upBtn.onclick = () => moveBlock(index, -1);
            upBtn.disabled = index === 0;
            
            const downBtn = document.createElement('button');
            downBtn.className = 'move-btn down';
            downBtn.title = '下移';
            downBtn.onclick = () => moveBlock(index, 1);
            downBtn.disabled = index === productData.content[0].blocks.length - 1;
            
            moveControls.appendChild(upBtn);
            moveControls.appendChild(downBtn);
            
            return moveControls;
        }

        // 删除内容块（图片或文本）
        function deleteBlock(index) {
            const block = productData.content[0].blocks[index];
            const blockType = block.type === 'text' ? '文本' : '图片';
            
            layer.confirm(`确定要删除这个${blockType}内容吗？`, {
                btn: ['确定', '取消']
            }, function() {
                productData.content[0].blocks.splice(index, 1);
                initGallery();
                layer.msg('删除成功', {icon: 1});
            });
        }

        // 移动内容块
        function moveBlock(index, direction) {
            const blocks = productData.content[0].blocks;
            const newIndex = index + direction;
            
            if (newIndex >= 0 && newIndex < blocks.length) {
                // 交换位置
                [blocks[index], blocks[newIndex]] = [blocks[newIndex], blocks[index]];
                initGallery();
                layer.msg('移动成功', {icon: 1});
            }
        }

        // 编辑文本内容
        function editTextContent(index) {
            const block = productData.content[0].blocks[index];
            const currentText = block.text || '';
            
            layer.open({
                type: 1,
                title: '编辑文本内容',
                area: ['800px', '600px'],
                content: `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">文本内容：</label>
                            <textarea id="textEditor" style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 4px; padding: 10px; font-size: 14px; line-height: 1.5; resize: vertical;">${currentText}</textarea>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">格式化选项：</label>
                            <button type="button" onclick="formatText('bold')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">粗体</button>
                            <button type="button" onclick="formatText('italic')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">斜体</button>
                            <button type="button" onclick="formatText('underline')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">下划线</button>
                            <button type="button" onclick="formatText('h1')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">标题1</button>
                            <button type="button" onclick="formatText('h2')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">标题2</button>
                            <button type="button" onclick="formatText('p')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">段落</button>
                        </div>
                        <div style="text-align: right;">
                            <button type="button" onclick="saveTextContent(${index})" style="padding: 8px 20px; background: #1E9FFF; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">保存</button>
                            <button type="button" onclick="layer.closeAll()" style="padding: 8px 20px; background: #ccc; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                        </div>
                    </div>
                `,
                success: function() {
                    // 添加格式化函数到全局
                    window.formatText = function(format) {
                        const textarea = document.getElementById('textEditor');
                        const start = textarea.selectionStart;
                        const end = textarea.selectionEnd;
                        const selectedText = textarea.value.substring(start, end);
                        
                        let formattedText = '';
                        switch(format) {
                            case 'bold':
                                formattedText = `<strong>${selectedText}</strong>`;
                                break;
                            case 'italic':
                                formattedText = `<em>${selectedText}</em>`;
                                break;
                            case 'underline':
                                formattedText = `<u>${selectedText}</u>`;
                                break;
                            case 'h1':
                                formattedText = `<h1>${selectedText}</h1>`;
                                break;
                            case 'h2':
                                formattedText = `<h2>${selectedText}</h2>`;
                                break;
                            case 'p':
                                formattedText = `<p>${selectedText}</p>`;
                                break;
                        }
                        
                        if (formattedText) {
                            textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
                            textarea.focus();
                            textarea.setSelectionRange(start, start + formattedText.length);
                        }
                    };
                    
                    window.saveTextContent = function(index) {
                        const newText = document.getElementById('textEditor').value;
                        productData.content[0].blocks[index].text = newText;
                        initGallery();
                        layer.closeAll();
                        layer.msg('文本保存成功', {icon: 1});
                    };
                }
            });
        }

        // 添加文本内容
        function addTextContent() {
            layer.open({
                type: 1,
                title: '添加文本内容',
                area: ['800px', '600px'],
                content: `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">文本内容：</label>
                            <textarea id="newTextEditor" placeholder="请输入文本内容，支持HTML格式..." style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 4px; padding: 10px; font-size: 14px; line-height: 1.5; resize: vertical;"></textarea>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">格式化选项：</label>
                            <button type="button" onclick="formatNewText('bold')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">粗体</button>
                            <button type="button" onclick="formatNewText('italic')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">斜体</button>
                            <button type="button" onclick="formatNewText('underline')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">下划线</button>
                            <button type="button" onclick="formatNewText('h1')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">标题1</button>
                            <button type="button" onclick="formatNewText('h2')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">标题2</button>
                            <button type="button" onclick="formatNewText('p')" style="margin-right: 5px; padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 3px; cursor: pointer;">段落</button>
                        </div>
                        <div style="text-align: right;">
                            <button type="button" onclick="saveNewTextContent()" style="padding: 8px 20px; background: #1E9FFF; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">添加</button>
                            <button type="button" onclick="layer.closeAll()" style="padding: 8px 20px; background: #ccc; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                        </div>
                    </div>
                `,
                success: function() {
                    // 添加格式化函数到全局
                    window.formatNewText = function(format) {
                        const textarea = document.getElementById('newTextEditor');
                        const start = textarea.selectionStart;
                        const end = textarea.selectionEnd;
                        const selectedText = textarea.value.substring(start, end);
                        
                        let formattedText = '';
                        switch(format) {
                            case 'bold':
                                formattedText = `<strong>${selectedText}</strong>`;
                                break;
                            case 'italic':
                                formattedText = `<em>${selectedText}</em>`;
                                break;
                            case 'underline':
                                formattedText = `<u>${selectedText}</u>`;
                                break;
                            case 'h1':
                                formattedText = `<h1>${selectedText}</h1>`;
                                break;
                            case 'h2':
                                formattedText = `<h2>${selectedText}</h2>`;
                                break;
                            case 'p':
                                formattedText = `<p>${selectedText}</p>`;
                                break;
                        }
                        
                        if (formattedText) {
                            textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
                            textarea.focus();
                            textarea.setSelectionRange(start, start + formattedText.length);
                        }
                    };
                    
                    window.saveNewTextContent = function() {
                        const newText = document.getElementById('newTextEditor').value.trim();
                        if (!newText) {
                            layer.msg('请输入文本内容', {icon: 2});
                            return;
                        }
                        
                        // 创建新的文本块
                        const newBlock = {
                            type: 'text',
                            text: newText
                        };
                        
                        productData.content[0].blocks.push(newBlock);
                        initGallery();
                        layer.closeAll();
                        layer.msg('文本添加成功', {icon: 1});
                    };
                }
            });
        }
        
        // 通过上传添加图片
        function addImageByUpload() {
            const fileInput = document.getElementById('imageUpload');
            fileInput.onchange = handleFileUpload;
            fileInput.click();
        }
        
        // 处理文件上传
        async function handleFileUpload(event) {
            const files = event.target.files;
            if (!files.length) return;
            
            const loading = layer.msg('正在上传图片...', {icon: 16, time: 0});
            
            try {
                for (let file of files) {
                    await uploadSingleImage(file);
                }
                
                await initGallery();
                layer.msg(`成功上传${files.length}张图片`, {icon: 1});
                
            } catch (error) {
                layer.msg('上传失败: ' + error.message, {icon: 2});
            } finally {
                layer.close(loading);
                // 清空文件输入
                event.target.value = '';
            }
        }
        
        // 上传单张图片
        async function uploadSingleImage(file) {
            const formData = new FormData();
            formData.append('file', file); // 注意这里是 'file' 不是 'image'
            
            const response = await fetch('./ajax.php?act=upload_image', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.code === 0) {
                // 获取图片尺寸
                const size = await getImageSize(result.data.src);
                
                // 添加到数据中
                const newBlock = {
                    type: 'image',
                    img: {
                        src: result.data.src,
                        srcMobile: result.data.src,
                        width: size.width,
                        height: size.height
                    }
                };
                
                productData.content[0].blocks.push(newBlock);
            } else {
                throw new Error(result.msg || '上传失败');
            }
        }
        
        // 通过链接添加图片
        function addImageByUrl() {
            layer.prompt({
                title: '输入图片链接',
                placeholder: 'https://example.com/image.jpg',
                area: ['400px', '200px']
            }, function(value, layerIndex) {
                if (value && isValidImageUrl(value)) {
                    addImageFromUrl(value);
                    layer.close(layerIndex);
                } else {
                    layer.msg('请输入有效的图片链接', {icon: 2});
                }
            });
        }
        
        // 验证图片链接
        function isValidImageUrl(url) {
            const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp)$/i;
            return url.startsWith('http') && (imageExtensions.test(url) || url.includes('placeholder'));
        }
        
        // 从链接添加图片
        async function addImageFromUrl(imageUrl) {
            const loading = layer.msg('正在添加图片...', {icon: 16, time: 0});
            
            try {
                // 获取图片尺寸
                const size = await getImageSize(imageUrl);
                
                // 添加到数据中
                const newBlock = {
                    type: 'image',
                    img: {
                        src: imageUrl,
                        srcMobile: imageUrl,
                        width: size.width,
                        height: size.height
                    }
                };
                
                productData.content[0].blocks.push(newBlock);
                
                await initGallery();
                layer.msg('图片添加成功', {icon: 1});
                
            } catch (error) {
                layer.msg('添加失败: ' + error.message, {icon: 2});
            } finally {
                layer.close(loading);
            }
        }

        // 切换设备模式
        function toggleDeviceMode() {
            document.body.classList.toggle('mobile-preview');
        }

        // 翻译功能实现
        async function translateImage(index) {
            const block = productData.content[0].blocks[index];
            
            // 只对图片类型进行翻译
            if (block.type !== 'image' && !block.img) {
                layer.msg('只能翻译图片内容', {icon: 2});
                return;
            }
            
            const originalImg = block.img;
            const loading = layer.msg('正在翻译图片...', {icon: 16, time: 0});
            
            try {
                const res = await fetch('./ajax.php?act=json_translate', {
                    method: 'POST',
                    body: JSON.stringify({
                        url: isMobile() ? block.img.srcMobile : block.img.src
                    })
                });
                
                const { translatedUrl } = await res.json();
                
                productData.content[0].blocks[index].img = {
                    ...originalImg,
                    src: translatedUrl,
                    srcMobile: translatedUrl,
                    width: await getImageSize(translatedUrl).width,
                    height: await getImageSize(translatedUrl).height
                };
                
                await initGallery();
                layer.msg('图片翻译成功', {icon: 1});
            } catch (error) {
                console.error('翻译失败:', error);
                layer.msg('翻译失败: ' + error.message, {icon: 2});
            } finally {
                layer.close(loading);
            }
        }
        
        // 全部翻译功能
        async function translateAllImages() {
            const load = layer.msg('开始翻译全部图片...', {icon: 16, time:0});
            
            try {
                const blocks = productData.content[0].blocks;
                let translatedCount = 0;
                
                for(let i=0; i<blocks.length; i++) {
                    const block = blocks[i];
                    // 只翻译图片类型的内容
                    if (block.type === 'image' || block.img) {
                        await translateImage(i);
                        translatedCount++;
                    }
                }
                
                if (translatedCount > 0) {
                    layer.msg(`成功翻译${translatedCount}张图片`, {icon:1});
                } else {
                    layer.msg('没有找到可翻译的图片', {icon:3});
                }
            } catch(e) {
                layer.msg('翻译失败: ' + e.message, {icon:2});
            } finally {
                layer.close(load);
            }
        }
        
        // 获取图片尺寸
        function getImageSize(url) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = function() {
                    resolve({
                        width: this.width,
                        height: this.height
                    });
                };
                img.src = url;
            });
        }

        // 设备检测
        function isMobile() {
            return window.innerWidth <= 767;
        }

        // 保存所有修改
        async function saveAllChanges() {
            const loading = layer.msg('正在保存数据...', { icon: 16, time: 0 });

            try {
                const payload = productData;

                const res = await fetch('./ajax.php?act=save_json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        id: '<?=$id?>', 
                        data: payload,
                        table: 'ozon_production'
                    })
                });

                if (!res.ok) {
                    const errorText = await res.text();
                    throw new Error(`服务器错误: ${res.status} ${errorText}`);
                }

                const result = await res.json();
                if (result.code === 0) {
                    layer.msg('保存成功！', { icon: 1 });
                } else {
                    layer.msg(result.msg || '保存失败', { icon: 2 });
                }

            } catch (error) {
                console.error('保存操作失败:', error);
                layer.msg(`保存失败: ${error.message}`, { icon: 2 });
            } finally {
                layer.close(loading);
            }
        }
        
        // 初始化拖拽上传
        function initDragUpload() {
            const dragArea = document.getElementById('dragUploadArea');
            
            // 拖拽区域样式控制
            function updateDragAreaStyle(isDragOver) {
                if (isDragOver) {
                    dragArea.style.borderColor = '#3b82f6';
                    dragArea.style.backgroundColor = '#eff6ff';
                    dragArea.style.color = '#3b82f6';
                } else {
                    dragArea.style.borderColor = '#cbd5e1';
                    dragArea.style.backgroundColor = '#f8fafc';
                    dragArea.style.color = '#64748b';
                }
            }
            
            // 防止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dragArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // 拖拽进入和离开效果
            ['dragenter', 'dragover'].forEach(eventName => {
                dragArea.addEventListener(eventName, () => updateDragAreaStyle(true), false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dragArea.addEventListener(eventName, () => updateDragAreaStyle(false), false);
            });
            
            // 处理文件拖拽放置
            dragArea.addEventListener('drop', handleDrop, false);
            
            // 点击拖拽区域选择文件
            dragArea.addEventListener('click', function() {
                const input = document.createElement('input');
                input.type = 'file';
                input.multiple = true;
                input.accept = 'image/*';
                input.onchange = function(e) {
                    handleFiles(e.target.files);
                };
                input.click();
            });
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }
            
            function handleFiles(files) {
                if (files.length === 0) return;
                
                // 过滤图片文件
                const imageFiles = Array.from(files).filter(file => {
                    return file.type.startsWith('image/');
                });
                
                if (imageFiles.length === 0) {
                    layer.msg('请选择图片文件', {icon: 2});
                    return;
                }
                
                if (imageFiles.length !== files.length) {
                    layer.msg(`已过滤非图片文件，将上传 ${imageFiles.length} 张图片`, {icon: 3});
                }
                
                // 检查文件大小
                const oversizedFiles = imageFiles.filter(file => file.size > 10 * 1024 * 1024);
                if (oversizedFiles.length > 0) {
                    layer.msg(`有 ${oversizedFiles.length} 个文件超过10MB限制，已跳过`, {icon: 2});
                    const validFiles = imageFiles.filter(file => file.size <= 10 * 1024 * 1024);
                    if (validFiles.length === 0) return;
                    uploadMultipleFiles(validFiles);
                } else {
                    uploadMultipleFiles(imageFiles);
                }
            }
            
            async function uploadMultipleFiles(files) {
                if (files.length === 0) return;
                
                const loading = layer.msg(`正在上传 ${files.length} 张图片...`, {icon: 16, time: 0});
                let uploadedCount = 0;
                let failedCount = 0;
                
                try {
                    for (let file of files) {
                        try {
                            await uploadSingleImage(file);
                            uploadedCount++;
                        } catch (error) {
                            failedCount++;
                            console.error('上传失败:', error);
                        }
                    }
                    
                    await initGallery();
                    
                    if (uploadedCount > 0 && failedCount === 0) {
                        layer.msg(`成功上传 ${uploadedCount} 张图片`, {icon: 1});
                    } else if (uploadedCount > 0 && failedCount > 0) {
                        layer.msg(`成功上传 ${uploadedCount} 张图片，${failedCount} 张图片上传失败`, {icon: 3});
                    } else {
                        layer.msg('所有图片上传失败，请重试', {icon: 2});
                    }
                    
                } catch (error) {
                    layer.msg('上传过程中出现错误: ' + error.message, {icon: 2});
                } finally {
                    layer.close(loading);
                }
            }
        }

        // 初始化加载
        window.addEventListener('DOMContentLoaded', function() {
            ensureDataCompatibility();
            initGallery();
            initDragUpload();
        });
        window.addEventListener('resize', initGallery);
    </script>
</body>
</html>
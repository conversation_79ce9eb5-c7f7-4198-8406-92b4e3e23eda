<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 内存和性能设置
ini_set('memory_limit', '512M');
set_time_limit(0);

class SafeConsumer {
    private $connection;
    private $channel;
    private $maxRetries = 3;
    private $logger;
    private $config;
    private $db;
    
    public function __construct($config,$DB) {
        $this->config = $config;
        $this->logger = new ConsumerLogger();
        $this->connectRabbitMQ();
        $this->db = $DB; // 假设有一个数据库封装类
    }
    
    private function connectRabbitMQ() {
        try {
            $this->connection = new AMQPStreamConnection(
                $this->config['host'],
                $this->config['port'],
                $this->config['user'],
                $this->config['pwd'],
                '/',
                false,
                'AMQPLAIN',
                null,
                'en_US',
                60,  // 连接超时
                30,  // 读写超时
                null,
                true // 保持连接
            );
            
            $this->channel = $this->connection->channel();
            $this->channel->queue_declare('CollectProducts', false, true, false, false);
            $this->channel->basic_qos(null, 1, null);
            
        } catch (Exception $e) {
            $this->logger->error("RabbitMQ连接失败: ".$e->getMessage());
            throw $e;
        }
    }
    
    public function consume() {
        $callback = function ($msg) {
            $startTime = microtime(true);
            $messageId = uniqid();
            
            try {
                $this->logger->info("[$messageId] 开始处理消息");
                
                $data = $this->parseMessage($msg->body);
                $this->logger->info("[$messageId] 收到 {$data['type']} ID: ".($data['id']??$data['sku']));
                
                $this->processMessageWithRetry($messageId, $data);
                
                $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
                
                $duration = round(microtime(true) - $startTime, 2);
                $this->logger->info("[$messageId] 处理完成 耗时: {$duration}s");
                
            } catch (InvalidMessageException $e) {
                $this->logger->error("[$messageId] 消息格式错误: ".$e->getMessage());
                $msg->delivery_info['channel']->basic_reject($msg->delivery_info['delivery_tag'], false);
                
            } catch (RecoverableException $e) {
                $this->logger->error("[$messageId] 可重试错误: ".$e->getMessage());
                $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag']);
                
            } catch (Exception $e) {
                $this->logger->error("[$messageId] 不可恢复错误: ".$e->getMessage());
                $msg->delivery_info['channel']->basic_reject($msg->delivery_info['delivery_tag'], false);
            }
            
            $this->checkResourceUsage($messageId);
        };
        
        $this->channel->basic_consume('CollectProducts', '', false, false, false, false, $callback);
        
        $this->logger->info("消费者启动，等待消息...");
        
        while (count($this->channel->callbacks)) {
            $this->channel->wait();
        }
    }
    
    private function parseMessage($body) {
        $data = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidMessageException("无效的JSON数据");
        }
        
        if (empty($data['type'])) {
            throw new InvalidMessageException("消息缺少type字段");
        }
        
        return $data;
    }
    
    private function processMessageWithRetry($messageId, $data) {
        $retryCount = 0;
        $lastError = null;
        
        while ($retryCount < $this->maxRetries) {
            try {
                $this->processMessage($data);
                return;
                
            } catch (RecoverableException $e) {
                $retryCount++;
                $lastError = $e;
                $waitTime = pow(2, $retryCount); // 指数退避
                $this->logger->warning("[$messageId] 尝试 {$retryCount}/{$this->maxRetries} 失败，等待 {$waitTime}s 后重试");
                sleep($waitTime);
            }
        }
        
        throw new Exception("重试 {$this->maxRetries} 次后仍然失败: ".$lastError->getMessage());
    }
    
    private function processMessage($data) {
        try {
            $this->handleProduction($data);            #采集商品处理
        } catch (Exception $e) {
            throw new RecoverableException("处理 {$data['type']} 类型消息失败: ".$e->getMessage());
        }
    }
    
    private function handleProduction($data) {
        $this->logger->info("开始处理产品数据: ID {$data['id']}");
        if($data['id']==1){
            return true;
        }
        if ($data['datatype'] == '1688') {
            $productionData = $this->process1688Data($data);
        } else {
            $productionData = $this->processGenericData($data);
        }
        
        if (!empty($productionData)) {
            $this->updateProduction($data['id'], $productionData);
        }
        
        $this->logger->info("产品数据处理完成: ID {$data['id']}");
    }
    
    private function process1688Data($data) {
        if (empty($data['orderUrl'])) {
            $this->logger->warning("1688数据缺少orderUrl");
            return null;
        }
        
        $alidata = ali1688data($data['orderUrl']);
        if (empty($alidata)) {
            throw new RecoverableException("获取1688数据失败");
        }
        
        $productionData = [];
        
        if (!empty($alidata['title'])) {
            $productionData['title'] = $alidata['title'];
        }
        
        if (!empty($alidata['description'])) {
            $productionData['json'] = json_encode(jsondata($alidata['description']));
        }
        
        if(!empty($data['jsonimgData'])){
            $productionData['json'] = json_encode(jsondata($data['jsonimgData']));
        }
        // 处理图片、视频等数据...
        
        // 记录详细日志
        $this->logger->debug("1688数据处理结果", $productionData);
        
        return $productionData;
    }
    
    private function updateProduction($id, $data) {
        try {
            $this->db->beginTransaction();
            
            $result = $this->db->update('production', $data, ['id' => $id]);
            if (!$result) {
                throw new Exception("更新产品数据失败: ".$this->db->error());
            }
            
            $this->db->commit();
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    private function processGenericData($data) {
        $this->logger->info("处理通用产品数据: ID {$data['id']}");
        
        $productionData = [];
        
        // 处理标题
        if (!empty($data['title'])) {
            $productionData['title'] = $data['title'];
        }
        
        if(!empty($data['jsonimgData'])){
            $productionData['json'] = json_encode(jsondata($data['jsonimgData']));
        }
        if ($data['pttype']=='ozon'){
            $categories = $this->ozoncategories($data['orderUrl']);
            if($categories){
                $productionData['category_chain'] = $categories;
            }
        }
        // 处理图片
        if (!empty($data['images'])) {
            $productionData['images'] = $this->processImages($data['images']);
        }
        
        // 记录详细日志
        $this->logger->debug("通用数据处理结果", $productionData);
        
        return $productionData;
    }
    
    private function ozoncategories($url){
        $sku = (string) get_sku($url);
        $referer = 'https://seller.ozon.ru/app/products/add/general-info';
        $result = doRequests('https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku', ['skus' => [$sku]], $referer);
        $json = json_decode($result['body'], true);
        if(isset($json['resolved_categories_by_sku'][$sku])){
            $data = $json['resolved_categories_by_sku'][$sku];
            $processed[] = $data['description_category_id_level_2'];
            $processed[] = $data['description_category_id_level_3'];
            $processed[] = $data['description_type_id'];
            $return['ids'] = implode(',', $processed);
            return json_encode($return);
        }
        return false;
    }
    
    private function processSlowTask($data) {
        $this->logger->info("开始处理慢速任务: 类型 {$data['type']}");
        
        try {
            // 根据不同类型处理慢速任务
            switch ($data['type']) {
                case 'image_processing':
                    return $this->processImageTask($data);
                case 'data_export':
                    return $this->processExportTask($data);
                case 'report_generation':
                    return $this->processReportTask($data);
                default:
                    throw new Exception("未知的慢速任务类型: {$data['type']}");
            }
        } catch (Exception $e) {
            $this->logger->error("慢速任务处理失败: ".$e->getMessage());
            throw new RecoverableException("慢速任务处理失败: ".$e->getMessage());
        }
    }
    
    private function processImages($images) {
        if (strpos($images, ';') === false) {
            return replaceImageUrl($images);
        }else{
            $processed = [];
            $images = explode(";",$images);
            foreach ($images as $image) {
                $image = replaceImageUrl($image);
                $processed[] = $image;
            }
            return implode(';', $processed);;
        }
    }
    
    private function processImageTask($data) {
        // 实现图片处理逻辑
        $this->logger->debug("处理图片任务", $data);
        return true;
    }
    
    private function processExportTask($data) {
        // 实现数据导出逻辑
        $this->logger->debug("处理导出任务", $data);
        return true;
    }
    
    private function processReportTask($data) {
        // 实现报告生成逻辑
        $this->logger->debug("处理报告任务", $data);
        return true;
    }
    
    
    private function checkResourceUsage($messageId) {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryUsage > 100 * 1024 * 1024) { // 超过100MB
            $this->logger->warning("[$messageId] 内存使用过高: ".round($memoryUsage/1024/1024, 2)."MB");
        }
        
        // 可以添加更多资源检查...
    }
    
    public function __destruct() {
        try {
            if ($this->channel && $this->channel->is_open()) {
                $this->channel->close();
            }
            
            if ($this->connection && $this->connection->isConnected()) {
                $this->connection->close();
            }
        } catch (Exception $e) {
            $this->logger->error("关闭连接时出错: ".$e->getMessage());
        }
    }
}

// 自定义异常类
class InvalidMessageException extends Exception {}
class RecoverableException extends Exception {}

// 日志类
class ConsumerLogger {
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    private function log($level, $message, $context = []) {
        $logEntry = sprintf(
            "[%s] [%s] %s %s\n",
            date('Y-m-d H:i:s'),
            $level,
            $message,
            !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''
        );
        
        file_put_contents(__DIR__.'/logs/consumer.log', $logEntry, FILE_APPEND);
        
        // 同时输出到控制台
        echo $logEntry;
    }
}

// 启动消费者
try {
    $consumer = new SafeConsumer($Raconfig,$DB);
    $consumer->consume();
} catch (Exception $e) {
    echo "消费者启动失败: ".$e->getMessage()."\n";
    exit(1);
}
<?php return array (
  'root' => 
  array (
    'pretty_version' => '1.0.0+no-version-set',
    'version' => '1.0.0.0',
    'aliases' => 
    array (
    ),
    'reference' => NULL,
    'name' => '__root__',
  ),
  'versions' => 
  array (
    '__root__' => 
    array (
      'pretty_version' => '1.0.0+no-version-set',
      'version' => '1.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => NULL,
    ),
    'cboden/ratchet' => 
    array (
      'pretty_version' => 'v0.4.4',
      'version' => '0.4.4.0',
      'aliases' => 
      array (
      ),
      'reference' => '5012dc954541b40c5599d286fd40653f5716a38f',
    ),
    'composer/pcre' => 
    array (
      'pretty_version' => '3.3.2',
      'version' => '3.3.2.0',
      'aliases' => 
      array (
      ),
      'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
    ),
    'evenement/evenement' => 
    array (
      'pretty_version' => 'v3.0.2',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '0a16b0d71ab13284339abb99d9d2bd813640efbc',
    ),
    'firebase/php-jwt' => 
    array (
      'pretty_version' => 'v6.11.1',
      'version' => '6.11.1.0',
      'aliases' => 
      array (
      ),
      'reference' => 'd1e91ecf8c598d073d0995afa8cd5c75c6e19e66',
    ),
    'guzzlehttp/guzzle' => 
    array (
      'pretty_version' => '7.9.3',
      'version' => '7.9.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
    ),
    'guzzlehttp/promises' => 
    array (
      'pretty_version' => '2.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
    ),
    'guzzlehttp/psr7' => 
    array (
      'pretty_version' => '2.7.1',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
    ),
    'maennchen/zipstream-php' => 
    array (
      'pretty_version' => '3.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '9712d8fa4cdf9240380b01eb4be55ad8dcf71416',
    ),
    'markbaker/complex' => 
    array (
      'pretty_version' => '3.0.2',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
    ),
    'markbaker/matrix' => 
    array (
      'pretty_version' => '3.0.1',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
    ),
    'paragonie/constant_time_encoding' => 
    array (
      'pretty_version' => 'v3.0.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
    ),
    'paragonie/random_compat' => 
    array (
      'pretty_version' => 'v9.99.100',
      'version' => '**********',
      'aliases' => 
      array (
      ),
      'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
    ),
    'php-amqplib/php-amqplib' => 
    array (
      'pretty_version' => 'v3.7.3',
      'version' => '3.7.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '9f50fe69a9f1a19e2cb25596a354d705de36fe59',
    ),
    'phpoffice/phpspreadsheet' => 
    array (
      'pretty_version' => '4.4.0',
      'version' => '4.4.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '747ccd1b443e85e0cfc0d52acf50e4d15ef959eb',
    ),
    'phpseclib/phpseclib' => 
    array (
      'pretty_version' => '3.0.43',
      'version' => '3.0.43.0',
      'aliases' => 
      array (
      ),
      'reference' => '709ec107af3cb2f385b9617be72af8cf62441d02',
    ),
    'psr/http-client' => 
    array (
      'pretty_version' => '1.0.3',
      'version' => '1.0.3.0',
      'aliases' => 
      array (
      ),
      'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
    ),
    'psr/http-client-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
      ),
    ),
    'psr/http-factory' => 
    array (
      'pretty_version' => '1.1.0',
      'version' => '1.1.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
    ),
    'psr/http-factory-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
      ),
    ),
    'psr/http-message' => 
    array (
      'pretty_version' => '2.0',
      'version' => '2.0.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
    ),
    'psr/http-message-implementation' => 
    array (
      'provided' => 
      array (
        0 => '1.0',
      ),
    ),
    'psr/simple-cache' => 
    array (
      'pretty_version' => '3.0.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
    ),
    'ralouphie/getallheaders' => 
    array (
      'pretty_version' => '3.0.3',
      'version' => '3.0.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '120b605dfeb996808c31b6477290a714d356e822',
    ),
    'ratchet/rfc6455' => 
    array (
      'pretty_version' => 'v0.3.1',
      'version' => '0.3.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '7c964514e93456a52a99a20fcfa0de242a43ccdb',
    ),
    'react/cache' => 
    array (
      'pretty_version' => 'v1.2.0',
      'version' => '1.2.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'd47c472b64aa5608225f47965a484b75c7817d5b',
    ),
    'react/dns' => 
    array (
      'pretty_version' => 'v1.13.0',
      'version' => '1.13.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'eb8ae001b5a455665c89c1df97f6fb682f8fb0f5',
    ),
    'react/event-loop' => 
    array (
      'pretty_version' => 'v1.5.0',
      'version' => '1.5.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354',
    ),
    'react/promise' => 
    array (
      'pretty_version' => 'v3.2.0',
      'version' => '*******',
      'aliases' => 
      array (
      ),
      'reference' => '8a164643313c71354582dc850b42b33fa12a4b63',
    ),
    'react/socket' => 
    array (
      'pretty_version' => 'v1.16.0',
      'version' => '1.16.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1',
    ),
    'react/stream' => 
    array (
      'pretty_version' => 'v1.4.0',
      'version' => '1.4.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '1e5b0acb8fe55143b5b426817155190eb6f5b18d',
    ),
    'setasign/fpdi' => 
    array (
      'pretty_version' => 'v2.6.3',
      'version' => '2.6.3.0',
      'aliases' => 
      array (
      ),
      'reference' => '67c31f5e50c93c20579ca9e23035d8c540b51941',
    ),
    'symfony/deprecation-contracts' => 
    array (
      'pretty_version' => 'v3.5.1',
      'version' => '3.5.1.0',
      'aliases' => 
      array (
      ),
      'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
    ),
    'symfony/http-foundation' => 
    array (
      'pretty_version' => 'v6.4.21',
      'version' => '6.4.21.0',
      'aliases' => 
      array (
      ),
      'reference' => '3f0c7ea41db479383b81d436b836d37168fd5b99',
    ),
    'symfony/polyfill-mbstring' => 
    array (
      'pretty_version' => 'v1.32.0',
      'version' => '1.32.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
    ),
    'symfony/polyfill-php83' => 
    array (
      'pretty_version' => 'v1.32.0',
      'version' => '1.32.0.0',
      'aliases' => 
      array (
      ),
      'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
    ),
    'symfony/routing' => 
    array (
      'pretty_version' => 'v6.4.18',
      'version' => '6.4.18.0',
      'aliases' => 
      array (
      ),
      'reference' => 'e9bfc94953019089acdfb9be51c1b9142c4afa68',
    ),
    'tecnickcom/tcpdf' => 
    array (
      'pretty_version' => '6.10.0',
      'version' => '6.10.0.0',
      'aliases' => 
      array (
      ),
      'reference' => 'ca5b6de294512145db96bcbc94e61696599c391d',
    ),
    'videlalvaro/php-amqplib' => 
    array (
      'replaced' => 
      array (
        0 => 'v3.7.3',
      ),
    ),
  ),
);

<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海豚OZON店铺管理系统 - 专业跨境电商数据服ERP务</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css">
    <style>
        .btn-group.vertical {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 300px;
            margin-top: 30px;
        }

        .btn-group.vertical .btn {
            width: 100%;
            justify-content: center;
            padding: 18px 25px;
            font-size: 1.1em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-group.vertical .btn i {
            font-size: 1.2em;
            margin-right: 12px;
        }

        @media (min-width: 768px) {
            .btn-group.vertical {
                flex-direction: row;
                max-width: none;
                gap: 25px;
            }

            .btn-group.vertical .btn {
                width: auto;
                min-width: 220px;
                padding: 18px 35px;
            }
        }

        @media (max-width: 480px) {
            .btn-group.vertical .btn {
                font-size: 1em;
                padding: 16px 20px;
            }
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #3b82f6;
            --accent-color: #60a5fa;
            --success-color: #2563eb;
            --warning-color: #3b82f6;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --light-gray: #f1f5f9;
            --dark-gray: #334155;
            --text-color: #1e293b;
            --text-light: #64748b;
            --border-color: #e2e8f0;
            --shadow-light: 0 4px 6px -1px rgba(37, 99, 235, 0.1);
            --shadow-medium: 0 10px 15px -3px rgba(37, 99, 235, 0.15);
            --shadow-heavy: 0 25px 50px -12px rgba(37, 99, 235, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            color: #1e293b;
            line-height: 1.7;
            font-size: 16px;
            font-weight: 400;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            background-attachment: fixed;
            overflow-x: hidden;
        }

        /* 粒子背景动画 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 悬浮动画 */
        @keyframes hover {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 渐变文字动画 */
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .gradient-text {
            background: linear-gradient(-45deg, #2563eb, #3b82f6, #60a5fa, #93c5fd);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient 3s ease infinite;
        }

        /* 脉冲动画 */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
            100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
        }

        /* 价格表部分优化样式 */

        .pricing-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        }

        .comparison-header th, .comparison-row td {
            padding: 8px 12px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.9em;
            line-height: 1.3;
        }

        .comparison-header th {
            background: linear-gradient(45deg, #2c3e50, #34495e, #3d566e);
            color: white;
            font-size: 1.1em;
            font-weight: 700;
            position: sticky;
            top: 0;
            z-index: 10;
            padding: 15px 8px;
            line-height: 1.3;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .comparison-header th .plan-price {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            margin-top: 8px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 107, 107, 0.3);
            box-shadow: 0 4px 20px rgba(255, 107, 107, 0.2);
        }

        .comparison-header th:first-child {
            text-align: center;
            background: #f8fafc;
            color: var(--text-color);
            font-size: 1.2em;
        }

        .comparison-row:last-child td {
            border-bottom: none;
        }

        .feature-name {
            text-align: center;
            font-weight: 600;
            font-size: 0.95em;
            color: var(--text-color);
        }

        .feature-value {
            font-size: 1em;
        }

        .feature-value .fa-check-circle {
            color: #2ecc71;
        }

        .feature-value .fa-times-circle {
            color: #e74c3c;
        }

        .plan-price {
            font-size: 2.4em;
            font-weight: 900;
            color: #ff6b6b;
            margin-bottom: 8px;
            margin-top: 5px;
            line-height: 1.2;
            text-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
            z-index: 10;
            position: relative;
        }

        .plan-price span {
            font-size: 0.45em;
            color: #ffffff;
            font-weight: 700;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .comparison-row.highlight {
            background: #f0f5ff;
        }

        .comparison-row.highlight td:nth-child(3) {
            background: #e6f0ff;
            border-left: 3px solid var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        .pricing-section {
            padding: 60px 5%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            display: none;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .pricing-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            z-index: 1;
        }

        .pricing-container {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .pricing-header {
            text-align: center;
            margin-bottom: 35px;
            position: relative;
        }

        .pricing-title {
            font-size: 4em;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
        }

        .pricing-mascot {
            position: absolute;
            top: -20px;
            right: 8%;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .dolphin-icon {
            display: none;
        }

        .mascot-text {
            font-size: 1.4em;            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pricing-plans {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-bottom: 60px;
            perspective: 1000px;
        }

        .pricing-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 45px 35px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(20px);
            transform-style: preserve-3d;
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 30px 30px 0 0;
        }

        .pricing-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.8s ease;
        }

        .pricing-card:hover::after {
            left: 100%;
        }

        .pricing-card:hover {
            transform: translateY(-20px) rotateX(5deg);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.25);
            border-color: rgba(102, 126, 234, 0.5);
        }

        .pricing-card.featured {
            transform: scale(1.08) translateY(-10px);
            border-color: rgba(102, 126, 234, 0.6);
            box-shadow: 0 25px 70px rgba(102, 126, 234, 0.3);
        }

        .pricing-card.featured:hover {
            transform: scale(1.08) translateY(-25px) rotateX(5deg);
        }

       

        .plan-header {
            text-align: center;
            margin-bottom: 35px;
            padding-bottom: 25px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .plan-header h3 {
            font-size: 2em;
            font-weight: 800;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }

        .price {
            font-size: 3.8em;
            font-weight: 900;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            text-shadow: 0 2px 10px rgba(37, 99, 235, 0.3);
        }

        .price span {
            font-size: 0.35em;
            color: var(--text-light);
            font-weight: 600;
        }

        .plan-features {
            margin-bottom: 35px;
        }

        .feature {
            display: flex;
            align-items: center;
            padding: 15px 0;
            font-size: 1.05em;
            font-weight: 500;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .feature-available {
            color: var(--text-color);
        }

        .feature-missing {
            color: #ff5c5c;
            text-decoration: line-through;
            opacity: 0.6;
        }

        .feature:hover {
            background: rgba(102, 126, 234, 0.05);
            padding-left: 10px;
        }

        .feature:last-child {
            border-bottom: none;
        }

        .feature i {
            color: var(--primary-color);
            margin-right: 15px;
            width: 20px;
            font-size: 1.2em;
        }

        .feature-missing i {
            color: #ff5c5c;
        }

        .plan-button {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 0.95em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .plan-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.5);
            background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
        }

        .enterprise-custom {
            text-align: center;
            margin: 25px 0;
        }

        .enterprise-custom h2 {
            font-size: 2.2em;
            font-weight: 800;
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
            padding: 18px 35px;
            border-radius: 20px;
            display: inline-block;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .enterprise-custom p {
            font-size: 1.1em;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }

        /* 价格切换按钮样式 */
        .pricing-toggle {
            text-align: center;
            margin: 40px 0;
        }

        .toggle-container {
            display: inline-flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            padding: 6px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

                .toggle-btn {
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 600;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            min-width: 90px;
            flex: 1;
        }

        .toggle-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .toggle-btn.active {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
        }

        .discount-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .price-amount {
            transition: all 0.3s ease;
        }

        /* 原有样式保持不变... */
        /* 侧边导航栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 30px 0;
            box-shadow: 4px 0 20px rgba(37, 99, 235, 0.1);
            border-right: 1px solid rgba(37, 99, 235, 0.1);
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar:hover {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 6px 0 30px rgba(37, 99, 235, 0.15);
        }

        .sidebar-logo {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-shadow: none;
            transition: all 0.3s ease;
            padding: 0 30px 40px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 30px;
        }

        .sidebar-logo:hover {
            transform: scale(1.05);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
        }

        .sidebar-logo i {
            margin-right: 12px;
            color: var(--accent-color);
            font-size: 1.2em;
            animation: hover 2s ease-in-out infinite;
        }

        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 0 20px;
        }

        .sidebar-nav span {
            cursor: pointer;
            font-weight: 500;
            padding: 15px 20px;
            position: relative;
            transition: all 0.3s ease;
            color: var(--text-light);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            background: transparent;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            font-size: 16px;
        }

        .sidebar-nav span:hover {
            color: var(--primary-color);
            background: var(--light-gray);
            transform: translateX(8px);
            border-color: var(--border-color);
        }

        .sidebar-nav span.active {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        .sidebar-nav span a {
            color: inherit;
            text-decoration: none;
        }

        .sidebar-nav span i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        .sidebar-nav span::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 0;
            background: var(--accent-color);
            border-radius: 0 4px 4px 0;
            transition: height 0.3s ease;
        }

        .sidebar-nav span:hover::before,
        .sidebar-nav span.active::before {
            height: 60%;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* 顶部展示区 */
        .hero-section {
            background: var(--white);
            backdrop-filter: blur(20px);
            padding: 40px 60px 60px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            position: relative;
            overflow: hidden;
            min-height: 80vh;
            z-index: 2;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60%;
            height: 100%;
            background: linear-gradient(45deg, rgba(37, 99, 235, 0.05), rgba(96, 165, 250, 0.05));
            clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);
            z-index: -1;
            animation: float 8s ease-in-out infinite;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: 15%;
            right: 15%;
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, rgba(96, 165, 250, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: pulse 4s ease-in-out infinite;
            z-index: -1;
        }

        .hero-right {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .hero-card {
            background: var(--white);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-medium);
            transform: perspective(1000px) rotateY(-5deg);
            transition: all 0.3s ease;
        }

        .hero-card:hover {
            transform: perspective(1000px) rotateY(0deg) translateY(-10px);
            box-shadow: var(--shadow-heavy);
        }

        .hero-card h3 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
        }

        .hero-features {
            list-style: none;
            padding: 0;
        }

        .hero-features li {
            color: var(--text-color);
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
        }

        .hero-features li:last-child {
            border-bottom: none;
        }

        .hero-features li i {
            color: var(--accent-color);
            margin-right: 12px;
            width: 20px;
        }

        .left-panel {
            flex: 1;
            position: relative;
            z-index: 3;
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .left-panel h1 {
            font-size: 4em;
            margin-bottom: 35px;
            color: var(--text-color);
            line-height: 1.1;
            font-weight: 800;
            text-shadow: none;
            position: relative;
        }

        .left-panel h1::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 0;
            width: 120px;
            height: 5px;
            background: linear-gradient(45deg, var(--accent-color), var(--success-color));
            border-radius: 3px;
            animation: slideIn 1s ease-out 0.5s both;
        }

        @keyframes slideIn {
            from {
                width: 0;
            }
            to {
                width: 120px;
            }
        }

        .left-panel p {
            font-size: 1.4em;
            margin-bottom: 35px;
            max-width: 700px;
            color: var(--text-light);
            text-shadow: none;
            line-height: 1.8;
            font-weight: 400;
        }

        .hero-description {
            font-size: 1.2em;
            margin-bottom: 30px;
            max-width: 650px;
            color: var(--text-light);
            text-shadow: none;
            line-height: 1.7;
            font-weight: 300;
        }



        /* 功能模块区 */
        .features {
            padding: 100px 60px;
            background: var(--light-bg);
            backdrop-filter: blur(20px);
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(37, 99, 235, 0.02), rgba(96, 165, 250, 0.02));
            z-index: -1;
        }

        .section-title {
            font-size: 2.8em;
            color: var(--primary-color);
            margin-bottom: 25px;
            position: relative;
            display: inline-block;
            font-weight: 700;
            line-height: 1.2;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: var(--secondary-color);
            border-radius: 2px;
        }

        .section-subtitle {
            font-size: 1.3em;
            margin-bottom: 70px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            color: var(--text-color);
            line-height: 1.6;
            font-weight: 400;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .feature-card {
            border-radius: 20px;
            padding: 45px 35px;
            background: var(--white);
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-medium);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: left;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: var(--shadow-heavy);
            border-color: var(--accent-color);
        }

        .feature-card i {
            font-size: 3em;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            display: block;
            transition: transform 0.3s ease;
        }

        .feature-card:hover i {
            transform: scale(1.1) rotate(5deg);
        }

        .feature-card h3 {
            font-size: 1.7em;
            margin-bottom: 20px;
            color: var(--dark-gray);
            font-weight: 700;
            line-height: 1.3;
        }

        .feature-card p {
            font-size: 1.1em;
            line-height: 1.8;
            color: var(--text-color);
            opacity: 0.9;
            font-weight: 400;
        }

        /* 统计数据区 */
        .stats {
            padding: 80px 60px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--success-color) 100%);
            color: var(--white);
            text-align: center;
        }


        .stat-item {
            flex: 1;
            /* 等分剩余空间 */
            min-width: 200px;
            /* 最小宽度防止内容挤压 */
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            /* 半透明背景 */
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 3.2em;
            font-weight: 700;
            margin-bottom: 12px;
            color: var(--white);
            line-height: 1.1;
        }

        .stat-label {
            font-size: 1.3em;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 合作伙伴区 */
        .partners {
            padding: 100px 8%;
            text-align: center;
            background: var(--light-bg);
        }

        .partner-logos {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-top: 50px;
            flex-wrap: wrap;
            align-items: center;
        }

        .partner-logo {
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
            filter: grayscale(100%);
        }

        .partner-logo:hover {
            opacity: 1;
            filter: grayscale(0%);
        }

        /* 客户评价区 */
        .testimonials {
            padding: 100px 8%;
            background: var(--white);
            text-align: center;
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .testimonial-card {
            background: var(--light-bg);
            padding: 40px 30px;
            border-radius: 12px;
            text-align: left;
            position: relative;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: 20px;
            left: 30px;
            font-size: 5em;
            color: rgba(0, 0, 0, 0.05);
            font-family: serif;
            line-height: 1;
        }

        .testimonial-content {
            margin-bottom: 20px;
            font-style: italic;
            position: relative;
            z-index: 1;
            font-size: 1.1em;
            line-height: 1.7;
            color: var(--text-color);
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
        }

        .author-info h4 {
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .author-info p {
            font-size: 0.9em;
            opacity: 0.8;
        }

        /* 服务优势区 */
        .advantages {
            padding: 100px 8%;
            background: var(--white);
            text-align: center;
        }

        .advantage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .advantage-card {
            background: var(--light-bg);
            padding: 40px 30px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .advantage-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .advantage-card:hover::before {
            transform: scaleX(1);
        }

        .advantage-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-medium);
        }

        .advantage-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            transition: transform 0.3s ease;
        }

        .advantage-icon i {
            font-size: 2.2em;
            color: white;
        }

        .advantage-card:hover .advantage-icon {
            transform: scale(1.1) rotate(10deg);
        }

        .advantage-card h3 {
            font-size: 1.5em;
            margin-bottom: 18px;
            color: var(--dark-gray);
            font-weight: 600;
            line-height: 1.3;
        }

        .advantage-card p {
            color: var(--text-light);
            line-height: 1.8;
            font-size: 1.1em;
            font-weight: 400;
        }



        @media (max-width: 768px) {
            .advantage-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 底部CTA */
        .cta-section {
            padding: 100px 8%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            text-align: center;
        }

        .cta-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-content h2 {
            font-size: 2.8em;
            margin-bottom: 25px;
            font-weight: 700;
            line-height: 1.2;
        }

        .cta-content p {
            font-size: 1.3em;
            margin-bottom: 45px;
            line-height: 1.6;
            font-weight: 400;
        }

        /* 页脚 */
        footer {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: var(--white);
            padding: 60px 8% 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-bottom: 50px;
        }

        .footer-column h3 {
            color: var(--white);
            margin-bottom: 25px;
            font-size: 1.2em;
            position: relative;
            padding-bottom: 10px;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: var(--secondary-color);
        }

        .footer-column ul {
            list-style: none;
        }

        .footer-column ul li {
            margin-bottom: 15px;
        }

        .footer-column ul li a {
            color: #bbbbbb;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-column ul li a:hover {
            color: var(--secondary-color);
            padding-left: 5px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9em;
            color: #bbbbbb;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-links a {
            color: var(--white);
            background: rgba(255, 255, 255, 0.1);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background: var(--secondary-color);
            transform: translateY(-3px);
        }

        /* 按钮样式 */
        .btn {
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            backdrop-filter: blur(10px);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn i {
            margin-right: 10px;
            transition: transform 0.3s ease;
        }

        .btn:hover i {
            transform: scale(1.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            margin-right: 20px;
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(37, 99, 235, 0.5);
            animation: pulse 2s infinite;
        }

        .btn-secondary {
            background: linear-gradient(45deg, var(--accent-color), var(--success-color));
            color: var(--white);
            box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(96, 165, 250, 0.5);
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--accent-color);
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 255, 255, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 260px;
            }
            
            .sidebar {
                width: 260px;
            }
            
            .hero-section {
                padding: 30px 40px 50px;
                gap: 40px;
            }
        }

        @media (max-width: 992px) {
            .main-content {
                margin-left: 0;
            }
            
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .hero-section {
                grid-template-columns: 1fr;
                text-align: center;
                padding: 30px 30px 50px;
                gap: 40px;
            }

            .hero-section::before {
                display: none;
            }

            .left-panel {
                text-align: center;
            }

            .hero-right {
                order: -1;
            }

            .stats-container {
                display: flex;
                justify-content: space-between;
                gap: 30px;
                flex-wrap: wrap;
            }
            
            .features {
                padding: 80px 30px;
            }
            
            .stats {
                padding: 60px 30px;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 20px 20px 40px;
            }
            
            .features {
                padding: 60px 20px;
            }
            
            .stats {
                padding: 50px 20px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .pricing-section {
                padding: 60px 3%;
            }
            
            .pricing-title {
                font-size: 3em;
            }
            
            .pricing-mascot {
                position: static;
                margin: 20px auto 40px;
                right: auto;
            }
            
            .pricing-plans {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .pricing-card {
                padding: 35px 25px;
            }
            
            .pricing-card.featured {
                transform: scale(1.02);
            }
            
            .pricing-card.featured:hover {
                transform: translateY(-15px) scale(1.02);
            }
            
            .plan-header h3 {
                font-size: 1.6em;
            }
            
            .price {
                font-size: 3.2em;
            }
            
            .enterprise-custom h2 {
                font-size: 2.2em;
                padding: 20px 30px;
            }

            .btn {
                display: block;
                width: 100%;
                margin-bottom: 15px;
            }

            .btn-primary {
                margin-right: 0;
            }
            
            .hero-card {
                padding: 30px 20px;
                transform: none;
            }
            
            .hero-card:hover {
                transform: translateY(-5px);
            }
        }

        @media (max-width: 576px) {
            .stat-item {
                flex: 0 1 100%;
            }

            .section-title {
                font-size: 2em;
            }

            .left-panel h1 {
                font-size: 2.5em;
            }
            
            .pricing-section {
                padding: 40px 4%;
            }
            
            .pricing-title {
                font-size: 2.5em;
            }
            
            .pricing-mascot {
                padding: 20px;
                margin: 15px auto 30px;
            }
            
            .dolphin-icon {
                font-size: 3.5em;
            }
            
            .mascot-text {
                font-size: 1.2em;
            }
            
            .pricing-plans {
                gap: 20px;
            }
            
            .pricing-card {
                padding: 30px 20px;
            }
            
            .plan-header h3 {
                font-size: 1.4em;
            }
            
            .price {
                font-size: 2.8em;
            }
            
            .feature {
                padding: 10px 0;
                font-size: 0.95em;
            }
            
            .plan-button {
                padding: 15px 25px;
                font-size: 1em;
            }
            
            .enterprise-custom h2 {
                font-size: 2em;
                padding: 15px 25px;
            }
            
            .enterprise-custom p {
                font-size: 1.1em;
            }
        }
    </style>
</head>

<body>
    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>
    
    <!-- 侧边导航栏 -->
    <nav class="sidebar">
        <div class="sidebar-logo">
            <i class="fas fa-dolphin"></i>
            海豚OZON数据
        </div>
        <div class="sidebar-nav">
            <span class="active" onclick="showSection('home')">
                <i class="fas fa-home"></i>首页
            </span>
            <span onclick="showSection('pricing')">
                <i class="fas fa-tags"></i>价格
            </span>
            <span>
                <i class="fas fa-download"></i>
                <a href="./hterp.zip">插件下载</a>
            </span>

            <span onclick="window.open('https://v.douyin.com/qPzYAajJLF4/', '_blank')" style="cursor: pointer;">
                <i class="fas fa-question-circle"></i>帮助中心
            </span>
            <span>
                <i class="fas fa-envelope"></i>联系我们
            </span>
            <span onclick="window.location.href='./user/login.php'" style="cursor: pointer;">
                <i class="fas fa-user"></i>登录/注册
            </span>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 首页内容 -->
        <div id="home-content">
        <!-- 顶部展示区 -->
        <section class="hero-section">
            <div class="left-panel">
                <h1 class="gradient-text">专业OZON数据服务提供商</h1>
                <p>系统满足企业级运营需求，提供全方位选品、工资单、订单管理等服务</p>
                <div class="hero-description">
                    持续创新与维护，助力您成为最优秀的OZON跨境电商。我们专注于为中国卖家提供最专业的OZON平台数据分析和运营工具，帮助您在竞争激烈的跨境电商市场中脱颖而出。
                </div>
                <div class="btn-group vertical">
                    <a class="btn btn-primary" href="./user/">
                        <i class="fas fa-tachometer-alt"></i>进入控制台
                    </a>
                    <a class="btn btn-secondary" href="./user/">
                        <i class="fas fa-user-plus"></i>注册免费使用
                    </a>
                </div>
            </div>
            <div class="hero-right">
                <div class="hero-card">
                    <h3><i class="fas fa-chart-line"></i> 实时数据监控</h3>
                    <ul class="hero-features">
                        <li><i class="fas fa-check"></i>24小时实时数据更新</li>
                        <li><i class="fas fa-check"></i>多维度销售分析</li>
                        <li><i class="fas fa-check"></i>智能选品推荐</li>
                        <li><i class="fas fa-check"></i>竞品监控预警</li>
                        <li><i class="fas fa-check"></i>价格趋势分析</li>
                        <li><i class="fas fa-check"></i>库存智能管理</li>
                    </ul>
                </div>
            </div>
        </section>









        <!-- 功能模块区 -->
        <section class="features">
            <h2 class="section-title">高效，便捷的OZON店铺管理系统</h2>
            <p class="section-subtitle">为新手OZON卖家提供低门槛选品上货工具，降低跨境学习成本，缩短出单空窗期，让您的跨境电商之路更加顺畅</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>选品分析</h3>
                    <p>使用多维销售参数筛选热门商品，精准把握市场趋势<br>
                        选品插件一键展示OZON平台热销商品数据<br>
                        24小时实时数据更新，确保决策依据最新市场动态</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-boxes"></i>
                    <h3>商品管理</h3>
                    <p>批量上货功能，极大提升工作效率<br>
                        智能定价计算器，自动计算最优价格区间<br>
                        多店铺矩阵运营支持，统一管理多个店铺</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>订单管理</h3>
                    <p>全流程物流状态跟踪提醒，随时掌握订单动态<br>
                        智能备货管理系统，自动计算补货需求<br>
                        专业扫码设备对接，快速处理大批量订单</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-truck"></i>
                    <h3>物流管理</h3>
                    <p>完整货代服务对接，一站式解决物流难题<br>
                        精确物流账单信息，清晰掌握每笔费用<br>
                        专业仓储贴标验货服务，确保商品符合平台要求</p>
                </div>
            </div>
        </section>

        <!-- 统计数据区 -->
        <section class="stats">
            <h2 class="section-title">我们的成就</h2>
            <p class="section-subtitle">海豚OZON数据服务已帮助数千家跨境电商企业实现业绩增长</p>


            <span class="stat-number">5,000+</span>
            <span class="stat-label">注册商家 </span>

            <span class="stat-number">95%</span>
            <span class="stat-label">客户满意度 </span>

            <span class="stat-number">24/7</span>
            <span class="stat-label">技术支持 </span>


            <span class="stat-number">30+</span>
            <span class="stat-label">合作物流商 </span>


        </section>

        <!-- 合作伙伴区 -->
        <section class="partners">
            <h2 class="section-title">合作伙伴</h2>
            <p class="section-subtitle">我们与行业领先企业建立战略合作，为您提供更全面的服务</p>

            <div class="partner-logos">
                <img src="https://via.placeholder.com/150x60?text=OZON" class="partner-logo" alt="OZON">
                <img src="https://via.placeholder.com/150x60?text=国欧物流" class="partner-logo" alt="国欧物流">
                <img src="https://via.placeholder.com/150x60?text=连连国际" class="partner-logo" alt="连连国际">
                <img src="https://via.placeholder.com/150x60?text=邮宝" class="partner-logo" alt="邮宝">
                <img src="https://via.placeholder.com/150x60?text=Global+E-Pay" class="partner-logo"
                    alt="Global E-Payment">
            </div>
        </section>

        <!-- 客户评价区 -->
        <section class="testimonials">
            <h2 class="section-title">客户评价</h2>
            <p class="section-subtitle">听听我们的客户怎么说</p>

            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "使用海豚OZON系统后，我们的选品效率提升了300%，系统提供的数据非常精准，帮助我们避免了多个选品错误。"
                    </div>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" class="author-avatar" alt="客户头像">
                        <div class="author-info">
                            <h4>XX</h4>
                            <p>X电商 运营总</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "物流管理模块大大简化了我们的工作流程，现在可以实时跟踪所有订单状态，再也不用担心物流延误问题了。"
                    </div>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" class="author-avatar" alt="客户头像">
                        <div class="author-info">
                            <h4>XXX</h4>
                            <p>某在职宝妈</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "作为一个刚进入OZON平台的新手，海豚系统的培训资料和客服支持让我快速上手，现在店铺月销售额已突破5万美元。"
                    </div>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/men/75.jpg" class="author-avatar" alt="客户头像">
                        <div class="author-info">
                            <h4>XXX</h4>
                            <p>XXX贸易 创始人</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务优势区 -->
        <section class="advantages">
            <h2 class="section-title">为什么选择海豚OZON数据服务</h2>
            <p class="section-subtitle">专业团队打造，为您的跨境电商之路保驾护航</p>
            
            <div class="advantage-grid">
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>数据安全保障</h3>
                    <p>采用银行级加密技术，确保您的商业数据绝对安全。多重备份机制，永不丢失重要信息。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>快速响应</h3>
                    <p>7×24小时技术支持，平均响应时间不超过30分钟。专业客服团队随时为您解答疑问。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>AI智能分析</h3>
                    <p>基于机器学习的智能选品算法，精准预测市场趋势，帮您抢占先机。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>专业团队</h3>
                    <p>拥有5年以上跨境电商经验的专业团队，深度了解OZON平台运营规则。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>数据精准</h3>
                    <p>与OZON官方数据同步，确保数据准确性达99.9%以上，为您的决策提供可靠依据。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>持续更新</h3>
                    <p>根据平台政策变化及时更新功能，确保系统始终与OZON平台保持同步。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>精准选品分析</h3>
                    <p>基于大数据算法的智能选品工具，多维度分析商品潜力，帮您发现下一个爆款。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3>竞品监控</h3>
                    <p>实时监控竞争对手价格变动、库存状态，让您始终掌握市场动态，抢占先机。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3>批量操作</h3>
                    <p>一键批量上货、价格调整、库存管理，大幅提升运营效率，节省宝贵时间。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3>数据报表</h3>
                    <p>详细的销售数据分析报表，可视化展示店铺运营状况，为决策提供科学依据。</p>
                </div>
            </div>
        </section>

        <!-- 底部CTA -->
        <section class="cta-section">
            <div class="cta-content">
                <h2>立即开始您的OZON跨境电商之旅</h2>
                <p>注册即可获得7天免费试用，体验专业数据服务带来的改变</p>
                <button class="btn btn-outline" onclick="window.open('https://v.douyin.com/qPzYAajJLF4/', '_blank')"><i class="fas fa-play"></i>观看演示视频</button>
                <button class="btn btn-secondary" onclick="window.location.href='user/'"><i class="fas fa-rocket"></i>立即注册</button>
            </div>
        </section>
    </div>

    <!-- 价格表部分 -->
    <section id="pricing-section" class="pricing-section">
        <div class="pricing-container">
            <div class="pricing-header">
                <h1 class="pricing-title">海豚ERP价格表</h1>
                
                <!-- 价格周期切换按钮 -->
                <div class="pricing-toggle">
                    <div class="toggle-container">
                        <button class="toggle-btn" data-period="weekly" onclick="switchPricing('weekly')">按周付费</button>
                        <button class="toggle-btn active" data-period="monthly" onclick="switchPricing('monthly')">按月付费</button>
                        <button class="toggle-btn" data-period="quarterly" onclick="switchPricing('quarterly')">按季付费</button>
                        <button class="toggle-btn" data-period="yearly" onclick="switchPricing('yearly')">按年付费</button>
                    </div>
                    <div class="discount-badge" id="discount-badge">周卡体验价，季付享9折优惠，年付享8折优惠</div>
                </div>

            </div>

            <table class="pricing-comparison-table">
                <thead class="comparison-header">
                    <tr>
                        <th>功能</th>
                        <th>
                            <div>个人版</div>
                            <div class="plan-price">
                                <span class="price-amount" data-weekly="188" data-monthly="388" data-quarterly="1050" data-yearly="3720">388</span>¥
                                <span class="price-period">/月</span>
                            </div>
                        </th>
                        <th>
                           
                            <div>团队版(推荐)</div>
                            <div class="plan-price">
                                <span class="price-amount" data-weekly="588" data-monthly="688" data-quarterly="1860" data-yearly="6600">688</span>¥
                                <span class="price-period">/月</span>
                            </div>
                        </th>
                        <th>
                            <div>企业版</div>
                            <div class="plan-price">
                                <span class="price-amount" data-weekly="688" data-monthly="888" data-quarterly="2999" data-yearly="12888">888</span>¥
                                <span class="price-period">/月</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="comparison-row">
                        <td class="feature-name">店铺数量</td>
                        <td class="feature-value">5个</td>
                        <td class="feature-value">150个</td>
                        <td class="feature-value">不限制</td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">跟卖数量</td>
                        <td class="feature-value">500/天</td>
                        <td class="feature-value">不限制</td>
                        <td class="feature-value">不限制</td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">采集数量</td>
                        <td class="feature-value">150/天</td>
                        <td class="feature-value">不限制</td>
                        <td class="feature-value">不限制</td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">同时在线设备</td>
                        <td class="feature-value">1台</td>
                        <td class="feature-value">15台</td>
                        <td class="feature-value">30台</td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">店铺监控</td>
                        <td class="feature-value">5个</td>
                        <td class="feature-value">50个</td>
                        <td class="feature-value">200个</td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">销量数据插件</td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">利润计算</td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">热销榜单</td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">关键词/反查关键词</td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">中国区选品</td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">类目分析</td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">订单/商品管理系统</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">打单管理系统</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">黑名单数据库</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">下架重上</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">定时批量上品任务</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">企业定制库存管理系统</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">年卡定制企业专属手机扫码打单贴单</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">AI项目 待开发</td>
                        <td class="feature-value"><i class="fas fa-clock"></i></td>
                        <td class="feature-value"><i class="fas fa-clock"></i></td>
                        <td class="feature-value"><i class="fas fa-clock"></i></td>
                    </tr>
                    <tr class="comparison-row">
                        <td class="feature-name">聊天功能</td>
                        <td class="feature-value"><i class="fas fa-times-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                        <td class="feature-value"><i class="fas fa-check-circle"></i></td>
                    </tr>

                </tbody>
            </table>

            <div class="enterprise-custom">
                <h2>企业定制部署</h2>
                <p>联系我们获取专属解决方案</p>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="btn btn-primary" onclick="showSection('home')"><i class="fas fa-arrow-left"></i>
                    返回首页</button>
            </div>
        </div>
    </section>
        </div>
    </div>

    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <h3>关于我们</h3>
                <ul>
                    <li><a href="#">公司简介</a></li>
                    <li><a href="#">发展历程</a></li>
                    <li><a href="#">团队介绍</a></li>
                    <li><a href="#">加入我们</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>产品服务</h3>
                <ul>
                    <li><a href="#">选品分析</a></li>
                    <li><a href="#">商品管理</a></li>
                    <li><a href="#">订单管理</a></li>
                    <li><a href="#">物流服务</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3 onclick="window.open('https://v.douyin.com/qPzYAajJLF4/', '_blank')" style="cursor: pointer;">帮助中心</h3>
                <ul>
                    <li><a href="#">常见问题</a></li>
                    <li><a href="#">使用教程</a></li>
                    <li><a href="#">API文档</a></li>
                    <li><a href="#">联系我们</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>联系我们</h3>
                <ul>
                    <li><i class="fas fa-phone-alt"></i> ************</li>
                    <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    <li><i class="fas fa-map-marker-alt"></i> 广东东莞</li>
                </ul>
                <div class="social-links">
                    <a href="#"><i class="fab fa-weixin"></i></a>
                    <a href="#"><i class="fab fa-weibo"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p><a href="https://beian.miit.gov.cn/">© 2025 极耀海豚ERP 版权所有 | 粤ICP备2025399873号-1</a> </p>
        </div>
    </footer>

    <script>
        // 创建粒子背景
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机大小和位置
                const size = Math.random() * 4 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                
                // 随机动画延迟
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                
                particlesContainer.appendChild(particle);
            }
        }
        
        // 平滑滚动效果
        function smoothScroll() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }
        
        // 侧边栏导航激活状态
        function updateSidebarActive(activeSection) {
            const navItems = document.querySelectorAll('.sidebar-nav span');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            
            if (activeSection === 'home') {
                navItems[0].classList.add('active');
            } else if (activeSection === 'pricing') {
                navItems[1].classList.add('active');
            }
        }
        
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }
        
        // 元素进入视口时的动画
        function observeElements() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            // 观察所有功能卡片
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        }
        
        // 默认显示首页内容
        document.addEventListener('DOMContentLoaded', function () {
            showSection('home');
            createParticles();
            smoothScroll();
            observeElements();
        });

        // 显示不同部分的函数
        function showSection(sectionId) {
            // 隐藏所有主要内容区域
            document.getElementById('home-content').style.display = 'none';
            document.getElementById('pricing-section').style.display = 'none';

            // 显示选中的部分
            if (sectionId === 'home') {
                document.getElementById('home-content').style.display = 'block';
                window.scrollTo(0, 0);
            } else if (sectionId === 'pricing') {
                document.getElementById('pricing-section').style.display = 'block';
                // 滚动到价格表部分
                document.getElementById('pricing-section').scrollIntoView({ behavior: 'smooth' });
            }
            
            // 更新侧边栏激活状态
            updateSidebarActive(sectionId);
        }

        // 价格切换功能
        function switchPricing(period) {
            // 更新按钮状态
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-period="${period}"]`).classList.add('active');

            // 更新价格显示
            document.querySelectorAll('.price-amount').forEach(priceElement => {
                const newPrice = priceElement.getAttribute(`data-${period}`);
                priceElement.textContent = newPrice;
            });

            // 更新价格周期文本
            let periodText = '';
            let discountText = '';
            
            switch(period) {
                case 'weekly':
                    periodText = '/周';
                    discountText = '周卡体验价，快速试用我们的服务';
                    break;
                case 'monthly':
                    periodText = '/月';
                    discountText = '按需付费，灵活选择';
                    break;
                case 'quarterly':
                    periodText = '/季';
                    discountText = '季付享9折优惠，节省更多';
                    break;
                case 'yearly':
                    periodText = '/年';
                    discountText = '年付享8折优惠，最划算选择';
                    break;
            }
            
            document.querySelectorAll('.price-period').forEach(periodElement => {
                periodElement.textContent = periodText;
            });
            
            // 更新优惠提示
            document.getElementById('discount-badge').textContent = discountText;
        }
    </script>
</body
<?php
include("../includes/common.php");

$act = isset($_GET['act']) ? daddslashes($_GET['act']) : (isset($_POST['act']) ? daddslashes($_POST['act']) : null);

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
@header('Content-Type: application/json; charset=UTF-8');

// 检查登录状态（使用与ajax.php相同的逻辑）
if ($islogin2 == 1) {
    // 用户已登录，$uid 和 $userrow 已经在 member.php 中设置
    $uid = $userrow['uid'] ?? 0;
} else {
    exit('{"code":-3,"msg":"No Login"}');
}

require_once '../includes/lib/OzonApiClient.php';

// 对于getChatList，我们不需要单一店铺配置，因为要获取所有店铺的聊天
// 其他操作仍需要指定店铺
$storeId = $_REQUEST['store_id'] ?? null;
$storeConfig = null;
$ozonClient = null;

// 只有在需要特定店铺操作时才获取配置
if ($act !== 'getChatList' && $storeId) {
    $storeConfig = $DB->getRow(
        "SELECT id, ClientId, `key`, apistatus, storename FROM ozon_store WHERE id=:store_id AND uid=:uid LIMIT 1",
        [':store_id' => $storeId, ':uid' => $uid]
    );
    
    if ($storeConfig) {
        $ozonClient = new \lib\OzonApiClient($storeConfig['ClientId'], $storeConfig['key']);
    }
}

switch ($act) {
    case 'getstore':
        // 简化版店铺获取，避免API调用超时
        try {
            $list = $DB->getAll("SELECT id, storename, ClientId, apistatus FROM ozon_store WHERE uid={$uid}");
            $list2 = [];
            foreach($list as $row){
                $list2[] = [
                    'id' => $row['id'],
                    'storename' => $row['storename'],
                    'ClientId' => $row['ClientId'],
                    'apistatus' => $row['apistatus'],
                    'warehouses' => [] // 暂时返回空数组，避免API调用
                ];
            }
            exit(json_encode(['code'=>0,'stores'=>$list2,'msg'=>'获取成功(简化版)']));
        } catch (Exception $e) {
            exit(json_encode(['code'=>-1,'msg'=>'获取店铺失败: ' . $e->getMessage()]));
        }
        break;
        
    case 'getShopGroups':
        // 直接使用ajax.php中的getShopGroups逻辑
        if (empty($uid)) {
            exit(json_encode(['code' => -1, 'msg' => '未登录']));
        }
        $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
        $groups = [];
        if ($user && $user['shop_groups']) {
            $shopGroups = json_decode($user['shop_groups'], true);
            $groups = $shopGroups['groups'] ?? [];
        }
        exit(json_encode(['code' => 0, 'data' => $groups]));
        break;
        
    case 'getChatList':
        // 添加错误处理避免502
        try {
            getChatList();
        } catch (Exception $e) {
            // 获取聊天列表失败
            exit(json_encode([
                'code' => -1, 
                'msg' => '获取聊天列表失败: ' . $e->getMessage(),
                'data' => [],
                'stores' => [],
                'total_chats' => 0,
                'total_stores' => 0
            ]));
        }
        break;
        
    case 'getChatHistory':
        getChatHistory();
        break;
        
    case 'sendMessage':
        sendMessage($ozonClient, $storeConfig);
        break;
        
    case 'sendFile':
        sendFile($ozonClient, $storeConfig);
        break;
        
    case 'markRead':
        markRead($ozonClient, $storeConfig);
        break;
        
    case 'getUpdates':
        getUpdates($ozonClient, $storeConfig);
        break;
        
    case 'getOrderInfo':
        getOrderInfo($ozonClient, $storeConfig);
        break;
        
    // 调试功能已移除
        
    case 'getCachedOrderInfo':
        getChatCachedOrderInfo();
        break;
        
    default:
        exit('{"code":-1,"msg":"无效的操作"}');
}



/**
 * 获取聊天列表 - 优化版：先加载店铺列表，按需加载聊天
 */
function getChatList() {
    global $DB, $uid;
    
    try {
        $selectedStoreId = $_GET['store_id'] ?? null;
        $chatTypeFilter = $_GET['chat_type'] ?? 'Buyer_Seller'; // 默认只显示买家咨询
        $chatStatusFilter = $_GET['chat_status'] ?? null; // 未读/已读筛选
        $timeFilter = $_GET['time_filter'] ?? null; // 时间筛选
        $startDate = $_GET['start_date'] ?? null; // 开始日期
        $endDate = $_GET['end_date'] ?? null; // 结束日期
        $loadFromApi = $_GET['load_from_api'] ?? false; // 是否从API加载
        
        // 获取用户的所有店铺
        $allStores = $DB->getAll("SELECT id, storename, ClientId, `key`, apistatus FROM ozon_store WHERE uid=:uid", [':uid' => $uid]);
        
        $storeInfoList = [];
        $allChats = [];
        $apiErrors = [];
        
        // 检查每个聊天是否可以发送消息
        if ($allChats) {
            foreach ($allChats as &$chat) {
                // 默认可发送，除非有明确的禁用标记
                $chat['can_send_message'] = true;
            }
        }

        // 如果没选择特定类型或选择了买家咨询，从数据库加载买家咨询
        if (!$chatTypeFilter || $chatTypeFilter === 'Buyer_Seller') {
            $whereConditions = ["uid = :uid", "chat_type = 'Buyer_Seller'"];
            $params = [':uid' => $uid];
            
            // 如果指定了店铺，添加店铺筛选
            if ($selectedStoreId) {
                $whereConditions[] = "store_id = :store_id";
                $params[':store_id'] = $selectedStoreId;
            }
            
            $sql = "SELECT c.*, s.storename FROM ozon_chats c 
                    LEFT JOIN ozon_store s ON c.store_id = s.id 
                    WHERE " . implode(' AND ', $whereConditions) . "
                    ORDER BY c.updated_at DESC 
                    LIMIT 300";
            
            $cachedChats = $DB->getAll($sql, $params);
            
            foreach ($cachedChats as $chat) {
                $chatItem = [
                    'chat_id' => $chat['chat_id'],
                    'title' => $chat['title'] ?: '买家咨询',
                    'last_message' => '点击查看消息详情',
                    'last_message_time' => $chat['updated_at'],
                    'unread_count' => 0,
                    'status' => $chat['status'] ?: 'Active',
                    'chat_type' => $chat['chat_type'],
                    'store_id' => $chat['store_id'],
                    'store_title' => $chat['storename'] ?: ('店铺 ' . $chat['store_id']),
                    'store_client_id' => '',
                    'order_number' => $chat['order_number'],
                    'sku' => $chat['sku'],
                    'product_name' => $chat['product_name'],
                    'source' => 'database'
                ];
                
                // 应用筛选条件
                if (applyChatFilters($chatItem, $chatTypeFilter, $chatStatusFilter, $timeFilter, $startDate, $endDate)) {
                    $allChats[] = $chatItem;
                }
            }
            
            // 从数据库加载买家咨询
        }
        
        // 判断是否需要API调用的条件：
        // 1. 强制从API刷新
        // 2. 选择了非买家咨询的类型
        // 3. 没有选择类型（显示所有类型，需要API补充其他类型）
        $needApiCall = $loadFromApi || ($chatTypeFilter && $chatTypeFilter !== 'Buyer_Seller') || !$chatTypeFilter;
        
        // 构建店铺信息列表
        foreach ($allStores as $store) {
            $storeInfoList[] = [
                'store_id' => $store['id'],
                'store_title' => $store['storename'] ?: ('店铺 ' . $store['ClientId']),
                'store_client_id' => $store['ClientId'],
                'chat_count' => 0,
                'api_status' => $store['apistatus'] ? ($store['key'] ? '点击加载' : 'API密钥缺失') : 'API未启用'
            ];
        }
        
        // 如果需要API数据（刷新或非买家咨询类型），且指定了店铺
        if ($needApiCall && $selectedStoreId) {
            $selectedStore = array_filter($allStores, function($s) use ($selectedStoreId) {
                return $s['id'] == $selectedStoreId;
            });
            
            if (!empty($selectedStore)) {
                $store = array_values($selectedStore)[0];
                
                if ($store['apistatus'] && !empty($store['key'])) {
                    try {
                        require_once '../includes/lib/OzonApiClient.php';
                        $ozonClient = new \lib\OzonApiClient($store['ClientId'], $store['key']);
                        
                        // 支持分页参数
                        $limit = intval($_GET['limit'] ?? 400); // 每页数量，默认50
                        $offset = intval($_GET['offset'] ?? 0); // 偏移量，默认0
                        $response = $ozonClient->getChatList($limit, $offset);
                        
                        // API响应处理
                        
                        // 检查API错误
                        if (is_array($response) && isset($response['error'])) {
                            throw new Exception($response['error']);
                        }
                        
                        if ($response && is_array($response)) {
                            $apiChats = [];
                            
                            // 根据调试结果，聊天历史返回标准格式，但聊天列表可能不同
                            // 检查各种可能的聊天数据位置
                            if (isset($response['chats']) && is_array($response['chats'])) {
                                $apiChats = $response['chats'];
                            } elseif (isset($response['result']['chats']) && is_array($response['result']['chats'])) {
                                $apiChats = $response['result']['chats'];
                            } elseif (isset($response['result']) && is_array($response['result']) && !empty($response['result'])) {
                                // 检查result是否直接包含聊天数组
                                $firstItem = reset($response['result']);
                                if (is_array($firstItem) && isset($firstItem['chat_id'])) {
                                    $apiChats = $response['result'];
                                }
                            } elseif (isset($response['data']) && is_array($response['data'])) {
                                // 可能的替代格式
                                $apiChats = $response['data'];
                            }
                            
                            // 如果上述都没找到，但响应包含chat_id字段，可能响应本身就是聊天数组
                            if (empty($apiChats) && isset($response[0]['chat_id'])) {
                                $apiChats = $response;
                            }
                            
                            // 备用聊天列表 - 当聊天列表API不可用时使用已知聊天ID
                            if (empty($apiChats)) {
                                $knownChatIds = ['a0f716c5-9601-4866-9863-d119db5ccdee'];
                                
                                foreach ($knownChatIds as $chatId) {
                                    try {
                                        $chatHistory = $ozonClient->getChatHistory($chatId, 1, 0);
                                        if (isset($chatHistory['messages']) && !empty($chatHistory['messages'])) {
                                            $lastMessage = $chatHistory['messages'][0];
                                            
                                            // 提取订单信息
                                            $orderInfo = [
                                                'order_number' => '',
                                                'sku' => '',
                                                'product_name' => ''
                                            ];
                                            
                                            if (isset($lastMessage['context'])) {
                                                $orderInfo['order_number'] = $lastMessage['context']['order_number'] ?? '';
                                                $orderInfo['sku'] = $lastMessage['context']['sku'] ?? '';
                                            }
                                            
                                            if (isset($lastMessage['data']) && is_array($lastMessage['data'])) {
                                                $messageText = implode(' ', $lastMessage['data']);
                                                if (preg_match('/"([^"]*)"/', $messageText, $matches)) {
                                                    $orderInfo['product_name'] = $matches[1];
                                                }
                                            }
                                            
                                            // 构建聊天项
                                            $chatItem = [
                                                'chat_id' => $chatId,
                                                'title' => '系统通知聊天',
                                                'last_message' => !empty($lastMessage['data'][0]) ? 
                                                    mb_substr(strip_tags($lastMessage['data'][0]), 0, 100) . '...' : 
                                                    '点击查看消息',
                                                'last_message_time' => $lastMessage['created_at'] ?? date('Y-m-d H:i:s'),
                                                'unread_count' => $lastMessage['is_read'] ? 0 : 1,
                                                'status' => 'Active',
                                                'chat_type' => 'Seller_Notification',
                                                'store_id' => $store['id'],
                                                'store_title' => $store['storename'],
                                                'store_client_id' => $store['ClientId'],
                                                'order_number' => $orderInfo['order_number'],
                                                'sku' => $orderInfo['sku'],
                                                'product_name' => $orderInfo['product_name']
                                            ];
                                            
                                            // 应用类型筛选
                                            if (!$chatTypeFilter || $chatItem['chat_type'] === $chatTypeFilter) {
                                                $allChats[] = $chatItem;
                                            }
                                        }
                                    } catch (Exception $e) {
                                        // 备用聊天提取失败，跳过
                                    }
                                }
                                
                                // 更新对应店铺的聊天数量
                                foreach ($storeInfoList as &$storeInfo) {
                                    if ($storeInfo['store_id'] == $selectedStoreId) {
                                        $storeInfo['chat_count'] = count($allChats);
                                        $storeInfo['api_status'] = '已加载 (备用模式: ' . count($allChats) . '个聊天)';
                                        break;
                                    }
                                }
                            }

                            if (!empty($apiChats)) {
                                foreach ($apiChats as $chat) {
                                    if (isset($chat['chat_id'])) {
                                        // 旧的前端筛选已移到applyChatFilters函数
                                        // 获取最后消息 - 根据Ozon API的实际字段结构
                                        $lastMessage = '点击查看消息'; // 默认提示
                                        
                                        // 按优先级检查可能的消息字段
                                        if (isset($chat['last_message']['text']) && !empty($chat['last_message']['text'])) {
                                            $lastMessage = $chat['last_message']['text'];
                                        } elseif (isset($chat['last_message']['content']) && !empty($chat['last_message']['content'])) {
                                            $lastMessage = $chat['last_message']['content'];
                                        } elseif (!empty($chat['last_message_text'])) {
                                            $lastMessage = $chat['last_message_text'];
                                        } elseif (!empty($chat['preview_text'])) {
                                            $lastMessage = $chat['preview_text'];
                                        } elseif (!empty($chat['message_preview'])) {
                                            $lastMessage = $chat['message_preview'];
                                        } elseif (is_string($chat['last_message']) && !empty($chat['last_message'])) {
                                            $lastMessage = $chat['last_message'];
                                        }
                                        
                                        // 处理聊天数据
                                        
                                        // 第一优先级：从缓存中获取订单信息
                                        $orderInfo = getCachedOrderInfoForChat($chat['chat_id'], $store['id'], $uid);
                                        
                                        // 第二优先级：从聊天列表响应中直接提取订单信息
                                        if (!$orderInfo || (empty($orderInfo['order_number']) && empty($orderInfo['sku']) && empty($orderInfo['product_name']))) {
                                            $orderInfo = extractOrderInfoFromChatListResponse($chat);
                                        }
                                        
                                        // 第三优先级：使用轻量级文本提取
                                        if (empty($orderInfo['order_number']) && empty($orderInfo['sku']) && empty($orderInfo['product_name'])) {
                                            $orderInfo = extractOrderInfoFromChat($chat);
                                        }
                                        
                                        $chatItem = [
                                            'chat_id' => $chat['chat_id'],
                                            'title' => getChatTitle($chat),
                                            'last_message' => $lastMessage,
                                            'last_message_time' => $chat['created_at'] ?? $chat['updated_at'] ?? $chat['timestamp'] ?? date('Y-m-d H:i:s'),
                                            'unread_count' => intval($chat['unread_count'] ?? $chat['unread'] ?? 0),
                                            'status' => $chat['chat_status'] ?? $chat['status'] ?? 'Active',
                                            'chat_type' => $chat['chat_type'] ?? $chat['type'] ?? 'Unknown',
                                            'store_id' => $store['id'],
                                            'store_title' => $store['storename'] ?: ('店铺 ' . $store['ClientId']),
                                            'store_client_id' => $store['ClientId'],
                                            'order_number' => $orderInfo['order_number'] ?? '',
                                            'sku' => $orderInfo['sku'] ?? '',
                                            'product_name' => $orderInfo['product_name'] ?? '',
                                            // 订单信息提取完成
                                            'source' => 'api'
                                        ];
                                        
                                        // 买家咨询且有订单号的才缓存到数据库
                                        if ($chatItem['chat_type'] === 'Buyer_Seller' && !empty($chatItem['order_number'])) {
                                            cacheBuyerChatToDatabase($chatItem, $uid);
                                        }
                                        
                                        // 应用筛选条件并避免重复
                                        if (applyChatFilters($chatItem, $chatTypeFilter, $chatStatusFilter, $timeFilter, $startDate, $endDate)) {
                                            // 检查是否已存在相同的聊天（来自数据库）
                                            $exists = false;
                                            foreach ($allChats as $existingChat) {
                                                if ($existingChat['chat_id'] === $chatItem['chat_id'] && 
                                                    $existingChat['store_id'] === $chatItem['store_id']) {
                                                    $exists = true;
                                                    break;
                                                }
                                            }
                                            
                                            if (!$exists) {
                                                $allChats[] = $chatItem;
                                            }
                                        }
                                    }
                                }
                                
                                // 按最后消息时间排序聊天列表（最新的聊天在上面）
                                usort($allChats, function($a, $b) {
                                    return strtotime($b['last_message_time']) - strtotime($a['last_message_time']);
                                });
                                
                                // 更新对应店铺的聊天数量
                                foreach ($storeInfoList as &$storeInfo) {
                                    if ($storeInfo['store_id'] == $selectedStoreId) {
                                        $storeInfo['chat_count'] = count($apiChats);
                                        $storeInfo['api_status'] = '已加载 (' . count($apiChats) . '个聊天)';
                                        break;
                                    }
                                }
                            }
                        }
                        
                    } catch (Exception $e) {
                        $errorMsg = $e->getMessage();
                        // API调用失败
                        $apiErrors[] = "店铺 {$store['storename']}: " . $errorMsg;
                        
                        // 更新店铺状态
                        foreach ($storeInfoList as &$storeInfo) {
                            if ($storeInfo['store_id'] == $selectedStoreId) {
                                $storeInfo['api_status'] = 'API调用失败: ' . $errorMsg;
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        // 生成结果消息
        $totalChats = count($allChats);
        $enabledStores = count(array_filter($allStores, function($s) { return $s['apistatus']; }));
        
        // 统计数据来源
        $dbChats = count(array_filter($allChats, function($chat) { return $chat['source'] === 'database'; }));
        $apiChats = count(array_filter($allChats, function($chat) { return $chat['source'] === 'api'; }));
        
        // 生成筛选类型的中文描述
        $typeText = '';
        if ($chatTypeFilter) {
            $typeMapping = [
                'Buyer_Seller' => '买家咨询',
                'Seller_Support' => '客服支持',
                'Seller_Notification' => '系统通知',
                'Seller_Notification_FBS' => 'FBS通知',
                'Seller_Notification_Major' => '重要通知',
                'Seller_Marketplace_Promo' => '平台推广',
                'Seller_Notification_Findoc' => '财务文档',
                'Seller_Notification_UpdateContent' => '内容更新',
                'Seller_Notification_Content' => '内容通知',
                'Seller_Paid_Brands' => '品牌推广',
                'Seller_Online_Training' => '在线培训'
            ];
            $typeText = $typeMapping[$chatTypeFilter] ?? $chatTypeFilter;
        }
        
        if ($totalChats > 0) {
            $sourceParts = [];
            if ($dbChats > 0) $sourceParts[] = "数据库:{$dbChats}条";
            if ($apiChats > 0) $sourceParts[] = "API:{$apiChats}条";
            $sourceText = implode(', ', $sourceParts);
            
            if ($selectedStoreId) {
                $msg = $chatTypeFilter ? 
                    "获取成功 ({$typeText}类型，共{$totalChats}个聊天，{$sourceText})" : 
                    "获取成功 (共{$totalChats}个聊天，{$sourceText})";
            } else {
                $msg = "获取成功 (数据库缓存的买家咨询，共{$totalChats}个聊天)";
            }
        } elseif ($selectedStoreId) {
            $msg = $chatTypeFilter ? 
                "该店铺暂无{$typeText}类型的聊天数据" : 
                "该店铺暂无聊天数据";
        } elseif ($chatTypeFilter && $chatTypeFilter !== 'Buyer_Seller') {
            $msg = "查看{$typeText}类型聊天需要选择具体店铺";
        } elseif ($enabledStores == 0) {
            $msg = "未启用任何店铺的API，请在店铺管理中启用";
        } else {
            $msg = "暂无数据，可选择店铺查看更多聊天";
        }
        
        exit(json_encode([
            'code' => 1, 
            'msg' => $msg,
            'data' => $allChats,
            'stores' => $storeInfoList,
            'total_chats' => count($allChats),
            'total_stores' => count($storeInfoList),
            'enabled_stores' => $enabledStores,
            'selected_store' => $selectedStoreId,
            'api_errors' => $apiErrors,
            'pagination' => [
                'limit' => $limit ?? 400,
                'offset' => $offset ?? 0,
                'has_more' => isset($response['has_next']) ? $response['has_next'] : (count($allChats) >= ($limit ?? 400))
            ]
        ]));
        
    } catch (Exception $e) {
        // 获取聊天列表严重错误
        exit(json_encode([
            'code' => -1, 
            'msg' => '获取聊天列表失败: ' . $e->getMessage(),
            'data' => [],
            'stores' => [],
            'total_chats' => 0,
            'total_stores' => 0
        ]));
    }
}

/**
 * 根据聊天类型生成标题
 */
function getChatTitle($chat) {
    if (isset($chat['title']) && !empty($chat['title'])) {
        return $chat['title'];
    }
    
    // 根据聊天类型设置标题
    switch ($chat['chat_type'] ?? '') {
        case 'Buyer_Seller':
            return '买家咨询';
        case 'Seller_Support':
            return '客服支持';
        case 'Seller_Notification':
            return '系统通知';
        case 'Seller_Notification_FBS':
            return 'FBS通知';
        case 'Seller_Notification_Major':
            return '重要通知';
        case 'Seller_Marketplace_Promo':
            return '平台推广';
        case 'Seller_Notification_Findoc':
            return '财务文档';
        case 'Seller_Notification_UpdateContent':
            return '内容更新';
        case 'Seller_Notification_Content':
            return '内容通知';
        case 'Seller_Paid_Brands':
            return '品牌推广';
        case 'Seller_Online_Training':
            return '在线培训';
        default:
            return '聊天 ' . substr($chat['chat_id'], 0, 8);
    }
}

/**
 * 获取聊天历史
 */
function getChatHistory() {
    global $DB, $uid;
    
    $chatId = $_GET['chat_id'] ?? '';
    $storeId = $_GET['store_id'] ?? '';
    
    if (empty($chatId)) {
        exit(json_encode(['code' => -1, 'msg' => '聊天ID不能为空']));
    }
    
    if (empty($storeId)) {
        exit(json_encode(['code' => -1, 'msg' => '请指定有效的店铺']));
    }
    
    // 动态获取店铺配置
    $storeConfig = $DB->getRow(
        "SELECT id, ClientId, `key`, apistatus, storename FROM ozon_store WHERE id=:store_id AND uid=:uid LIMIT 1",
        [':store_id' => $storeId, ':uid' => $uid]
    );
    
    if (!$storeConfig) {
        exit(json_encode(['code' => -1, 'msg' => '店铺不存在或无权限访问']));
    }
    
    if (!$storeConfig['apistatus']) {
        exit(json_encode(['code' => -1, 'msg' => '店铺API未启用']));
    }
    
    // 创建Ozon客户端
    require_once '../includes/lib/OzonApiClient.php';
    $ozonClient = new \lib\OzonApiClient($storeConfig['ClientId'], $storeConfig['key']);
    
    try {
        // 调用聊天历史API
        
        $response = $ozonClient->getChatHistory($chatId, 100, 0);
        
        // 分析API响应结构
        
        // 检查是否有API错误
        if (isset($response['error'])) {
            // API返回错误
            throw new Exception("Chat API Error: " . $response['error']);
        }
        
        // 检查HTTP错误
        if (isset($response['response']) && strpos($response['response'], 'error') !== false) {
            // HTTP错误响应
            throw new Exception("HTTP Error: " . $response['response']);
        }
        
        // 检查API响应格式，支持Ozon官方API响应结构
        $apiMessages = null;
        
        // Ozon官方响应格式: {"has_next": true, "messages": [...]}
        if (isset($response['messages']) && is_array($response['messages'])) {
            $apiMessages = $response['messages'];
            // 找到消息数据
        } 
        // 备用格式检查
        elseif (isset($response['result']['messages']) && is_array($response['result']['messages'])) {
            $apiMessages = $response['result']['messages'];
            // 找到消息数据
        } elseif (isset($response['result']) && is_array($response['result'])) {
            $apiMessages = $response['result'];
            // 找到消息数据
        } elseif (is_array($response) && empty($response)) {
            // 空数组表示没有消息
            $apiMessages = [];
            // API返回空数组
        } elseif (isset($response['result']) && $response['result'] === null) {
            $apiMessages = [];
            // API返回result为null
        }
        
        // 如果API返回null或没有找到消息数据，显示具体错误
        if ($apiMessages === null) {
            $errorDetails = [];
            if (isset($response['message'])) {
                $errorDetails[] = 'API消息: ' . $response['message'];
            }
            if (isset($response['error'])) {
                $errorDetails[] = 'API错误: ' . $response['error'];
            }
            if (isset($response['code'])) {
                $errorDetails[] = 'API错误码: ' . $response['code'];
            }
            
            $errorMsg = '获取聊天历史失败: ';
            if (!empty($errorDetails)) {
                $errorMsg .= implode(', ', $errorDetails);
            } else {
                $errorMsg .= '未找到消息数据，可能该聊天为空或API权限不足';
            }
            
            $canSendMessage = true;
        if (isset($response['result']) && isset($response['result']['can_send_message'])) {
            $canSendMessage = $response['result']['can_send_message'];
        }

        // 聊天历史获取失败
        exit(json_encode([
                'code' => -1, 
                'msg' => $errorMsg, 
                'error_details' => [
                    'chat_id' => $chatId,
                    'store_id' => $storeId
                ]
            ]));
        }
        
        if (is_array($apiMessages)) {
            $messages = [];
            $chatOrderInfo = null; // 存储从聊天历史中提取的订单信息
            
            foreach ($apiMessages as $msg) {
                // 处理Ozon API的消息格式
                $text = '';
                if (isset($msg['data']) && is_array($msg['data'])) {
                    // data字段包含消息内容数组
                    $text = implode("\n", $msg['data']);
                } elseif (isset($msg['text'])) {
                    $text = $msg['text'];
                } elseif (isset($msg['content'])) {
                    $text = $msg['content'];
                }
                
                // 从第一条消息的context中提取订单信息
                if ($chatOrderInfo === null && isset($msg['context'])) {
                    $chatOrderInfo = [
                        'order_number' => $msg['context']['order_number'] ?? '',
                        'sku' => $msg['context']['sku'] ?? '',
                        'product_name' => '',
                        'extraction_source' => 'chat_history_context'
                    ];
                    
                    // 尝试从消息内容中提取商品名称
                    if (isset($msg['data']) && is_array($msg['data'])) {
                        $messageText = implode(' ', $msg['data']);
                        // 提取引号中的商品名称
                        if (preg_match('/"([^"]{3,100})"/u', $messageText, $matches)) {
                            $chatOrderInfo['product_name'] = $matches[1];
                        }
                        // 提取артикул后的SKU（如果context中没有）
                        if (empty($chatOrderInfo['sku']) && preg_match('/артикул\s*(\d+)/iu', $messageText, $matches)) {
                            $chatOrderInfo['sku'] = $matches[1];
                        }
                    }
                    
                    // 缓存订单信息到数据库或会话中
                    cacheOrderInfoForChat($chatId, $storeId, $chatOrderInfo, $uid);
                }
                
                // 判断消息发送方
                $fromMe = false;
                if (isset($msg['user']['type'])) {
                    // NotificationUser, SystemUser等为系统消息
                    // 如果是seller发送的消息，user.type可能是SellerUser
                    $fromMe = in_array($msg['user']['type'], ['SellerUser', 'Seller']);
                } elseif (isset($msg['from_me'])) {
                    $fromMe = $msg['from_me'];
                }
                
                $message = [
                    'message_id' => $msg['message_id'] ?? uniqid(),
                    'chat_id' => $chatId,
                    'text' => $text,
                    'from_me' => $fromMe,
                    'created_at' => $msg['created_at'] ?? date('Y-m-d H:i:s'),
                    'attachments' => $msg['attachments'] ?? [],
                    'user_type' => $msg['user']['type'] ?? 'Unknown',
                    'user_id' => $msg['user']['id'] ?? '',
                    'is_read' => $msg['is_read'] ?? true,
                    'is_image' => $msg['is_image'] ?? false
                ];
                
                $messages[] = $message;
            }
            
            // 返回提取到的订单信息
            $orderInfo = $chatOrderInfo;

            exit(json_encode(['code' => 1, 'data' => $messages, 'order_info' => $orderInfo, 'can_send_message' => $canSendMessage]));

            // 标记聊天为已读
            $localChat = $DB->getRow(
                "SELECT id FROM ozon_chats WHERE chat_id=:chat_id AND uid=:uid LIMIT 1",
                [':chat_id' => $chatId, ':uid' => $uid]
            );
            
            if ($localChat) {
                $DB->exec(
                    "UPDATE ozon_chats SET unread_count=0 WHERE id=:chat_id",
                    [':chat_id' => $localChat['id']]
                );
                $DB->exec(
                    "UPDATE ozon_messages SET is_read=1 WHERE chat_id=:chat_id AND sender_type='customer'",
                    [':chat_id' => $localChat['id']]
                );
            }
            
            if (count($messages) > 0) {
                // 按时间排序，最老的消息在前面（正常聊天顺序）
                usort($messages, function($a, $b) {
                    return strtotime($a['created_at']) - strtotime($b['created_at']);
                });
                
                // 返回消息和订单信息
                $response = [
                    'code' => 1, 
                    'msg' => '获取成功', 
                    'data' => $messages
                ];
                
                // 如果提取到了订单信息，包含在响应中
                if ($chatOrderInfo && (!empty($chatOrderInfo['order_number']) || !empty($chatOrderInfo['sku']) || !empty($chatOrderInfo['product_name']))) {
                    $response['order_info'] = $chatOrderInfo;
                }
                
                exit(json_encode($response));
            } else {
                exit(json_encode(['code' => 1, 'msg' => '该聊天暂无消息', 'data' => []]));
            }
        } else {
            $errorMsg = '获取聊天历史失败: ';
            if (isset($response['message'])) {
                $errorMsg .= $response['message'];
            } elseif (isset($response['error'])) {
                $errorMsg .= $response['error'];
            } else {
                $errorMsg .= '未找到消息数据';
            }
            exit(json_encode(['code' => -1, 'msg' => $errorMsg]));
        }
    } catch (Exception $e) {
        // 聊天历史API异常
        exit(json_encode([
            'code' => -1, 
            'msg' => '获取聊天历史失败: ' . $e->getMessage(),
            'error_details' => [
                'chat_id' => $chatId,
                'store_id' => $storeId
            ]
        ]));
    }
}

/**
 * 发送消息
 */
function sendMessage($ozonClient, $storeConfig) {
    global $DB, $uid;
    
    $chatId = $_POST['chat_id'] ?? '';
    $storeId = $_POST['store_id'] ?? '';
    $text = trim($_POST['text'] ?? '');
    
    if (empty($chatId) || empty($text)) {
        exit(json_encode(['code' => -1, 'msg' => '聊天ID和消息内容不能为空']));
    }
    
    if (empty($storeId)) {
        exit(json_encode(['code' => -1, 'msg' => '请指定有效的店铺']));
    }
    
    // 动态获取店铺配置
    $storeConfig = $DB->getRow(
        "SELECT id, ClientId, `key`, apistatus, storename FROM ozon_store WHERE id=:store_id AND uid=:uid LIMIT 1",
        [':store_id' => $storeId, ':uid' => $uid]
    );
    
    if (!$storeConfig) {
        exit(json_encode(['code' => -1, 'msg' => '店铺不存在或无权限访问']));
    }
    
    if (!$storeConfig['apistatus']) {
        exit(json_encode(['code' => -1, 'msg' => '店铺API未启用']));
    }
    
    // 创建Ozon客户端
    $ozonClient = new \lib\OzonApiClient($storeConfig['ClientId'], $storeConfig['key']);
    
    try {
        $response = $ozonClient->sendMessage($chatId, $text);
        
        // 处理发送消息响应
        
        // 检查特定的错误类型
        if (isset($response['code'])) {
            // if ($response['code'] == 7 && strpos($response['message'], 'chat disabled') !== false) {
            //     exit(json_encode([
            //         'code' => -1, 
            //         'msg' => '此聊天类型不支持发送消息（只读聊天）',
            //         'error_type' => 'chat_disabled'
            //     ]));
            // }
            
            if ($response['code'] != 0) {
                $errorMsg = '发送失败: ';
                if (isset($response['message'])) {
                    $errorMsg .= $response['message'];
                } else {
                    $errorMsg .= "错误代码: " . $response['code'];
                }
                exit(json_encode(['code' => -1, 'msg' => $errorMsg]));
            }
        }
        
        // 检查不同的响应格式
        $success = false;
        $messageId = uniqid();
        
        if (isset($response['result']) && $response['result'] === 'success') {
            // 处理 {result: "success"} 格式
            $success = true;
            $messageId = $response['message_id'] ?? uniqid();
        } elseif (isset($response['result']['success']) && $response['result']['success']) {
            $success = true;
            $messageId = $response['result']['message_id'] ?? uniqid();
        } elseif (isset($response['success']) && $response['success']) {
            $success = true;
            $messageId = $response['message_id'] ?? uniqid();
        } elseif (isset($response['message_id'])) {
            $success = true;
            $messageId = $response['message_id'];
        }
        
        if ($success) {
            // 构造返回消息数据
            $message = [
                'message_id' => $messageId,
                'chat_id' => $chatId,
                'text' => $text,
                'from_me' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'attachments' => []
            ];
            
            exit(json_encode(['code' => 1, 'msg' => '发送成功', 'data' => $message]));
        } else {
            $errorMsg = '发送失败: ';
            if (isset($response['message'])) {
                $errorMsg .= $response['message'];
            } elseif (isset($response['error'])) {
                $errorMsg .= $response['error'];
            } else {
                $errorMsg .= '未知错误';
            }
            exit(json_encode(['code' => -1, 'msg' => $errorMsg]));
        }
    } catch (Exception $e) {
        // 发送消息API异常
        exit(json_encode(['code' => -1, 'msg' => '发送失败: ' . $e->getMessage()]));
    }
}

/**
 * 发送文件
 */
function sendFile($ozonClient, $storeConfig) {
    global $DB, $uid;
    
    $chatId = $_POST['chat_id'] ?? '';
    
    if (empty($chatId)) {
        exit(json_encode(['code' => -1, 'msg' => '聊天ID不能为空']));
    }
    
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        exit(json_encode(['code' => -1, 'msg' => '文件上传失败']));
    }
    
    $file = $_FILES['file'];
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];
    $maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!in_array($file['type'], $allowedTypes)) {
        exit(json_encode(['code' => -1, 'msg' => '不支持的文件类型']));
    }
    
    if ($file['size'] > $maxSize) {
        exit(json_encode(['code' => -1, 'msg' => '文件大小不能超过10MB']));
    }
    
    try {
        // 上传文件到本地
        $uploadDir = '../uploads/chat_files/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = time() . '_' . $file['name'];
        $filePath = $uploadDir . $fileName;
        
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            exit(json_encode(['code' => -1, 'msg' => '文件保存失败']));
        }
        
        // 构建附件数据
        $attachments = [
            [
                'name' => $file['name'],
                'type' => $file['type'],
                'size' => $file['size'],
                'url' => '../uploads/chat_files/' . $fileName
            ]
        ];
        
        // 发送文件
        $fileContent = base64_encode(file_get_contents($filePath));
        $response = $ozonClient->sendFile($chatId, $file['name'], $fileContent);
        
        if (isset($response['result']) && $response['result']['success']) {
            // 保存消息到本地
            $message = [
                'message_id' => $response['result']['message_id'] ?? uniqid(),
                'chat_id' => $chatId,
                'text' => '[文件] ' . $file['name'],
                'from_me' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'attachments' => $attachments
            ];
            

            
            exit(json_encode(['code' => 1, 'msg' => '文件发送成功', 'data' => $message]));
        } else {
            // 删除已上传的文件
            unlink($filePath);
            exit(json_encode(['code' => -1, 'msg' => '文件发送失败: ' . ($response['message'] ?? '未知错误')]));
        }
    } catch (Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => '文件发送失败: ' . $e->getMessage()]));
    }
}

/**
 * 标记消息为已读
 */
function markRead($ozonClient, $storeConfig) {
    global $DB, $uid;
    
    $chatId = $_POST['chat_id'] ?? '';
    
    if (empty($chatId)) {
        exit(json_encode(['code' => -1, 'msg' => '聊天ID不能为空']));
    }
    
    try {
        // 标记聊天为已读
        $localChat = $DB->getRow(
            "SELECT id FROM ozon_chats WHERE chat_id=:chat_id AND uid=:uid LIMIT 1",
            [':chat_id' => $chatId, ':uid' => $uid]
        );
        
        if ($localChat) {
            $DB->exec(
                "UPDATE ozon_chats SET unread_count=0 WHERE id=:chat_id",
                [':chat_id' => $localChat['id']]
            );
            $DB->exec(
                "UPDATE ozon_messages SET is_read=1 WHERE chat_id=:chat_id AND sender_type='customer'",
                [':chat_id' => $localChat['id']]
            );
        }
        
        exit(json_encode(['code' => 1, 'msg' => '标记成功']));
    } catch (Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => '标记失败: ' . $e->getMessage()]));
    }
}

/**
 * 获取聊天更新
 */
function getUpdates($ozonClient, $storeConfig) {
    global $DB, $uid;
    
    $chatId = $_GET['chat_id'] ?? '';
    
    try {
        // 获取最后一条消息的时间
        $localChat = $DB->getRow(
            "SELECT id FROM ozon_chats WHERE chat_id=:chat_id AND uid=:uid LIMIT 1",
            [':chat_id' => $chatId, ':uid' => $uid]
        );
        
        $lastMessageTime = null;
        if ($localChat) {
            $lastMessageTime = $DB->getColumn(
                "SELECT MAX(sent_at) FROM ozon_messages WHERE chat_id=:chat_id",
                [':chat_id' => $localChat['id']]
            );
        }
        
        $since = $lastMessageTime ? date('c', strtotime($lastMessageTime)) : null;
        $response = $ozonClient->getChatUpdates($since);
        
        if (isset($response['result'])) {
            $updates = [];
            foreach ($response['result']['updates'] as $update) {
                if ($update['type'] === 'new_message' && 
                    (!$chatId || $update['chat_id'] === $chatId)) {
                    $updates[] = $update;
                }
            }
            
            exit(json_encode(['code' => 1, 'msg' => '获取成功', 'data' => $updates]));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '获取更新失败: ' . ($response['message'] ?? '未知错误')]));
        }
    } catch (Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => '获取更新失败: ' . $e->getMessage()]));
    }
}

/**
 * 缓存聊天的订单信息
 */
function cacheOrderInfoForChat($chatId, $storeId, $orderInfo, $userId) {
    global $DB;
    
    try {
        // 查找或创建聊天记录，直接在ozon_chats表中存储订单信息
        $existingChat = $DB->getRow(
            "SELECT id FROM ozon_chats WHERE chat_id=:chat_id AND store_id=:store_id AND uid=:uid LIMIT 1",
            [':chat_id' => $chatId, ':store_id' => $storeId, ':uid' => $userId]
        );
        
        $orderInfoJson = json_encode($orderInfo, JSON_UNESCAPED_UNICODE);
        
        if ($existingChat) {
            // 更新现有聊天记录的订单信息
            $DB->exec(
                "UPDATE ozon_chats SET order_number=:order_number, sku=:sku, product_name=:product_name, order_info_json=:order_info_json, extraction_source=:extraction_source, updated_at=NOW() WHERE id=:id",
                [
                    ':order_number' => $orderInfo['order_number'] ?? '',
                    ':sku' => $orderInfo['sku'] ?? '',
                    ':product_name' => $orderInfo['product_name'] ?? '',
                    ':order_info_json' => $orderInfoJson,
                    ':extraction_source' => $orderInfo['extraction_source'] ?? 'api',
                    ':id' => $existingChat['id']
                ]
            );
        } else {
            // 创建新的聊天记录
            $DB->exec(
                "INSERT INTO ozon_chats (uid, store_id, chat_id, title, chat_type, status, order_number, sku, product_name, order_info_json, extraction_source, created_at, updated_at) VALUES (:uid, :store_id, :chat_id, '买家咨询', 'Buyer_Seller', 'active', :order_number, :sku, :product_name, :order_info_json, :extraction_source, NOW(), NOW())",
                [
                    ':uid' => $userId,
                    ':store_id' => $storeId,
                    ':chat_id' => $chatId,
                    ':order_number' => $orderInfo['order_number'] ?? '',
                    ':sku' => $orderInfo['sku'] ?? '',
                    ':product_name' => $orderInfo['product_name'] ?? '',
                    ':order_info_json' => $orderInfoJson,
                    ':extraction_source' => $orderInfo['extraction_source'] ?? 'api'
                ]
            );
        }
        
        // 缓存订单信息
    } catch (Exception $e) {
        // 缓存订单信息失败
    }
}



/**
 * 获取缓存的订单信息
 */
function getCachedOrderInfoForChat($chatId, $storeId, $userId) {
    global $DB;
    
    try {
        $record = $DB->getRow(
            "SELECT order_number, sku, product_name, order_info_json, extraction_source FROM ozon_chats WHERE chat_id=:chat_id AND store_id=:store_id AND uid=:uid LIMIT 1",
            [':chat_id' => $chatId, ':store_id' => $storeId, ':uid' => $userId]
        );
        
        if ($record && (!empty($record['order_number']) || !empty($record['sku']) || !empty($record['product_name']))) {
            return [
                'order_number' => $record['order_number'] ?? '',
                'sku' => $record['sku'] ?? '',
                'product_name' => $record['product_name'] ?? '',
                'extraction_source' => $record['extraction_source'] ?? 'cached_from_db'
            ];
        }
    } catch (Exception $e) {
        // 获取缓存订单信息失败
    }
    
    return null;
}



/**
 * 从聊天列表API响应中直接提取订单信息（最轻量级）
 */
function extractOrderInfoFromChatListResponse($chat) {
    $orderInfo = [
        'order_number' => '',
        'sku' => '',
        'product_name' => '',
        'source' => 'chatlist_response'
    ];
    
    // 只有买家咨询类型的聊天才可能包含订单信息
    if (($chat['chat_type'] ?? '') !== 'Buyer_Seller') {
        $orderInfo['source'] = 'not_buyer_seller_type';
        return $orderInfo;
    }
    
    // 检查聊天列表响应中是否包含订单相关字段
    $checkFields = [
        'order_number', 'order_id', 'orderId', 'order', 
        'sku', 'product_sku', 'article', 'articul',
        'product_name', 'product_title', 'item_name', 'title'
    ];
    
    $foundFields = [];
    foreach ($checkFields as $field) {
        if (isset($chat[$field]) && !empty($chat[$field])) {
            $foundFields[] = "$field: " . $chat[$field];
            
            // 映射到标准字段
            if (in_array($field, ['order_number', 'order_id', 'orderId', 'order'])) {
                $orderInfo['order_number'] = $chat[$field];
            } elseif (in_array($field, ['sku', 'product_sku', 'article', 'articul'])) {
                $orderInfo['sku'] = $chat[$field];
            } elseif (in_array($field, ['product_name', 'product_title', 'item_name']) && $field !== 'title') {
                $orderInfo['product_name'] = $chat[$field];
            }
        }
    }
    
    // 检查嵌套字段（如果有）
    if (isset($chat['context']) && is_array($chat['context'])) {
        foreach ($checkFields as $field) {
            if (isset($chat['context'][$field]) && !empty($chat['context'][$field])) {
                $foundFields[] = "context.$field: " . $chat['context'][$field];
                
                if (in_array($field, ['order_number', 'order_id', 'orderId', 'order'])) {
                    $orderInfo['order_number'] = $chat['context'][$field];
                } elseif (in_array($field, ['sku', 'product_sku', 'article', 'articul'])) {
                    $orderInfo['sku'] = $chat['context'][$field];
                } elseif (in_array($field, ['product_name', 'product_title', 'item_name']) && $field !== 'title') {
                    $orderInfo['product_name'] = $chat['context'][$field];
                }
            }
        }
    }
    
    // 检查last_message结构化数据
    if (isset($chat['last_message']) && is_array($chat['last_message'])) {
        foreach ($checkFields as $field) {
            if (isset($chat['last_message'][$field]) && !empty($chat['last_message'][$field])) {
                $foundFields[] = "last_message.$field: " . $chat['last_message'][$field];
                
                if (in_array($field, ['order_number', 'order_id', 'orderId', 'order'])) {
                    $orderInfo['order_number'] = $chat['last_message'][$field];
                } elseif (in_array($field, ['sku', 'product_sku', 'article', 'articul'])) {
                    $orderInfo['sku'] = $chat['last_message'][$field];
                } elseif (in_array($field, ['product_name', 'product_title', 'item_name']) && $field !== 'title') {
                    $orderInfo['product_name'] = $chat['last_message'][$field];
                }
            }
        }
    }
    
    // 如果没有从context或其他字段提取到订单号，尝试从data数组提取
    if (empty($orderInfo['order_number']) && isset($chat['data']) && is_array($chat['data']) && !empty($chat['data'])) {
        $firstDataItem = $chat['data'][0] ?? '';
        // 检查是否像订单号格式（包含数字和连字符）
        if (preg_match('/^\d{8,}-\d{4}-\d+$/', $firstDataItem)) {
            $orderInfo['order_number'] = $firstDataItem;
            $foundFields[] = "data[0]: " . $firstDataItem;
        }
    }
    
    // 如果还是没有提取到商品名称，尝试从data数组的第二个元素提取
    if (empty($orderInfo['product_name']) && isset($chat['data']) && is_array($chat['data']) && count($chat['data']) > 1) {
        $secondDataItem = $chat['data'][1] ?? '';
        if (!empty($secondDataItem) && mb_strlen($secondDataItem) > 3) {
            $orderInfo['product_name'] = $secondDataItem;
            $foundFields[] = "data[1]: " . $secondDataItem;
        }
    }
    
    $orderInfo['extraction_info'] = empty($foundFields) ? 'no_fields_found' : implode('; ', $foundFields);
    
    return $orderInfo;
}

/**
 * 从聊天基本信息中提取订单信息（轻量级）
 */
function extractOrderInfoFromChat($chat) {
    $orderInfo = [
        'order_number' => '',
        'sku' => '',
        'product_name' => '',
        'source' => 'text_extraction'
    ];
    
    // 只有买家咨询类型的聊天才可能包含订单信息
    if (($chat['chat_type'] ?? '') !== 'Buyer_Seller') {
        $orderInfo['source'] = 'not_buyer_seller_type';
        return $orderInfo;
    }
    
    // 收集所有文本内容用于匹配
    $textSources = [
        'title' => $chat['title'] ?? '',
        'last_message' => is_string($chat['last_message'] ?? '') ? $chat['last_message'] : '',
        'description' => $chat['description'] ?? '',
        'preview' => $chat['preview_text'] ?? ''
    ];
    
    $allText = implode(' ', array_filter($textSources));
    $extractedInfo = [];
    
    if (!empty($allText)) {
        // 1. 提取订单号 - 多种格式
        $orderPatterns = [
            '/заказ[^\d]*(\d+)/iu',           // "заказ 123456"
            '/order[^\d]*(\d+)/iu',          // "order 123456"
            '/№\s*(\d+)/u',                  // "№ 123456"
            '/номер[^\d]*(\d+)/iu',          // "номер 123456"
            '/(\d{6,})/u'                    // 6位以上数字（可能是订单号）
        ];
        
        foreach ($orderPatterns as $pattern) {
            if (preg_match($pattern, $allText, $matches)) {
                $orderInfo['order_number'] = $matches[1];
                $extractedInfo[] = "order: {$matches[1]}";
                break;
            }
        }
        
        // 2. 提取SKU - 多种格式
        $skuPatterns = [
            '/артикул[^\d]*(\d+)/iu',        // "артикул 123456"
            '/sku[^\d]*(\d+)/iu',           // "sku 123456"
            '/код[^\d]*(\d+)/iu',           // "код товара 123456"
            '/арт\.\s*(\d+)/iu',            // "арт. 123456"
            '/id[^\d]*(\d+)/iu'             // "id 123456"
        ];
        
        foreach ($skuPatterns as $pattern) {
            if (preg_match($pattern, $allText, $matches)) {
                $orderInfo['sku'] = $matches[1];
                $extractedInfo[] = "sku: {$matches[1]}";
                break;
            }
        }
        
        // 3. 提取商品名称 - 多种格式
        $namePatterns = [
            '/"([^"]{3,100})"/u',           // 引号中的文本（3-100字符）
            '/товар[:\s]+([^.\n]{3,100})/iu', // "товар: название"
            '/продукт[:\s]+([^.\n]{3,100})/iu', // "продукт: название"
            '/наименование[:\s]+([^.\n]{3,100})/iu' // "наименование: название"
        ];
        
        foreach ($namePatterns as $pattern) {
            if (preg_match($pattern, $allText, $matches)) {
                $productName = trim($matches[1]);
                if (strlen($productName) >= 3) {
                    $orderInfo['product_name'] = $productName;
                    $extractedInfo[] = "product: " . substr($productName, 0, 50);
                    break;
                }
            }
        }
        
        $orderInfo['extraction_info'] = empty($extractedInfo) ? 'no_patterns_matched' : implode('; ', $extractedInfo);
    } else {
        $orderInfo['source'] = 'no_text_content';
    }
    
    return $orderInfo;
}

/**
 * 缓存买家咨询到数据库
 */
function cacheBuyerChatToDatabase($chatItem, $uid) {
    global $DB;
    
    try {
        // 检查是否已存在
        $existing = $DB->getRow(
            "SELECT id FROM ozon_chats WHERE chat_id = ? AND store_id = ? AND uid = ?",
            [$chatItem['chat_id'], $chatItem['store_id'], $uid]
        );
        
        if ($existing) {
            // 更新现有记录
            $DB->exec(
                "UPDATE ozon_chats SET title = ?, order_number = ?, sku = ?, product_name = ?, updated_at = NOW() WHERE id = ?",
                [
                    $chatItem['title'],
                    $chatItem['order_number'],
                    $chatItem['sku'],
                    $chatItem['product_name'],
                    $existing['id']
                ]
            );
            // 更新买家咨询缓存
        } else {
            // 创建新记录
            $DB->exec(
                "INSERT INTO ozon_chats (uid, store_id, chat_id, title, chat_type, status, order_number, sku, product_name, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $uid,
                    $chatItem['store_id'],
                    $chatItem['chat_id'],
                    $chatItem['title'],
                    'Buyer_Seller',
                    $chatItem['status'],
                    $chatItem['order_number'],
                    $chatItem['sku'],
                    $chatItem['product_name']
                ]
            );
            // 新增买家咨询缓存
        }
    } catch (Exception $e) {
        // 缓存买家咨询失败
    }
}

/**
 * 提取聊天中的订单和商品信息（详细版本，需要API调用）
 */
function extractOrderInfo($ozonClient, $chatId, $chatType) {
    $orderInfo = [
        'order_number' => '',
        'sku' => '',
        'product_name' => ''
    ];
    
    // 只有买家咨询类型的聊天才可能包含订单信息
    if ($chatType !== 'Buyer_Seller') {
        return $orderInfo;
    }
    
    try {
        // 获取聊天的第一条消息来提取订单信息
        $response = $ozonClient->getChatHistory($chatId, 1, 0);
        
        if (isset($response['messages']) && !empty($response['messages'])) {
            $firstMessage = $response['messages'][0];
            
            // 检查消息的context字段
            if (isset($firstMessage['context'])) {
                $context = $firstMessage['context'];
                
                if (isset($context['order_number'])) {
                    $orderInfo['order_number'] = $context['order_number'];
                }
                
                if (isset($context['sku'])) {
                    $orderInfo['sku'] = $context['sku'];
                }
            }
            
            // 尝试从消息内容中提取商品名称
            if (isset($firstMessage['data']) && is_array($firstMessage['data'])) {
                $messageText = implode(' ', $firstMessage['data']);
                
                // 使用正则表达式提取商品名称（在引号中的文本）
                if (preg_match('/"([^"]*)"/', $messageText, $matches)) {
                    $orderInfo['product_name'] = $matches[1];
                }
                
                // 尝试提取SKU（数字格式）
                if (empty($orderInfo['sku']) && preg_match('/артикул\s*(\d+)/iu', $messageText, $matches)) {
                    $orderInfo['sku'] = $matches[1];
                }
            }
        }
    } catch (Exception $e) {
        // 如果获取失败，记录日志但不影响主流程
        // 提取订单信息失败
    }
    
    return $orderInfo;
}

/**
 * 应用聊天筛选条件
 */
function applyChatFilters($chatItem, $chatTypeFilter, $chatStatusFilter, $timeFilter, $startDate, $endDate) {
    // 类型筛选
    if ($chatTypeFilter && $chatItem['chat_type'] !== $chatTypeFilter) {
        return false;
    }
    
    // 状态筛选（未读/已读）
    if ($chatStatusFilter) {
        if ($chatStatusFilter === 'unread' && $chatItem['unread_count'] <= 0) {
            return false;
        }
        if ($chatStatusFilter === 'read' && $chatItem['unread_count'] > 0) {
            return false;
        }
    }
    
    // 时间筛选
    if ($timeFilter && $timeFilter !== 'custom') {
        $chatDate = strtotime($chatItem['last_message_time']);
        $now = time();
        $today = strtotime(date('Y-m-d'));
        
        switch ($timeFilter) {
            case 'today':
                if ($chatDate < $today) return false;
                break;
            case 'yesterday':
                $yesterday = $today - 86400;
                if ($chatDate < $yesterday || $chatDate >= $today) return false;
                break;
            case 'week':
                $weekAgo = $today - (7 * 86400);
                if ($chatDate < $weekAgo) return false;
                break;
            case 'month':
                $monthAgo = $today - (30 * 86400);
                if ($chatDate < $monthAgo) return false;
                break;
        }
    }
    
    // 自定义时间范围筛选
    if ($timeFilter === 'custom' && ($startDate || $endDate)) {
        $chatDate = strtotime($chatItem['last_message_time']);
        if ($startDate) {
            $start = strtotime($startDate);
            if ($chatDate < $start) return false;
        }
        if ($endDate) {
            $end = strtotime($endDate . ' 23:59:59');
            if ($chatDate > $end) return false;
        }
    }
    
    return true;
}

/**
 * 获取聊天的详细订单信息
 */
function getOrderInfo($ozonClient, $storeConfig) {
    global $DB, $uid;
    
    $chatId = $_GET['chat_id'] ?? '';
    $chatType = $_GET['chat_type'] ?? '';
    
    if (empty($chatId)) {
        exit(json_encode(['code' => -1, 'msg' => '聊天ID不能为空']));
    }
    
    try {
        $orderInfo = extractOrderInfo($ozonClient, $chatId, $chatType);
        exit(json_encode(['code' => 1, 'msg' => '获取成功', 'data' => $orderInfo]));
    } catch (Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => '获取订单信息失败: ' . $e->getMessage()]));
    }
}

// 调试函数已移除

/**
 * 获取聊天的缓存订单信息
 */
function getChatCachedOrderInfo() {
    global $DB, $uid;
    
    $chatId = $_GET['chat_id'] ?? '';
    $storeId = $_GET['store_id'] ?? '';
    
    if (empty($chatId) || empty($storeId)) {
        exit(json_encode(['code' => -1, 'msg' => '聊天ID和店铺ID不能为空']));
    }
    
    // 获取缓存的订单信息
    $cachedInfo = getCachedOrderInfoForChat($chatId, $storeId, $uid);
    
    // 获取所有相关的缓存记录用于调试
    try {
        $allCached = $DB->getAll(
            "SELECT chat_id, order_number, sku, product_name, extraction_source, created_at, updated_at FROM ozon_chats WHERE uid=:uid AND store_id=:store_id AND (order_number != '' OR sku != '' OR product_name != '') ORDER BY updated_at DESC LIMIT 10",
            [':uid' => $uid, ':store_id' => $storeId]
        );
    } catch (Exception $e) {
        $allCached = [];
    }
    
    exit(json_encode([
        'code' => 1,
        'msg' => '获取成功',
        'data' => [
            'current_chat' => [
                'chat_id' => $chatId,
                'store_id' => $storeId,
                'cached_info' => $cachedInfo
            ],
            'all_cached' => $allCached,
            'total_cached' => count($allCached)
        ]
    ], JSON_UNESCAPED_UNICODE));
}

// 演示数据函数已移除，现在只使用真实的Ozon API数据
?>
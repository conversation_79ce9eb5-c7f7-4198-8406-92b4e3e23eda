
    <style>

        /* 待办事项信息栏样式 */
       .htozonbody .todo-info {
            border: none;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 3rem;
            transition: transform 0.3s ease;
            background-color: #ffffff;
        }

        /* 新增布局样式 */
        .htozonbody .content-wrapper {
            display: flex;
            width: 100%;
        }
        .htozonbody .left-panel {
            width: 50%;
            padding-right: 1rem;
            box-sizing: border-box;
        }
        .htozonbody .right-panel {
            width: 50%;
            padding-left: 1rem;
            box-sizing: border-box;
            border-left: 1px solid #e0e6ed;
            min-height: 400px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }
        .htozonbody .left-panel .tab-buttons {
            justify-content: flex-start;
            margin-bottom: 1rem;
        }
        .htozonbody .left-panel .info-list {
            max-height: 600px;
            overflow-y: auto;
        }

        /* 商品步骤样式 */
        .htozonbody .product-steps {
            padding: 1rem 2rem;
        }
        .htozonbody .product-steps h2 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }
        .htozonbody .product-steps ol {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        .htozonbody .product-steps ol li {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 1.5rem;
            font-size: 1rem;
            color: #333;
        }
        .htozonbody .product-steps ol li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            width: 2rem;
            height: 2rem;
            line-height: 2rem;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            text-align: center;
            font-weight: bold;
            font-size: 1rem;
        }
        .htozonbody .product-steps ol li a {
            color: #007bff;
            text-decoration: none;
            margin-left: 0.5rem;
        }
        .htozonbody .product-steps ol li a:hover {
            text-decoration: underline;
        }

       .htozonbody .todo-info:hover {
            transform: translateY(-8px);
        }

       .htozonbody .todo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #007bff;
            padding: 1.25rem 1.75rem;
            color: white;
        }

       .htozonbody .todo-header button {
            background-color: #ffffff;
            color: #007bff;
            border: none;
            padding: 0.625rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-weight: 600;
        }

       .htozonbody .todo-header button:hover {
            background-color: #e0e0e0;
        }

       .htozonbody .todo-header a {
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

       .htozonbody .todo-header a:hover {
            color: #e0e0e0;
        }

       .htozonbody .todo-row {
            display: flex;
            border-bottom: 1px solid #e0e6ed;
            padding: 1.5rem 1.75rem;
        }

       .htozonbody .todo-type {
            width: 10%;
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #333;
        }

       .htozonbody .todo-type i {
            font-size: 1.5rem;
            margin-right: 0.75rem;
            color: #007bff;
        }

       .htozonbody .todo-item {
            width: 15%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

       .htozonbody .todo-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }

       .htozonbody .todo-number.red {
            color: #f5222d;
        }

       .htozonbody .todo-text {
            font-size: 0.9rem;
            color: #666;
            text-align: center;
        }

        /* 切换按钮样式 */
       .htozonbody .tab-buttons {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

       .htozonbody .tab-buttons button {
            margin: 0 0.75rem;
            padding: 0.75rem 1.5rem;
            border: none;
            background-color: #e0e6ed;
            cursor: pointer;
            border-radius: 8px;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-weight: 600;
            color: #333;
        }

       .htozonbody .tab-buttons button.active {
            background-color: #007bff;
            color: white;
        }

       .htozonbody .tab-buttons button:hover {
            background-color: #0056b3;
            color: white;
        }

        /* 信息列表样式 */
       .htozonbody .info-list {
            display: none;
            animation: fadeIn 0.3s ease;
        }

       .htozonbody .info-list.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片样式 */
       .htozonbody .layui-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
            background-color: #ffffff;
        }

       .htozonbody .layui-card:hover {
            transform: translateY(-8px);
        }

       .htozonbody .layui-card-body {
            padding: 1.75rem;
        }

       .htozonbody .dynamic-status dd {
            margin-bottom: 1rem;
            border-bottom: 1px solid #e0e6ed;
            padding-bottom: 1rem;
        }

       .htozonbody .dynamic-status dd:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

       .htozonbody .dynamic-status p {
            font-size: 1rem;
            color: #333;
            margin-bottom: 0.25rem;
        }

       .htozonbody .dynamic-status span {
            font-size: 0.875rem;
            color: #666;
        }
    </style>


<div class="htozonbody">
            <div class="pear-container">
                <!-- 待办事项信息栏 -->
                <div class="todo-info">
                    <div class="todo-header">
                        <button>待办事项</button>
                        <a href="#">自定义待办事项</a>
                    </div>
                    <div class="todo-row">
                        <div class="todo-type">
                            <i class="fa-solid fa-box"></i> 产品
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="collectionBox">0</div> <!-- 采集箱数量 -->
                            <div class="todo-text">采集箱</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="todayPublishSuccess">0</div> <!-- 今日发布成功数量 -->
                            <div class="todo-text">今日发布成功</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="todayPublishFail">0</div> <!-- 今日发布失败数量 -->
                            <div class="todo-text">今日发布失败</div>
                        </div>
                    </div>
                    <div class="todo-row">
                        <div class="todo-type">
                            <i class="fa-solid fa-shopping-cart"></i> 订单
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="remainingDelivery">0</div>
                            <div class="todo-text">剩余发货小于1天</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="delayedShipment">0</div>
                            <div class="todo-text">超时订单</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="pendingPurchase">0</div>
                            <div class="todo-text">待采购</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="waitingShipment">0</div>
                            <div class="todo-text">等待发货</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number red" id="inTransit">0</div>
                            <div class="todo-text">运输中</div>
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="pendingAfterSales">0</div>
                            <div class="todo-text">待处理售后订单</div>
                        </div>
                    </div>
                    <div class="todo-row">
                        <div class="todo-type">
                            <i class="fa-solid fa-truck"></i> 采购
                        </div>
                        <div class="todo-item">
                            <div class="todo-number" id="todayPurchaseQuantity">0</div>
                            <div class="todo-text">今日采购数量</div>
                        </div>
                        <div class="todo-item">
                           <div class="todo-number" id="todayPurchaseAmount">0</div>
                            <div class="todo-text">今日采购金额</div>
                        </div>
                    </div>
                </div>

                <div class="content-wrapper">
                    <div class="left-panel">
                        <div class="tab-buttons">
                            <button onclick="showTab('plugin-log')" class="active">插件更新日志</button>
                            <button onclick="showTab('announcement')">公告消息</button>
                            <button onclick="showTab('live-training')">直播/培训</button>
                            <button onclick="showTab('ozon-news')">OZON 资讯</button>
                        </div>

                        <!-- 插件更新日志 -->
                        <div class="info-list active" id="plugin-log">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <dl class="dynamic-status">
                                        <dd>  <div>
                                                <p>插件 v1.9 版本更新，新增代发采购，可以采购PDD和1688商品自动同步订单状态</p>
                                                <span>2025-06-20</span>
                                            </div>
                                             </dd>
                                         <dd>  <div>
                                                <p>插件 v1.5.5 版本更新，新增自动利润计算</p>
                                                <span>2025-05-20</span>
                                            </div>
                                             </dd>
                                        <dd>  <div>
                                                <p>插件 v1.3 版本更新，新增采集箱，可采集编辑ozon和1688京东pdd淘宝等</p>
                                                <span>2025-05-09</span>
                                            </div>
                                             </dd>
                                             <dd>
                                            <div>
                                                <p>插件 v1.1 版本更新，修复了部分兼容性问题</p>
                                                <span>2025-04-19</span>
                                            </div>
                                        </dd>
                                        <dd>
                                            <div>
                                                <p>插件 v1.0 版本发布，新增了若干功能</p>
                                                <span>2025-04-15</span>
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <!-- 公告消息 -->
                        <div class="info-list" id="announcement">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <dl class="dynamic-status">
                                        <dd>
                                            <div>
                                                <p>功能持续升级更新中</p>
                                                <span>2025-07-20</span>
                                            </div>
                                        </dd>
                                        <dd>
                                            <div>
                                                <p>正式版发布，功能持续更新中</p>
                                                <span>2025-07-15</span>
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <!-- 直播/培训 -->
                        <div class="info-list" id="live-training">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <dl class="dynamic-status">
                                        <dd>
                                            <div>
                                                <p>下周五 14:00 将举办新功能培训直播，欢迎参加</p>
                                                <span>2025-05-12</span>
                                            </div>
                                        </dd>
                                        <dd>
                                            <div>
                                                <p>上周的直播培训回放已上传，可在指定位置查看</p>
                                                <span>2025-05-08</span>
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <!-- OZON 资讯 -->
                        <div class="info-list" id="ozon-news">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <dl class="dynamic-status">
                                        <dd>
                                            <div>
                                                <p>OZON 平台推出新的营销工具，助力商家提升销量</p>
                                                <span>2024-01-15</span>
                                            </div>
                                        </dd>
                                        <dd>
                                            <div>
                                                <p>OZON 调整了部分类目佣金政策，请商家关注</p>
                                                <span>2024-01-13</span>
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="right-panel">
                        <div class="product-steps">
                            <h2>上品步骤</h2>
                            <ol>
                                <li>授权店铺 <a href="https://v.douyin.com/qPzYAajJLF4/">授权教程 ></a> <ul>点击左侧任务栏-店铺管理</ul></li>
                               
                                <li>下载插件 <a href="../hterp.zip">点击下载 ></a><ul>最新版本</ul></li>
                                <li>上架商品 <a href="https://v.douyin.com/qPzYAajJLF4/">视频教程 ></a><ul>支持插件跟卖，数据分析，1688PDD等采集</ul></li>
                               
                            </ol>
                        </div>
                    </div>
                </div>
 <script>
        // 确保layui正确加载
        layui.use(['jquery', 'layer', 'element'], function(){
            var $ = layui.jquery;
            var layer = layui.layer;
            var element = layui.element;

            // 获取待办事项数据
            function loadTodoMetrics() {
                const requestUrl = '/user/ajax.php?act=todo_metrics';

                $.ajax({
                    url: requestUrl,
                    type: 'GET',
                    dataType: 'json',
                    beforeSend: function() {
                        $('#today-purchase-quantity').html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate"></i>');
                        $('#todayPurchaseAmount').html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate"></i>');
                        $('.todo-row .todo-number').each(function() {
                            $(this).html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate"></i>');
                        });
                    },
                    success: function(res) {
                        if(res.code === 0 && res.data) {
                    // 更新采集箱数量
        $('#collectionBox').text(res.data.collectionBox || 0); // 采集箱数量

                            // 更新今日发布成功和失败（假设数据接口有这两个字段）
                            $('#todayPublishSuccess').text(res.data.todayPublishSuccess || 0); // 今日发布成功数量
                            $('#todayPublishFail').text(res.data.todayPublishFail || 0); // 今日发布失败数量

                            // 更新今日采购数量和金额
                            const qty = parseInt(res.data.todayPurchaseQuantity) || 0;
                            $('#todayPurchaseQuantity').text(qty); // 今日采购数量

                            const amount = parseFloat(res.data.todayPurchaseAmount) || 0;
                            $('#todayPurchaseAmount').html(`¥${amount.toFixed(2)}`); // 今日采购金额

                            // 更新其他指标
                            $('#remainingDelivery').text(res.data.remainingDelivery || 0); // 剩余发货小于1天
                            $('#delayedShipment').text(res.data.delayedShipment || 0); // 超时订单
                            $('#pendingPurchase').text(res.data.pendingPurchase || 0); // 待采购
                            $('#waitingShipment').text(res.data.waitingShipment || 0); // 等待发货
                            $('#inTransit').text(res.data.inTransit || 0); // 运输中
                            $('#pendingAfterSales').text(res.data.pendingAfterSales || 0); // 待处理售后订单
                        } else {
                            console.error('Failed to load todo metrics:', res.msg);
                            $('#todayPurchaseQuantity').text('0');
                            $('#todayPurchaseAmount').text('¥0.00');
                            $('.todo-number').each(function() {
                                $(this).text('0');
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading todo metrics:', {
                            status: status,
                            error: error,
                            response: xhr.responseText
                        });
                        $('#today-purchase-quantity').text('0');
                        $('#todayPurchaseAmount').text('¥0.00');
                        $('.todo-row .todo-number').each(function() {
                            $(this).text('0');
                        });
                    }
                });
            }

            // 等待DOM加载完成
            $(function(){
                loadTodoMetrics();
                // 每2分钟刷新数据
                setInterval(loadTodoMetrics, 80000);
            });
        });

        // 切换信息列表
        function showTab(tabId) {
            const tabs = document.querySelectorAll('.info-list');
            const buttons = document.querySelectorAll('.tab-buttons button');

            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            buttons.forEach(button => {
                button.classList.remove('active');
            });

            document.getElementById(tabId).classList.add('active');
            document.querySelector(`.tab-buttons button[onclick*="${tabId}"]`).classList.add('active');
        }
    </script>
</div>
    
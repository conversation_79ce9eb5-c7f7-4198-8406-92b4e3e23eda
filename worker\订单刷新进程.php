<?php
require_once __DIR__ . '/../includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 2. 创建消息队列连接
$connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
$channel = $connection->channel();

// 定义队列名称
$queueName = 'ozon_order_processing';
$channel->queue_declare($queueName, false, true, false, false);

// 3. 生产者脚本 - 将订单放入队列
function enqueueOrders($DB, $channel, $queueName) {
    $time = time();
    $sql = "SELECT A.*, B.ClientId, B.key, B.apistatus 
            FROM ozon_order A 
            LEFT JOIN ozon_store B ON A.storeid = B.id 
            WHERE 
                (A.time < '{$time}' OR A.status IN ('awaiting_packaging', 'awaiting_deliver'))
                AND A.status NOT IN ('cancelled', 'cancelled_from_split_pending', 'delivered')
                AND B.apistatus = 1 
            ORDER BY 
                CASE 
                    WHEN A.status = 'awaiting_packaging' THEN 0
                    WHEN A.status = 'awaiting_deliver' THEN 1
                    ELSE 2
                END,
                A.time ASC 
            LIMIT 100";
    
    $rs = $DB->getAll($sql);
    
    foreach ($rs as $row) {
        // 将订单数据序列化为JSON
        $messageData = json_encode($row);
        $msg = new AMQPMessage(
            $messageData,
            ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
        );
        
        // 发布消息到队列
        $channel->basic_publish($msg, '', $queueName);
        
        // 立即更新订单时间，避免重复处理
        $DB->update('order', ['time' => time() + 3600], ['id' => $row['id']]);
    }
    
    return count($rs);
}

// 4. 消费者脚本 - 处理队列中的订单
function processOrder($msg, $DB, $Raconfig) {
    $row = json_decode($msg->body, true);
    $importer = new \lib\JsonImporter($DB, $Raconfig);
    
    try {
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key'], true);
        $json = $client->postingfbsget($row['posting_number']);
        
        if (empty($json)) {
            $msg->ack();
            return;
        }
        
        // 处理API错误
        if ($json['code'] == 7) {
            $store = [];
            if ($json['message'] == 'Company is blocked, please contact support') {
                $store['apistatus'] = 3; // 公司已封锁，请联系支持
            } elseif ($json['message'] == 'Api-key is deactivated, use another one or generate a new one') {
                $store['apistatus'] = 2; // Api-key 已失效
            }
            $DB->update('store', $store, ['ClientId' => $row['ClientId']]);
            $DB->update('order', ['time' => time() + 3600], ['id' => $row['id']]);
            $msg->ack();
            return;
        }
        
        if ($json['code']) {
            $DB->update('order', ['time' => time() + 3600], ['id' => $row['id']]);
            $msg->ack();
            return;
        }
        
        $status = $json['result']['status'];
        $data = [];
        
        // 根据订单状态设置下次处理时间
        if ($row['status'] == 'awaiting_packaging') {
            $data['time'] = time() + rand(3600, 7200);
        } elseif ($row['status'] == 'awaiting_deliver') {
            $data['time'] = time() + rand(14400, 21600);
        } elseif ($row['status'] == 'delivering') {
            if (getShippingMethod($row['tpl_provider'])['speedtx'] == 'Express') {
                $data['time'] = (getDaysBetweenDates($row['in_process_at'], date('Y-m-d')) > 8) 
                    ? time() + 86400 
                    : time() + rand(86400, 172800);
            } else {
                $data['time'] = (getDaysBetweenDates($row['in_process_at'], date('Y-m-d')) > 10) 
                    ? time() + 86400 
                    : time() + rand(86400, 172800);
            }
        } else {
            $data['time'] = time() + rand(21600, 43200);
        }
        
        // 处理面单下载
        if ($row['packagelabel'] == 0 && $row['status'] == 'awaiting_deliver') {
            if ($client->packagelabel($row)) {
                $data['packagelabel'] = 1;
            }
        }
        
        // 如果状态有变化，导入新订单数据
        if ($status != $row['status']) {
            $importer->importorder($json, false, $row);
        }
        
        // 更新订单数据
        $DB->update('order', $data, ['posting_number' => $row['posting_number']]);
        
        // 确认消息已处理
        $msg->ack();
    } catch (Exception $e) {
        // 记录错误并重新入队
        error_log("处理订单失败: " . $e->getMessage());
        $msg->nack(true); // 重新入队
    }
}

// 5. 根据命令行参数决定运行生产者还是消费者
if (isset($argv[1]) && $argv[1] == 'producer') {
    // 生产者模式
    $count = enqueueOrders($DB, $channel, $queueName);
    echo "已添加 {$count} 个订单到处理队列\n";
} else {
    // 消费者模式
    echo "等待处理订单...\n";
    
    $callback = function ($msg) use ($DB, $Raconfig) {
        processOrder($msg, $DB, $Raconfig);
    };
    
    // 设置公平分发
    $channel->basic_qos(null, 1, null);
    $channel->basic_consume($queueName, '', false, false, false, false, $callback);
    
    while ($channel->is_consuming()) {
        $channel->wait();
    }
}

// 关闭连接
$channel->close();
$connection->close();
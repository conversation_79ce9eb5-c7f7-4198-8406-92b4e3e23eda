<?php
include("../includes/common.php");
?>
<link rel="stylesheet" href="../assets/css/order.css">

<!-- 美化样式 -->
<button id="layuiBackToTop" class="layui-btn layui-btn-danger layui-btn-radius"
    style="position: fixed; right: 20px; bottom: 70px; z-index: 99999; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
    <i class="layui-icon layui-icon-top" style="font-size: 38px; color: #fff;"></i>
</button>

<!-- 勾选数量提示 -->
<div id="selectedCountTip" class="layui-table-grid-selected">
    0勾选
</div>

<style>
    .layui-table-grid-selected {
        position: fixed;
        bottom: 15px;
        right: 15px;
        background: #1E9FFF;
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        z-index: 10000;
        font-size: 13px;
        display: none;
    }

    /* 黑名单相关样式 */
    .add-blacklist-btn {
        background: #909399 !important;
        border-color: #909399 !important;
        transition: all 0.3s ease;
    }

    .add-blacklist-btn:hover {
        background: #73767a !important;
        border-color: #73767a !important;
        transform: scale(1.05);
    }

    .remove-blacklist-btn {
        background: #4CAF50 !important;
        border-color: #4CAF50 !important;
        transition: all 0.3s ease;
    }

    .remove-blacklist-btn:hover {
        background: #45a049 !important;
        border-color: #45a049 !important;
        transform: scale(1.05);
    }

    .blacklist-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    /* 工具栏冻结样式 */
    .layui-table-tool-frozen {
        position: sticky !important;
        top: 0 !important;
        z-index: 999 !important;
        background: #fff !important;
        border-bottom: 1px solid #e6e6e6 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        padding: 10px !important;
        margin: 0 !important;
    }

    /* 确保表格工具栏在滚动时保持在顶部 */
    .layui-table-tool {
        position: sticky !important;
        top: 0 !important;
        z-index: 999 !important;
        background: #fff !important;
        border-bottom: 1px solid #e6e6e6 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }
    /* 黑名单订单卡片样式 */
    .blacklist-order-card {
        background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
        border: 2px solid #f44336 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2) !important;
        margin: 8px 0 !important;
        position: relative !important;
    }

    .blacklist-order-card::before {
        content: "⚠️ 黑名单客户";
        position: absolute;
        top: -8px;
        right: 10px;
        background: #f44336;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .blacklist-order-card .dd {
        background: transparent !important;
        border-radius: 6px !important;
    }

    .blacklist-order-card .dd-b {
        background: rgba(255, 255, 255, 0.8) !important;
        border-radius: 4px !important;
        padding: 8px !important;
        margin-bottom: 5px !important;
    }

    .blacklist-order-card .ee {
        background: rgba(255, 255, 255, 0.9) !important;
        border-radius: 4px !important;
        border: 1px solid rgba(244, 67, 54, 0.3) !important;
    }
</style>

<!-- 订单状态导航 -->
<div class="order-page">
    <div style="padding: 10px;">
        <div class="layui-card">
            <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <form class="layui-form layui-form-pane" lay-filter="orderForm">
                        <div class="layui-inline">
                            <input type="text" name="text" placeholder="SKU/货件编号/运号/采购单号/快递单号/标题(双语)"
                                class="layui-input" autocomplete="off" style="width: 300px;">
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 80px;">
                                <input type="text" name="money" placeholder="最低价" class="layui-input"
                                    autocomplete="off">
                            </div>
                            <div class="layui-input-inline" style="width: 80px;">
                                <input type="text" name="moneys" placeholder="最高价" class="layui-input"
                                    autocomplete="off">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 150px;">
                                <select name="storeid" lay-search id="sellerSelect">
                                    <option value="">所有店铺</option>
                                </select>
                            </div>
                        </div>
                        <!-- 新增分组筛选 -->
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 150px;">
                                <select name="groupid" lay-search id="groupSelect">
                                    <option value="">所有分组</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 150px;">
                                <select name="paypt" lay-search id="采购">
                                    <option value="">所有平台采购</option>
                                    <option value="pdd">拼多多</option>
                                    <option value="1688">1688</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: 50px;">日期</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" class="layui-input" name="date1" id="date1" placeholder="yyyy-MM-dd">
                            </div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" class="layui-input" name="date2" id="date2" placeholder="yyyy-MM-dd">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 150px;">
                                <select name="cancelled" lay-search id="">
                                    <option value="">不过滤取消订单</option>
                                    <option value="yes">过滤取消订单</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 150px;">
                                <select name="courierNumberStatus" lay-search>
                                    <option value="">快递单号</option>
                                    <option value="empty">为空</option>
                                    <option value="not_empty">不为空</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                            <button type="reset" class="layui-btn layui-btn-primary" lay-filter="reset">重置</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-tab layui-tab-brief" lay-filter="orderTab">
                <!-- 在 layui-tab 的同一层级添加搜索区域 -->
                <ul class="layui-tab-title">
                    <li class="layui-this">
                        待处理
                        <span class="order-count-badge" id="count-pending">0</span>
                    </li>
                    <li>
                        等待发货
                        <span class="order-count-badge" id="count-awaiting_deliver">0</span>
                    </li>
                    <li>
                        交运平台
                        <span class="order-count-badge" id="count-awaiting_deliver2">0</span>
                    </li>
                    <li>
                        运输中
                        <span class="order-count-badge" id="count-delivering">0</span>
                    </li>
                    <li>
                        已送达
                        <span class="order-count-badge" id="count-delivered">0</span>
                    </li>
                    <li>
                        已取消
                        <span class="order-count-badge" id="count-cancelled">0</span>
                    </li>
                    <li>
                        全部订单
                        <span class="order-count-badge" id="count-all">0</span>
                    </li>

                </ul>

                <!-- 待处理子标签 -->
                <div class="sub-tabs" id="pendingSubTabs" style="display: none;">
                    <ul class="layui-tab-title">
                        <li class="layui-this">
                            全部
                            <!--span class="order-count-badge" id="count-pending">0</span-->
                        </li>
                        <li>
                            未采购
                            <span class="order-count-badge" id="count-not_purchased">0</span>
                        </li>
                        <li>
                            已采购
                            <span class="order-count-badge" id="count-purchased">0</span>
                        </li>
                        <li>
                            未上传护照
                            <span class="order-count-badge" id="count-awaiting_verification">0</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 订单表格区域 -->
            <div class="layui-col-md12">
                <table id="orderTable" lay-filter="orderTable"></table>
            </div>
        </div>
    </div>
</div>
<!-- Layui 脚本 -->
<!-- 状态导航与表格结构保持不变 -->
<script type="text/html" id="html">
<div id="{{d.posting_number}}" {{# if(d.is_blacklisted){ }}class="blacklist-order-card"{{# } }}>
  <!--表格上开始-->  
  <div class="dd" style="margin-top:-10px;">
    <div class="dd-b">
       <div class="layui-form" style="display: inline-block; margin-right: 5px;">
          <input type="checkbox" class="order-checkbox" data-posting_number="{{d.posting_number}}" data-status="{{d.status}}" data-packing_status="{{d.packing_status}}" lay-skin="primary" lay-filter="orderCheckbox">
       </div>
       <span class="ss sskuang">OZON</span>
       地区：<span style="color: #31bdec;">{{d.country}}</span>
          <span id="orderInfo" class="sa">订单:{{d.posting_number}}</span>
          <a><i class="fas fa-copy" data-posting_number="{{d.posting_number}}"></i></a>
      {{# if(d.status === 'awaiting_verification'){ }}
        <span class="ss"> 同步状态：<span class="hong">⚠️⚠️⚠️未上传护照(禁止发货)!!!</span></span>
      {{# } else if(d.status === 'awaiting_packaging'){ }}
        <span class="ss"> 同步状态：<span  style="color: #666;">等待备货</span></span>
      {{# } else if(d.status === 'awaiting_deliver' && d.packing_status === 1){ }}
        <span class="ss"> 同步状态：<span  style="color: #e67e22;">交运平台</span></span>
      {{# } else if(d.status === 'awaiting_deliver'){ }}
        <span class="ss"> 同步状态：<span  style="color: #e67e22;">等待发货</span></span>
      {{# } else if(d.status === 'delivering'){ }}
        <span class="ss"> 同步状态：<span  style="color: #2980b9;">运输中</span></span>
      {{# } else if(d.status === 'delivered'){ }}
        <span class="ss"> 同步状态：<span  style="color: #27ae60;">已送达</span></span>
      {{# } else if(d.status === 'cancelled'){ }}
        <span class="ss"> 同步状态：<span  style="color: #c0392b;">⚠️⚠️⚠️已取消</span></span>
      {{# } else if(d.status === 'awaiting_registration'){ }}
        <span class="ss"> 同步状态：<span  style="color: #8e44ad;">移交给快递</span></span>
      {{# } else if(d.status === 'cancelled_from_split_pending'){ }}
        <span class="ss"> 同步状态：<span  style="color: #c0392b;">因货件拆分而取消</span></span>
      {{# } else { }}
        <span class="ss"> 同步状态：<span  style="color: #2ec770;">{{d.statusmsg}}</span></span>
      {{# } }}
        <span class="ss">买家下单：<span class="hui">{{d.in_process_at}}</span></span>
        <span class="ss">逾期出货：<span class="hong">{{d.shipment_date}}</span></span>
        <span class="ss">截止出货：<span class="hong">{{d.out_date}}</span></span>
        <span class="ss">订单状态：{{ d.cost ? '完成采购':'待采购'}}
      {{# if(d.status=='awaiting_deliver'||d.status=='delivering'){ }}
          <img src="/assets/img/sheet.png" 
               class="preview" 
               style="width: 20px; vertical-align: middle; margin-left: 5px; cursor: pointer;"
               data-posting_number="{{d.posting_number}}">
      {{# } }}
        <span class="ss">打包状态：{{ d.packing_status==0 ? '未打包':'已经打包'}}
      </span>
    </div>
  </div>
  <!--表格上结束-->
  <!--表格下开始-->
  <div class="ee">
    <table width="100%" border="0" class="tab">
      <tbody>
        <tr style="min-height: 100px; height: auto;">
          <td style="width: 40%;">
            <div class="yy">
              {{# if(d.has_multiple_products === true){ }}
              <div style="margin-bottom: 15px; overflow: visible;">
                <div style="font-weight: bold; margin-bottom: 10px; color: #FF5722; font-size: 14px;">该订单包含 {{d.products_info.length}} 个商品:</div>
                
                {{# layui.each(d.products_info, function(index, item){ }}
                <div class="product-item" style="display: flex; margin-bottom: 15px; background-color: #f9f9f9; border-radius: 4px; padding: 10px; border: 1px solid #eee;">
                  <div class="product-image" style="width: 80px; margin-right: 15px;">
                    <img src="{{ item.image ? item.image : '../assets/img/syncing.png'}}" data-rel="{{item.image}}" class="tu thumbnail" style="width: 80px; height: 80px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px;">
                  </div>
                  <div class="product-info" style="flex: 1; display: flex; flex-direction: column; justify-content: space-between;">
                    <div>
                      <div  class="eeb-t" style="font-weight: bold; margin-bottom: 5px; color: #333; max-width: 450px;">商品{{index+1}}: {{item.name}}</div>
                      <div class="line" style="margin-bottom: 3px;">SKU: <a href="https://www.ozon.ru/product/{{item.sku}}/" target="_blank">{{item.sku}}</a> <a><i class="fas fa-copy" data-sku="{{item.sku}}"></i></a></div>
                      {{# if(item.attributes && item.attributes.color){ }}
                      <div class="line" style="margin-bottom: 3px;">颜色: {{item.attributes.color}}</div>
                      {{# } else if(d.color && index === 0){ }}
                      <div class="line" style="margin-bottom: 3px;">颜色: {{d.color}}</div>
                      {{# } }}
                      <div class="line" style="margin-bottom: 3px;">数量: {{item.quantity}} | 单价: {{item.price}} {{item.currency_code}}</div>
                      {{# if(item.dimensions && item.dimensions.width){ }}
                      <div class="line" style="margin-bottom: 3px;">尺寸: {{item.dimensions.width}}x{{item.dimensions.height}}x{{item.dimensions.length}}mm | 重量: {{item.dimensions.weight/1000}}kg</div>
                      {{# } }}
                    </div>

                  </div>
                </div>
                {{# }); }}
                
                <div class="procurement-container">
                  <button id="showModal" data-posting_number="{{d.posting_number}}" class="procurement-btn purchase">
                    <i class="layui-icon layui-icon-cart-simple"></i>代发采购
                  </button>
                  <button class="btn-inventory-multi procurement-btn inventory" data-posting_number="{{d.posting_number}}">
                    <i class="layui-icon layui-icon-component"></i>调用库存
                  </button>
                </div>
              </div>
              {{# } else { }}
              <img src="{{ d.primary_image ? d.primary_image : '../assets/img/syncing.png'}}" data-rel="{{d.primary_image}}" loading="lazy" class="tu thumbnail">
              
              <div class="ee-b">  
                <a href="https://www.ozon.ru/product/{{d.sku}}/" target="_blank">
                  <div style="display: flex; align-items: center;">
                    <div class="eeb-t" id="title_chinese_{{d.posting_number}}" title="{{d.order_name}}" style="color: #1E9FFF;">{{d.order_name??d.name2}}</div>
                    <div class="eeb-t" id="title_russian_{{d.posting_number}}" title="{{d.name2}}" style="color: #1E9FFF; display:none;">{{d.name2}}</div>
                    {{# if(d.quantity>=2){ }}
                        <span class="quantity-tag" style="background: #ff5252;">X{{d.quantity}}</span>
                    {{# } }}
                    
                  </div>
                </a>
                <div class="column-item-value">{{d.pj}}  </div>
                
                <div class="line">Sku：<a href="https://www.ozon.ru/product/{{d.sku}}/" target="_blank">{{d.sku}}</a>  <a><i class="fas fa-copy" data-sku="{{d.sku}}"></i></a></div>
                 <div class="line">offer_id：{{d.offer_id}}  <a><i class="fas fa-copy" data-offer_id="{{d.offer_id}}"></i></a></div>
                <div class="line">数量：＊ {{d.quantity}}</div>
                <div class="line">单价：{{d.money}}  <span> ￥</span>       </div>
                {{# if(!d.cost && d.status!='cancelled_from_split_pending' && d.status!='cancelled' && d.status!='awaiting_verification'){ }}
                <div class="procurement-container">
                  <button id="showModal" data-posting_number="{{d.posting_number}}" class="procurement-btn purchase">
                    <i class="layui-icon layui-icon-cart-simple"></i>代发采购
                  </button>
                  <button class="btn-inventory procurement-btn inventory" data-sku="{{d.sku}}" data-posting_number="{{d.posting_number}}">
                    <i class="layui-icon layui-icon-component"></i>调用库存
                  </button>
                </div>
                {{# } }}
              </div>
              {{# } }}
              <div class="both"></div>
            </div>
            {{# if(d.customer_name && d.region){ }}
            <hr>
            <div class="ee-c text-ellipsis-nume">
                姓  名：{{d.customer_name?d.customer_name:''}}  
                {{# if(d.customer_name){ }}
                {{# if(d.is_blacklisted){ }}
                <i class="layui-icon layui-icon-face-cry blacklist-icon view-blacklist-history" 
                   style="color: #000000; margin-left: 5px; font-size: 16px; cursor: pointer;" 
                   title="点击查看黑名单历史"
                   data-customer_name="{{d.customer_name}}"></i>
                <button class="layui-btn layui-btn-xs layui-btn-normal remove-blacklist-btn" 
                        data-customer_name="{{d.customer_name}}" 
                        data-posting_number="{{d.posting_number}}"
                        title="移除黑名单" 
                        style="margin-left: 3px;">
                    <i class="layui-icon layui-icon-ok"></i>
                </button>
                {{# } else { }}
                <button class="layui-btn layui-btn-xs layui-btn-danger add-blacklist-btn" 
                        data-posting_number="{{d.posting_number}}" 
                        data-customer_name="{{d.customer_name}}" 
                        title="添加到黑名单" 
                        style="margin-left: 5px;">
                    <i class="layui-icon layui-icon-close"></i>
                </button>
                {{# } }}
                {{# } }}
                省州/邮编：{{d.region}}
            </div>
            {{# } }}
          </td>
          <td style="width: 25%;text-align: left;padding-left: 10px;">
            {{# if(d.purchase_orderSn && d.purchase_type){ }}
            <div class="ee-c .purchase_orderSn">采购订单号：<span>{{d.purchase_orderSn}} <i class="fas fa-copy" data-purchase_order="{{d.purchase_orderSn}}"></i></span></div>
            <div class="ee-c">采购平台：<span>{{d.purchase_type=='pdd'?'拼多多':'1688'}} </span></div>
            <div class="ee-c">采购状态：<span>
                {{# if(d.purchase_ok==0){ }} 
                    等待付款
                {{# }else if(d.purchase_ok==1){ }}
                    待发货
                {{# }else if(d.purchase_ok==2){ }}
                    待收货
                {{# }else if(d.purchase_ok==3){ }}
                    已签收
                {{# }else if(d.purchase_ok==6){ }}
                    交易已取消
                {{# } }}
                </span><a><i class="layui-icon layui-icon-refresh" data-posting_number="{{d.posting_number}}" data-paytype="{{d.purchase_type}}" data-order-sn="{{d.purchase_orderSn}}"></i></a>
            </div>
            {{# } if(d.cost) { }}
            <div class="ee-c">
                  <button class="layui-btn layui-btn-xs cancel-purchase-btn" data-posting_number="{{d.posting_number}}" style="background:rgb(134, 134, 161); border: none; color: white; padding: 2px 8px; margin-left: 10px; border-radius: 3px; font-size: 11px; opacity: 0.8;">
                    <i class="layui-icon layui-icon-close" style="font-size: 10px; margin-right: 2px;"></i>取消关联
                </button>
            </div>
            {{# } if(d.purchase_ok==2||d.courierNumber!=null){ }}
                <!--<div class="ee-c">快递公司：<span>{{d.purchase_kdname}} </span></div>-->
                {{# if(d.courierNumber){ }}<div class="ee-c">快递单号：<span>{{d.courierNumber}} </span><i class="fas fa-copy" data-courierNumber="{{d.courierNumber}}"></i></div>{{# } }}
                
                {{# if(d.purchase_lus){ }}<div class="text-ellipsis" style="color: #1E9FFF;">{{d.purchase_lus}}</div> {{# } }}
            {{# } }}
            {{# if(d.OrderNotes && d.OrderNotes.trim()){ }}
            <div class="ee-c order-notes-display" id="notes-{{d.posting_number}}" style="background: #f0f9ff; border-left: 3px solid #1E9FFF; padding: 8px; margin-top: 5px; border-radius: 3px;">
                <i class="layui-icon layui-icon-note" style="color: #1E9FFF; margin-right: 5px;"></i>
                <span style="color: #666; font-size: 12px;">备注：</span>
                <span class="notes-text" style="color: #333; font-size: 12px; word-break: break-all;">{{d.OrderNotes}}</span>
            </div>
            {{# } else { }}
            <div class="ee-c order-notes-display" id="notes-{{d.posting_number}}" style="display:none;"></div>
            {{# } }}
          </td> 
          <td style="width: 15%;text-align: left;padding-left: 10px;"> <div class="ee-c"></div>
            <div class="ee-c">产品总价：<span>{{d.price}}  <span> ￥</span>       </span>   </div>
            <div class="ee-c wuliu_info wuliu_data" title="{{d.cost}}" data-info="{{d.cost}}">采购成本：{{d.cost?d.cost:'待采购'}} <span> ￥</span> </div>
            <div class="ee-c">后台/出库：{{d.weight}} /{{d.out_weight}} kg</div>
            <div class="ee-c"> 类目佣金：{{d.percent}} %</div>
            <div class="ee-c"> 物流/佣金：{{d.delivery}}/{{d.commissions}} </div>
            <div class="ee-c store_names" style="color: #1E9FFF;">预估利润：{{d.profit}} <span> ￥</span> </div> 
            {{# if(d.cost){ }}
                <div class="ee-c">成本利润：{{d.costprofit}} <span> %</span> </div>
                {{# if(d.bubble){ }}
                    <div class="ee-c store_names" style="color: #ff001a;">注意产品超抛!!!注意调整!!!</div> 
                {{# }}}
            {{# }}}
          </td> 
          <td style="width: 15%;text-align: left;padding-left: 10px;">
            <div class="ee-c store_names">店铺：{{d.storename}}</div>
            <div class="ee-c">备货仓库：{{d.warehouse}}</div>
            <div class="ee-c">运号：<a href="https://tracking.ozon.ru/?track={{ d.tracking_number }}&local=zh-Hans" target="_blank">{{ d.tracking_number ? d.tracking_number:'未申请备货' }}</a><a><i class="fas fa-copy" data-tracking_number="{{d.tracking_number}}"></i></a></div>
            <div class="ee-c wuliu_info wuliu_data" title="{{d.tpl_provider}}" data-info="{{d.tpl_provider}}">物流：{{d.tpl_provider}}</div>
                           <div class="ee-c">物流方式：{{d.wl ? d.wl:'未申请'}}      {{d.speed ? d.speed:'未申请'}}</div>
            <div class="ee-c">总数：<a href="javascript:void(0);" class="{{d.quantity>1?`hong`:``}}">{{d.quantity}}个</a></div>
            <div class="ee-c">下次更新：{{d.time}}</div>
             {{# if(d.print_time){ }}
            <div class="ee-c">打印时间：{{d.print_time}}</div>
            {{# } }}
          </td>  
          <td class="order_btn_td" style="width: 5%;">
             <div class="order_btn_div order_purchase" data-order_id="{{d.order_id}}" data-posting_number="{{d.posting_number}}" id="hong">采购信息</div>  
            <div class="order_btn_div order_status" data-posting_number="{{d.posting_number}}">同步订单</div>
            {{# if(d.status === 'awaiting_packaging'){ }}
            <div class="order_btn_div Stocking_order" data-posting_number="{{d.posting_number}}">申请备货</div>
            {{# } }}
            <div class="order_btn_div order_notes" data-posting_number="{{d.posting_number}}">
                订单备注
                {{# if(d.OrderNotes && d.OrderNotes.trim()){ }}
                <i class="layui-icon layui-icon-about" style="color: #FF5722; margin-left: 3px; font-size: 12px;" title="已有备注"></i>
                {{# } }}
            </div>
            {{# if(d.status === 'awaiting_deliver'){ }}
            <div class="order_btn_div order_self_only" data-posting_number="{{d.posting_number}}" style="background: linear-gradient(135deg, #1E9FFF 0%, #0984e3 100%); color: white;">一键打印面单</div>
            {{# } }}
          </td> 
        </tr> 
      </tbody>
    </table> 
  </div>
</div> 
</script>
<script type="text/html" id="toolbarOrde">
  <div class="layui-btn-container layui-table-tool-frozen" style="display: flex; align-items: center;">
    <div class="layui-form" style="display: inline-flex; align-items: center; margin-right: 10px;">
      <input type="checkbox" id="selectAll" lay-skin="primary" title="全选" lay-filter="selectAll">
    </div>
    <button class="layui-btn layui-btn-sm layui-bg-blue" id="updata">同步订单</button>
    <button class="layui-btn layui-btn-sm" lay-event="getCheckData">批量打印面单</button>
    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="exportOrders">导出订单</button>
    <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batch_order_package">批量等待发货</button>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchShipClick">批量交运</button>
    <button class="layui-btn layui-btn-sm layui-bg-purple" >1688授权</button>
    <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="SynchronizePinduoduoorders">同步拼多多物流</button>
    <button class="layui-btn layui-btn-sm" lay-event="Synchronize1688orders">同步1688物流</button>
    <div style="display: inline-flex; align-items: center; margin-left: 10px;">
      <span style="margin-right: 5px;">采购自动备货</span>
      <input type="checkbox" name="auto_close" lay-skin="switch" <?php if($userrow['package'] == 1){ ?> checked <?php } ?>lay-filter="user_auto_close_switch">
    </div>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchSetPurchase">批量设置采购信息</button>
    <button id="toggleTitleBtn" class="layui-btn layui-btn-sm layui-btn-primary" style="margin-left: 10px;">切换到俄文标题</button>
    <button id="sortOrderTimeBtn" class="layui-btn layui-btn-sm layui-btn-primary" title="按下单时间降序" style="margin-left: 10px;"><i class="layui-icon layui-icon-down"></i>下单时间</button>
  </div>
</script>
<!--<script src="/assets/js/jquery-3.6.0.min.js"></script>-->
<!--<script src="./order.js?t=<?php echo time();?>"></script>-->
<script>

    layui.use(['table', 'layer', 'jquery', 'form', 'laydate', 'element'], function () {
        const table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var element = layui.element;

        $('#layuiBackToTop').on('click', function () {
            // 直接获取pear-page作为滚动容器
            var scrollContainer = document.querySelector('.pear-page');

            if (scrollContainer && scrollContainer.scrollTop > 0) {
                scrollContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
            return false;
        });

        // 渲染日期选择器
        laydate.render({
            elem: '#date1',
            format: 'yyyy-MM-dd' // 可自定义格式
        });
        laydate.render({
            elem: '#date2',
            format: 'yyyy-MM-dd' // 可自定义格式
        });
        // 勾选数量显示
        function updateSelectedCount() {
            var count = $('.order-checkbox:checked').length;
            var $tip = $('#selectedCountTip');
            if (count > 0) {
                $tip.text(count + '勾选').show();
            } else {
                $tip.hide();
            }
        }
        window.updateSelectedCount = updateSelectedCount;

        // 工具：设置备注到DOM
        function setOrderNotesInDOM(posting_number, notes) {
            var $wrap = $('#notes-' + posting_number);
            if ($wrap.length === 0) return;
            if (notes && notes.trim()) {
                // 如果是隐藏占位，填充结构
                if ($wrap.find('.notes-text').length === 0) {
                    $wrap.html('<i class="layui-icon layui-icon-note" style="color:#1E9FFF;margin-right:5px;"></i>' +
                        '<span style="color:#666;font-size:12px;">备注：</span>' +
                        '<span class="notes-text" style="color:#333;font-size:12px;word-break:break-all;"></span>');
                    $wrap.css('background', '#f0f9ff').css('border-left', '3px solid #1E9FFF')
                        .css('padding', '8px').css('margin-top', '5px').css('border-radius', '3px');
                }
                $wrap.find('.notes-text').text(notes);
                $wrap.show();
            } else {
                $wrap.hide();
            }
        }

        // 初始化店铺下拉框
        initShopSelect();
        // 初始化分组下拉框
        initGroupSelect();
        function initGroupSelect() {
            $.ajax({
                url: 'ajax.php?act=getShopGroups',
                success: function (res) {
                    if (res.code === 0 && res.data) {
                        var $select = $('#groupSelect');
                        $select.empty();
                        $select.append('<option value="">所有分组</option>');
                        res.data.forEach(function (group) {
                            $select.append('<option value="' + group.id + '">' + group.name + '</option>');
                        });
                        layui.form.render('select');
                    }
                }
            });
        }
        var initOrderStatusCountsajax = 0;
        // 初始化订单状态统计
        function initOrderStatusCounts() {
            if (initOrderStatusCountsajax == 1) {
                return true;
            } else {
                initOrderStatusCountsajax = 1;
            }
            $.ajax({
                url: 'ajax_order.php?act=order_status_counts',
                type: 'GET',
                success: function (res) {
                    if (res.code === 0 && res.data) {
                        updateOrderStatusCounts(res.data);
                    }
                },
                error: function (xhr) {
                }
            });
            initOrderStatusCountsajax = 0;
        }

        // 更新订单状态统计数字
        function updateOrderStatusCounts(counts) {
            Object.keys(counts).forEach(function (status) {
                var count = counts[status] || 0;
                var $badge = $('#count-' + status);
                if ($badge.length > 0) {
                    $badge.text(count);
                    // 如果数量为0，可以选择隐藏徽章或显示0
                    if (count === 0) {
                        $badge.addClass('hidden');
                    } else {
                        $badge.removeClass('hidden');
                    }
                }

                // 更新筛选器数量
                var $filterCount = $('#filter-count-' + status);
                if ($filterCount.length > 0) {
                    $filterCount.text(count);
                }
            });
        }

        window.renderTableajax = 0;
        // 统一渲染配置
        const renderTable = function (status, text, money, moneys, storeid, date1, date2, groupid, sort_field, sort_order_param, paypt, cancelled) {
            // 如果提供了排序参数，则更新全局排序状态

            if (window.renderTableajax == 1) {
                return true;
            } else {
                window.renderTableajax = 1;
            }
            if (sort_order_param) {
                sort_order = sort_order_param;
            }
            // 按照用户要求，保持默认每页10条
            var limitNum = 20; // 默认每页10条
            var formData = form.val('orderForm');
            var courierFilter = formData.courierNumberStatus;
            table.render({
                elem: '#orderTable',
                url: 'ajax_order.php?act=orders_all',
                toolbar: '#toolbarOrde',
                defaultToolbar: ['exports', 'print'],
                request: {
                    timeout: 15000
                },
                loading: true,
                where: {
                    status,
                    text,
                    money,
                    moneys,
                    storeid,
                    date1,
                    date2,
                    groupid, // 添加分组ID参数
                    sort_field: sort_field || 'in_process_at', // 添加排序字段
                    sort_order: sort_order, // 使用全局排序变量
                    paypt,
                    cancelled,
                    courierFilter: courierFilter ?? ''
                },
                page: true,
                limit: 20, // 设置每页显示数量
                limits: [10, 20, 50, 100, 200, 500], // 可选每页数量
                skin: 'row',
                even: true,
                css: '.layui-table-cell{height:auto;}',
                cols: [[
                    { field: 'content', title: '', templet: '#html', width: '100%' }
                ]],
                done: function (res, curr, count) {
                    // 更新排序按钮状态
                    var $btn = $('#sortOrderTimeBtn');
                    if (sort_order === 'asc') {
                        $btn.attr('title', '按下单时间升序');
                        $btn.find('i').removeClass('layui-icon-down').addClass('layui-icon-up');
                    } else {
                        $btn.attr('title', '按下单时间降序');
                        $btn.find('i').removeClass('layui-icon-up').addClass('layui-icon-down');
                    }

                    // 重新渲染表单元素
                    form.render();

                    // 调试：检查返回的数据中是否包含黑名单信息
                    if (res.data && res.data.length > 0) {
                        console.log('订单数据示例:', res.data[0]);
                        var blacklistedCount = res.data.filter(function (item) {
                            return item.is_blacklisted == 1;
                        }).length;
                        console.log('黑名单订单数量:', blacklistedCount);
                    }

                    // 更新订单状态统计
                    initOrderStatusCounts();



                    // 确保当前状态下子标签的显示状态正确
                    if (currentStatus === 'not_purchased' || currentStatus === 'purchased' ||
                        currentStatus === 'awaiting_registration' || currentStatus === 'not_shipped' || currentStatus === 'awaiting_verification') {
                        $('#pendingSubTabs').show();

                        // 根据当前状态选中对应的子标签
                        if (currentStatus === 'not_purchased') {
                            $('#pendingSubTabs > .layui-tab-title li').removeClass('layui-this');
                            //$('#pendingSubTabs > .layui-tab-title li:eq(0)').addClass('layui-this');
                            $('#pendingSubTabs > .layui-tab-title li:eq(1)').addClass('layui-this');
                            $('#purchasedFilters').removeClass('show');
                        } else if (currentStatus === 'purchased' || currentStatus === 'awaiting_registration' || currentStatus === 'not_shipped') {
                            $('#pendingSubTabs > .layui-tab-title li').removeClass('layui-this');
                            // $('#pendingSubTabs > .layui-tab-title li:eq(1)').addClass('layui-this');
                            $('#pendingSubTabs > .layui-tab-title li:eq(2)').addClass('layui-this');
                            $('#purchasedFilters').addClass('show');

                            // 根据当前状态选中对应的筛选按钮
                            $('.filter-btn').removeClass('active');
                            if (currentStatus === 'purchased') {
                                $('.filter-btn[data-filter="all"]').addClass('active');
                            } else {
                                $('.filter-btn[data-filter="' + currentStatus + '"]').addClass('active');
                            }
                        }
                    }

                    // 全选按钮事件绑定
                    form.on('checkbox(selectAll)', function (data) {
                        var checked = data.elem.checked;
                        $('.order-checkbox').prop('checked', checked);
                        form.render('checkbox');
                        updateSelectedCount();
                    });

                    // 绑定动态生成的复选框事件（使用事件委托）
                    $(document).off('change', '.order-checkbox').on('change', '.order-checkbox', function () {
                        updateSelectedCount();
                    });

                    // 监听layui复选框变化
                    form.on('checkbox(orderCheckbox)', function (data) {
                        updateSelectedCount();
                    });

                    // 初始更新勾选数量
                    updateSelectedCount();

                    // 动态绑定图片点击事件（可选）
                    $('.thumbnail').off('click').on('click', function () {
                        const $thumbnail = $(this);
                        const imgSrc = $thumbnail.attr('data-rel') || $thumbnail.attr('src');
                        layer.photos({
                            photos: {
                                data: [{ src: imgSrc }]      // 使用Layer组件展示大图
                            },
                            anim: 5                     // 展开动画
                        });
                    });
                    $('.layui-icon-refresh').off('click').on('click', function () {
                        var type = 0;
                        var posting_number = $(this).data('posting_number'); // 获取行数据标识（假设数据中有唯一ID）
                        var paytype = $(this).data('paytype');
                        var orderSn = $(this).data('orderSn');
                        layer.load();
                        // 发送消息给插件
                        if(paytype=='pdd'){
                            function sendMessageToExtension(message) {
                              window.postMessage({
                                direction: "from-page-script",
                                message: message
                              }, "*");
                            }
                            sendMessageToExtension({
                                action: 'pddorder',
                                orderId: orderSn
                            });
                            
                            // 接收插件响应
                            window.addEventListener("message", (event) => {
                                if (event.data.direction === "from-content-script") {
                                    console.log("收到插件响应:", event.data.message);
                                    const apidata = event.data.message;
                                    if(apidata.data?.order_sn === orderSn) {
                                        setTimeout(() => {
                                            layer.closeAll();
                                            updateSingleOrder(posting_number);
                                        }, 1000); // 1000 milliseconds = 1 second
                                    }else if(apidata?.message){
                                        layer.closeAll();
                                        layer.msg(apidata.message, { icon: 2, time: 3000 });
                                        return ;
                                    }
                                }
                            });
                        }else{
                            $.ajax({
                                    url: 'ajax_order.php?act=updata_kd',
                                    type: 'POST',
                                    data: {posting_number:posting_number},
                                    dataType: 'json',
                                    success: function (res) {
                                        if(res.code === 0){
                                            layer.closeAll();
                                            updateSingleOrder(posting_number);
                                        }else{
                                            layer.closeAll();
                                            layer.msg(data.msg || '更新订单数据失败', { icon: 2 });
                                        }
                                        processed++;
                                        updateProgress();
                                        processNextOrder(index + 1,type);
                                    },
                                    error: function (xhr) {
                                        layer.closeAll();
                                    }
                                });
                        }
                    });
                    $('.layui-bg-purple').off('click').on('click', function () {
                        var auth = '<?=$userrow['1688auth']?>';

                        // 发送消息给插件
                        function sendMessageToExtension(message) {
                            layer.load();
                            window.postMessage({
                                direction: "from-page-script",
                                message: message
                            }, "*");
                        }
                        // 接收插件响应
                        window.addEventListener("message", (event) => {
                            if (event.data.direction === "from-content-script") {
                                console.log("收到插件响应:", event.data.message);
                                var data = event.data.message;
                                if (data.code == 0) {
                                    layer.msg('授权绑定成功', { icon: 1 });
                                } else if (data.code == 1) {
                                    layer.msg(data.msg || '授权绑定失败', { icon: 2 });
                                    window.open(data.url ? data.url : 'https://open.1688.com/user/register.htm', '_blank');
                                } else {
                                    layer.msg(data.msg || '授权绑定失败', { icon: 2 });
                                }
                                layer.closeAll();
                            }
                        });
                        if (auth) {
                            layer.confirm('你现在已经绑定，你确定更换账号授权?', {
                                icon: 3,
                                title: '1688授权',
                                btn: ['确定', '取消']
                            }, function (index) {
                                layer.close(index);
                                sendMessageToExtension({
                                    action: 'auth1688app'
                                });
                            });
                        } else {
                            sendMessageToExtension({
                                action: 'auth1688app'
                            });
                        }
                    });
                    // 在表格渲染的 done 回调中，修改复制功能部分
                    $('.fa-copy').off('click').on('click', function () {
                        // 定义可复制的字段列表
                        const copyFields = [
                            'posting_number',
                            'sku',
                            'couriernumber',
                            'offer_id',
                            'purchase_order',
                            'tracking_number'
                        ];

                        // 查找第一个有值的字段
                        let value = null;

                        for (const field of copyFields) {
                            const fieldValue = $(this).data(field);
                            if (fieldValue) {
                                value = fieldValue;
                                break;
                            }
                        }

                        if (!value) {
                            layer.msg("没有可复制的数据", { icon: 2 });
                            return;
                        }

                        // 创建临时输入框并复制
                        const tempInput = document.createElement('input');
                        tempInput.value = value;
                        document.body.appendChild(tempInput);
                        tempInput.select();
                        document.execCommand('copy');
                        document.body.removeChild(tempInput);

                        // 显示复制成功的提示
                        layer.msg("复制成功！");
                    });
                    $('#updata').on('click', function () {
                        var btn = $(this);

                        // 获取所有选中的订单
                        var checkboxes = document.querySelectorAll('.order-checkbox:checked');
                        var posting_numbers = [];
                        checkboxes.forEach(function (checkbox) {
                            posting_numbers.push(checkbox.getAttribute('data-posting_number'));
                        });

                        // 如果没有选中任何订单，显示店铺选择弹窗
                        if (posting_numbers.length === 0) {
                            showShopSelectModal(btn);
                            return;
                        }

                        // 有选中订单，确认同步选中的订单
                        layer.confirm('确定要同步选中的 ' + posting_numbers.length + ' 个订单吗？', {
                            icon: 3,
                            title: '批量同步确认',
                            btn: ['确定', '取消']
                        }, function (index) {
                            layer.close(index);
                            performSync(btn, posting_numbers);
                        });

                        // 执行同步的函数
                        function performSync(btn, posting_numbers) {
                            // 禁用按钮防止重复点击
                            btn.addClass('layui-btn-disabled').prop('disabled', true);

                            // 如果是批量同步选中订单，使用循环同步
                            if (posting_numbers.length > 0) {
                                batchSync(posting_numbers, btn);
                            }
                        }

                        // 批量同步函数
                        function batchSync(posting_numbers, btn) {
                            var total = posting_numbers.length;
                            var completed = 0;
                            var failed = 0;

                            var loadingIndex = layer.load(2, {
                                shade: [0.3, '#fff'],
                                content: '正在同步订单 1/' + total + '...'
                            });

                            // 并发同步，但限制并发数量避免服务器压力过大
                            var concurrency = 3; // 同时最多3个请求
                            var currentIndex = 0;

                            function syncNext() {
                                if (currentIndex >= total) {
                                    return;
                                }

                                var posting_number = posting_numbers[currentIndex];
                                currentIndex++;

                                $.ajax({
                                    url: 'ajax_order.php?act=orders_updata',
                                    type: 'POST',
                                    data: { posting_number: posting_number },
                                    success: function (res) {
                                        completed++;
                                        if (res.code !== 0) {
                                            failed++;
                                        }
                                        updateProgress();
                                        syncNext(); // 继续下一个
                                    },
                                    error: function () {
                                        completed++;
                                        failed++;
                                        updateProgress();
                                        syncNext(); // 继续下一个
                                    }
                                });
                            }

                            function updateProgress() {
                                // 更新进度提示
                                $('.layui-layer-content').html('正在同步订单 ' + completed + '/' + total + '...');

                                // 如果全部完成
                                if (completed >= total) {
                                    layer.close(loadingIndex);
                                    btn.removeClass('layui-btn-disabled').prop('disabled', false);

                                    if (failed === 0) {
                                        layer.msg('批量同步成功！共同步 ' + total + ' 个订单', { icon: 1 });
                                    } else {
                                        layer.msg('批量同步完成！成功 ' + (total - failed) + ' 个，失败 ' + failed + ' 个', { icon: 2 });
                                    }

                                    // 局部更新所有同步的订单
                                    posting_numbers.forEach(function (posting_number) {
                                        updateSingleOrder(posting_number);
                                    });

                                    // 重置勾选数量显示
                                    setTimeout(function () {
                                        updateSelectedCount();
                                    }, 500);
                                }
                            }

                            // 启动并发同步
                            for (var i = 0; i < Math.min(concurrency, total); i++) {
                                syncNext();
                            }
                        }

                        // 显示店铺选择弹窗
                        function showShopSelectModal(btn) {
                            // 获取店铺列表
                            $.ajax({
                                url: 'ajax_order.php?act=getShops',
                                type: 'GET',
                                success: function (res) {
                                    if (res.code === 0 && res.data) {
                                        var shopList = res.data;

                                        // 获取分组列表
                                        $.ajax({
                                            url: 'ajax.php?act=getShopGroups',
                                            success: function (groupRes) {
                                                // 根据接口返回的数据结构调整
                                                var groupList = [];
                                                var isSimpleFormat = false; // 标记是否为简单格式

                                                if (groupRes.data) {
                                                    if (groupRes.data.groups && Array.isArray(groupRes.data.groups)) {
                                                        // 复杂格式：如果数据在groups字段中
                                                        groupList = groupRes.data.groups;
                                                    } else if (Array.isArray(groupRes.data)) {
                                                        // 复杂格式：如果data直接是数组
                                                        groupList = groupRes.data;
                                                    } else if (typeof groupRes.data === 'object' && !Array.isArray(groupRes.data)) {
                                                        // 简单格式：ID到名称的映射对象 {"1": "震", "2": "覃", ...}
                                                        isSimpleFormat = true;
                                                        groupList = [];
                                                        Object.keys(groupRes.data).forEach(function (id) {
                                                            groupList.push({
                                                                id: id,
                                                                name: groupRes.data[id]
                                                            });
                                                        });
                                                    }
                                                }

                                                // 创建分组映射表
                                                var shopToGroupMap = {};
                                                var groupNameMap = {};

                                                // 遍历分组数据
                                                groupList.forEach(function (group) {
                                                    groupNameMap[group.id] = group.name;

                                                    // 如果是复杂格式且有shopIds数组，为每个店铺ID建立映射
                                                    if (!isSimpleFormat && group.shopIds && Array.isArray(group.shopIds)) {
                                                        group.shopIds.forEach(function (shopId) {
                                                            shopToGroupMap[shopId.toString()] = {
                                                                id: group.id,
                                                                name: group.name
                                                            };
                                                        });
                                                    }
                                                });

                                                // 为店铺添加分组名称
                                                shopList.forEach(function (shop, index) {
                                                    var shopId = shop.id.toString();

                                                    if (isSimpleFormat) {
                                                        // 简单格式：从店铺数据中获取group_id，然后查找分组名称
                                                        var groupId = shop.group_id || shop.groupid || shop.group_ID || shop.groupId || shop.Group_id || '';

                                                        if (groupId) {
                                                            groupId = groupId.toString().trim();
                                                        }

                                                        if (groupId && groupNameMap[groupId]) {
                                                            shop.group_id = groupId;
                                                            shop.group_name = groupNameMap[groupId];
                                                        } else {
                                                            shop.group_name = '默认分组';
                                                            shop.group_id = '';
                                                        }
                                                    } else {
                                                        // 复杂格式：使用shopToGroupMap映射
                                                        if (shopToGroupMap[shopId]) {
                                                            shop.group_id = shopToGroupMap[shopId].id.toString();
                                                            shop.group_name = shopToGroupMap[shopId].name;
                                                        } else {
                                                            shop.group_name = '默认分组';
                                                            shop.group_id = '';
                                                        }
                                                    }
                                                });

                                                var content = `
                                                    <div style="padding: 20px;">
                                                        <h3 style="margin: 0 0 20px 0;">选择同步店铺</h3>
                                                        
                                                        <form class="layui-form" lay-filter="shopSelectForm">
                                                            <!-- 设置区域 -->
                                                            <div style="margin-bottom: 15px;">
                                                                <div class="layui-form-item">
                                                                    <label class="layui-form-label">搜索店铺</label>
                                                                    <div class="layui-input-block">
                                                                        <input type="text" id="shopSearch" placeholder="输入店铺名称搜索..." class="layui-input">
                                                                    </div>
                                                                </div>
                                                                <div class="layui-form-item">
                                                                    <label class="layui-form-label">分组筛选</label>
                                                                    <div class="layui-input-block">
                                                                        <select id="groupFilter" lay-filter="groupFilter">
                                                                            <option value="">全部分组</option>
                                `;

                                                groupList.forEach(function (group) {
                                                    content += `<option value="${group.id}">${group.name}</option>`;
                                                });

                                                content += `<option value="default">默认分组</option>`;

                                                content += `
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                <div class="layui-form-item">
                                                                    <label class="layui-form-label">同步时间</label>
                                                                    <div class="layui-input-block">
                                                                        <select name="sync_days">
                                                                            <option value="7">最近7天</option>
                                                                            <option value="15">最近15天</option>
                                                                            <option value="30" selected>最近30天</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            
                                                            <!-- 全选区域 -->
                                                            <div class="layui-form-item">
                                                                <div class="layui-input-block">
                                                                    <input type="checkbox" id="selectAllShops" title="全选店铺" lay-skin="primary">
                                                                    <label for="selectAllShops" style="margin-left: 8px; cursor: pointer;">全选店铺</label>
                                                                    <span style="color: #999; margin-left: 20px;">
                                                                        已选择: <span id="selectedCount" style="color: #1E9FFF;">0</span> / <span id="totalCount">${shopList.length}</span>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            
                                                            <!-- 店铺列表区域 -->
                                                            <div style="border: 1px solid #e6e6e6; border-radius: 4px; max-height: 300px; overflow-y: auto;">
                                                                <table class="layui-table" style="margin: 0;">
                                                                    <thead>
                                                                        <tr>
                                                                            <th style="width: 60px;">选择</th>
                                                                            <th>店铺名称</th>
                                                                            <th style="width: 100px;">分组</th>
                                                                            <th style="width: 80px;">ID</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="shopTableBody">
                                `;

                                                shopList.forEach(function (shop) {
                                                    content += `
                                                        <tr class="shop-row" data-shop-id="${shop.id}" data-group-id="${shop.group_id || ''}">
                                                            <td style="text-align: center;">
                                                                <input type="checkbox" name="shop_ids" value="${shop.id}" lay-skin="primary" class="shop-checkbox">
                                                            </td>
                                                            <td>${shop.storename}</td>
                                                            <td>${shop.group_name || '默认分组'}</td>
                                                            <td>${shop.id}</td>
                                                        </tr>
                                                    `;
                                                });

                                                content += `
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </form>
                                                    </div>
                                                `;

                                                layer.open({
                                                    type: 1,
                                                    title: '选择同步店铺',
                                                    closeBtn: 1,
                                                    area: ['800px', '600px'],
                                                    content: content,
                                                    btn: ['开始同步选中店铺', '同步全部店铺', '取消'],
                                                    btnAlign: 'c',
                                                    btn1: function (index, layero) {
                                                        // 获取选中的店铺
                                                        var selectedShops = [];
                                                        layero.find('input[name="shop_ids"]:checked').each(function () {
                                                            selectedShops.push($(this).val());
                                                        });

                                                        if (selectedShops.length === 0) {
                                                            layer.msg('请选择至少一个店铺', { icon: 2 });
                                                            return false;
                                                        }

                                                        var syncDays = layero.find('select[name="sync_days"]').val();
                                                        layer.close(index);
                                                        performSyncShops(btn, selectedShops, syncDays);
                                                    },
                                                    btn2: function (index, layero) {
                                                        // 同步全部店铺
                                                        var syncDays = layero.find('select[name="sync_days"]').val();
                                                        layer.close(index);
                                                        performSyncShops(btn, [], syncDays); // 空数组表示全部店铺
                                                    },
                                                    success: function (layero, index) {
                                                        // 立即渲染form
                                                        form.render();

                                                        // 延迟执行事件绑定，确保layui渲染完成
                                                        setTimeout(function () {

                                                            // 更新统计数字
                                                            function updateCount(skipSelectAllUpdate) {
                                                                // 统计可见的店铺行数（而不是checkbox）
                                                                var total = layero.find('.shop-row:visible').length;
                                                                var checked = layero.find('.shop-checkbox:visible:checked').length;

                                                                layero.find('#selectedCount').text(checked);
                                                                layero.find('#totalCount').text(total);

                                                                // 只有在非全选操作时才自动更新全选状态
                                                                if (!skipSelectAllUpdate) {
                                                                    var $selectAll = layero.find('#selectAllShops');
                                                                    var shouldBeChecked = total > 0 && total === checked;
                                                                    $selectAll.prop('checked', shouldBeChecked);
                                                                }

                                                                // 重新渲染checkbox
                                                                form.render('checkbox');
                                                            }

                                                            // 全选功能 - 使用layui的form事件
                                                            form.on('checkbox', function (data) {
                                                                if (data.elem.id === 'selectAllShops') {
                                                                    var checked = data.elem.checked;

                                                                    // 更新所有店铺checkbox
                                                                    layero.find('.shop-checkbox').each(function () {
                                                                        $(this).prop('checked', checked);
                                                                    });

                                                                    form.render('checkbox');

                                                                    // 只更新统计数字，不要自动设置全选状态
                                                                    var total = layero.find('.shop-row:visible').length;
                                                                    var selectedCount = checked ? total : 0;
                                                                    layero.find('#selectedCount').text(selectedCount);
                                                                }
                                                            });

                                                            // 店铺checkbox事件绑定
                                                            layero.on('change', '.shop-checkbox', function () {
                                                                updateCount();
                                                            });

                                                            // 搜索功能
                                                            layero.find('#shopSearch').on('input', function () {
                                                                var keyword = $(this).val().toLowerCase();
                                                                layero.find('.shop-row').each(function () {
                                                                    var shopName = $(this).text().toLowerCase();
                                                                    if (keyword === '' || shopName.indexOf(keyword) > -1) {
                                                                        $(this).show();
                                                                    } else {
                                                                        $(this).hide();
                                                                    }
                                                                });
                                                                updateCount();
                                                            });

                                                            // 分组筛选功能
                                                            function doGroupFilter(groupId) {
                                                                layero.find('.shop-row').each(function () {
                                                                    var cardGroupId = $(this).data('group-id');
                                                                    // 转换为字符串进行比较
                                                                    var cardGroupIdStr = cardGroupId ? cardGroupId.toString() : '';
                                                                    var selectedGroupIdStr = groupId ? groupId.toString() : '';

                                                                    var shouldShow = false;

                                                                    if (selectedGroupIdStr === '') {
                                                                        // 选择"全部分组"
                                                                        shouldShow = true;
                                                                    } else if (selectedGroupIdStr === 'default') {
                                                                        // 选择"默认分组" - 显示没有分组的店铺
                                                                        shouldShow = (cardGroupIdStr === '');
                                                                    } else {
                                                                        // 选择具体分组 - 分组ID匹配
                                                                        shouldShow = (cardGroupIdStr === selectedGroupIdStr);
                                                                    }

                                                                    if (shouldShow) {
                                                                        $(this).show();
                                                                    } else {
                                                                        $(this).hide();
                                                                    }
                                                                });
                                                                updateCount();
                                                            }

                                                            // 分组筛选 - 使用layui form监听
                                                            form.on('select(groupFilter)', function (data) {
                                                                doGroupFilter(data.value);
                                                            });

                                                            // 备用分组筛选 - 使用原生change事件
                                                            layero.find('#groupFilter').on('change', function () {
                                                                var groupId = $(this).val();
                                                                doGroupFilter(groupId);
                                                            });



                                                            // 初始化统计
                                                            var shopCount = layero.find('.shop-row').length;

                                                            // 确保总数显示正确
                                                            layero.find('#totalCount').text(shopCount);

                                                            updateCount();

                                                        }, 300); // 延迟300ms确保layui渲染完成
                                                    }
                                                });
                                            },
                                            error: function (xhr) {
                                                layer.msg('获取分组列表失败: ' + xhr.statusText, { icon: 2 });
                                            }
                                        });
                                    } else {
                                        layer.msg('获取店铺列表失败', { icon: 2 });
                                    }
                                },
                                error: function (xhr) {
                                    layer.msg('获取店铺列表失败: ' + xhr.statusText, { icon: 2 });
                                }
                            });
                        }

                        // 执行店铺同步
                        function performSyncShops(btn, shopIds, syncDays) {
                            btn.addClass('layui-btn-disabled').prop('disabled', true);

                            var isAllShops = shopIds.length === 0;
                            var shopText = isAllShops ? '全部店铺' : shopIds.length + '个店铺';

                            // 如果是同步全部店铺，先获取所有店铺ID
                            if (isAllShops) {
                                var loadingIndex = layer.load(2, {
                                    shade: [0.3, '#fff'],
                                    content: '正在获取店铺列表...'
                                });

                                $.ajax({
                                    url: 'ajax_order.php?act=getShops',
                                    type: 'GET',
                                    success: function (res) {
                                        layer.close(loadingIndex);
                                        if (res.code === 0 && res.data) {
                                            var allShopIds = res.data.map(function (shop) {
                                                return shop.id.toString();
                                            });
                                            startSequentialSync(allShopIds, syncDays, btn);
                                        } else {
                                            layer.msg('获取店铺列表失败', { icon: 2 });
                                            btn.removeClass('layui-btn-disabled').prop('disabled', false);
                                        }
                                    },
                                    error: function (xhr) {
                                        layer.close(loadingIndex);
                                        layer.msg('获取店铺列表失败: ' + xhr.statusText, { icon: 2 });
                                        btn.removeClass('layui-btn-disabled').prop('disabled', false);
                                    }
                                });
                            } else {
                                // 直接同步选中的店铺
                                startSequentialSync(shopIds, syncDays, btn);
                            }
                        }

                        // 逐个店铺同步函数
                        function startSequentialSync(shopIds, syncDays, btn) {
                            var total = shopIds.length;
                            var completed = 0;
                            var failed = 0;
                            var failedShops = [];

                            var loadingIndex = layer.load(2, {
                                shade: [0.3, '#fff'],
                                content: '正在同步第 1 / ' + total + ' 个店铺...'
                            });

                            function syncNextShop(index) {
                                if (index >= total) {
                                    // 所有店铺同步完成
                                    layer.close(loadingIndex);
                                    btn.removeClass('layui-btn-disabled').prop('disabled', false);

                                    var successCount = total - failed;
                                    var message = '同步完成！';

                                    if (failed === 0) {
                                        message += '成功同步 ' + successCount + ' 个店铺';
                                        layer.msg(message, { icon: 1, time: 3000 });
                                    } else {
                                        message += '成功 ' + successCount + ' 个，失败 ' + failed + ' 个';
                                        if (failedShops.length > 0) {
                                            message += '<br>失败店铺ID: ' + failedShops.join(', ');
                                        }
                                        layer.msg(message, {
                                            icon: 2,
                                            time: 5000,
                                            area: ['400px', '120px']
                                        });
                                    }

                                    // 刷新表格和统计
                                    table.reload('orderTable');
                                    initOrderStatusCounts();

                                    // 重置勾选数量显示
                                    setTimeout(function () {
                                        updateSelectedCount();
                                    }, 500);
                                    return;
                                }

                                var shopId = shopIds[index];
                                var currentShop = index + 1;

                                // 更新进度提示
                                $('.layui-layer-content').html('正在同步第 ' + currentShop + ' / ' + total + ' 个店铺<br>店铺ID: ' + shopId);

                                $.ajax({
                                    url: 'ajax_order.php?act=orders_updata',
                                    type: 'POST',
                                    data: {
                                        shop_ids: shopId, // 单个店铺ID
                                        sync_days: syncDays
                                    },
                                    timeout: 60000, // 单个店铺同步超时时间设为60秒
                                    success: function (res) {
                                        completed++;
                                        if (res.code !== 0) {
                                            failed++;
                                            failedShops.push(shopId);
                                        }

                                        // 短暂延迟后继续下一个店铺，避免请求过于频繁
                                        setTimeout(function () {
                                            syncNextShop(index + 1);
                                        }, 1000);
                                    },
                                    error: function (xhr) {
                                        completed++;
                                        failed++;
                                        failedShops.push(shopId);

                                        // 短暂延迟后继续下一个店铺
                                        setTimeout(function () {
                                            syncNextShop(index + 1);
                                        }, 1000);
                                    }
                                });
                            }

                            // 开始同步第一个店铺
                            syncNextShop(0);
                        }


                    });


                    $('.preview').off('click').on('click', function () {
                        var posting_number = $(this).data('posting_number');
                        layer.load(2);
                        $.ajax({
                            url: 'ajax_order.php?act=order_preview', // 替换为实际接口地址
                            type: 'POST',
                            data: { posting_number },
                            success: function (res) {
                                layer.closeAll();
                                if (res.code === 0) { // 根据你的返回格式判断
                                    // 使用浏览器直接打印功能
                                    directPrint(res.url);
                                } else {
                                    layer.msg(res.msg || '获取面单失败', { icon: 2 });
                                }
                            },
                            error: function (xhr) {
                                layer.closeAll();
                                layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                            }
                        });
                    });


                    // 在表单提交时，获取分组ID
                    form.on('submit(search)', function (data) {
                        var field = data.field;
                        currentStatus = currentStatus || 'PPW';
                        text = field.text || '';
                        money = field.money || '';
                        moneys = field.moneys || '';
                        storeid = field.storeid || '';
                        groupid = field.groupid || ''; // 获取分组ID
                        date1 = field.date1 || '';
                        date2 = field.date2 || '';
                        paypt = field.paypt || '';
                        cancelled = field.cancelled || null;
                        renderTable(currentStatus, text, money, moneys, storeid, date1, date2, groupid, '', '', paypt, cancelled);
                        return false;
                    });
                    // 监听开关切换事件
                    form.on('switch(user_auto_close_switch)', function (obj) {
                        var id = $(this).data('id');
                        var status = obj.elem.checked ? 1 : 0;
                        layer.load(2);
                        $.ajax({
                            url: 'ajax_order.php?act=user_auto_switch',
                            type: 'POST',
                            data: {
                                id: id,
                                status: status
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code != 0) {
                                    layer.msg(res.msg || '操作失败');
                                    // 回滚状态
                                    obj.elem.checked = !status;
                                    form.render('checkbox');
                                }
                            },
                            error: function () {
                                layer.closeAll('loading');
                                layer.msg('请求失败，请检查网络');
                                obj.elem.checked = !status;
                                form.render('checkbox');
                            }
                        });
                    });
                },
                error: function(res, msg) {
                    if(msg === 'timeout') {
                      layer.msg('请求超时，请稍后再试');
                    } else {
                      layer.msg('数据加载失败: ' + msg);
                    }
                }
            });
            window.renderTableajax = 0;
        }
        function initShopSelect() {
            layui.use('form', function () {
                var form = layui.form;
                $.ajax({
                    url: 'ajax_order.php?act=getShops',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var html = '<option value="">所有店铺</option>';
                            res.data.forEach(shop => {
                                html += `<option value="${shop.id}">${shop.storename}</option>`;
                            });
                            $('select[name="storeid"]').html(html);
                            form.render('select'); // 必须重新渲染
                        }
                    }
                });
            });
        }

        // 使用事件委托方式绑定订单操作按钮事件，这样即使DOM重新渲染也不会丢失事件
        $(document).on('click', '.order_status', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number');
            if (!posting_number) {
                return;
            }
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=orders_updata',
                type: 'POST',
                data: { posting_number },
                success: function (res) {
                    layer.closeAll();
                    if (res.code === 0) {
                        // 局部更新当前订单
                        updateSingleOrder(posting_number);
                        layer.msg('同步成功', { icon: 1 });
                    } else {
                        layer.msg(res.msg || '同步失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });

        $(document).on('click', '.Stocking_order', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number');
            if (!posting_number) {
                return;
            }
            layer.confirm('确定要备货吗？', {
                icon: 3,
                title: '备货确认',
                btn: ['确定', '取消']
            }, function (index) {
                layer.close(index);
                $.ajax({
                    url: 'ajax_order.php?act=order_package',
                    type: 'POST',
                    data: { posting_number },
                    success: function (res) {
                        if (res.code === 0) {
                            // 局部更新当前订单
                            updateSingleOrder(posting_number);
                            layer.msg('备货成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '备货失败', { icon: 2 });
                        }
                    },


                    error: function (xhr) {
                        layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                    }
                });
            }, function (index) {
                layer.close(index);
            });
        });

        $(document).on('click', '.order_purchase', function () {
            var $this = $(this),
                orderId = $this.data('order_id'),
                posting_number = $this.data('posting_number'); // 直接从data属性获取
            if (!orderId) {
                layer.msg('缺少订单ID', { icon: 2 });
                return;
            }

            if (!posting_number) {
                layer.msg('缺少订单编号', { icon: 2 });
                return;
            }

            // 获取当前行的数据
            var row = $this.closest('tr');


            // 获取offer_id、SKU和数量
            var sku = '';
            var offer_id = '';
            var quantity = 1;

            // 尝试获取数量
            var quantityText = row.find('.line:contains("数量")').text().trim();
            var quantityMatch = quantityText.match(/数量[：:]\s*[＊\*]?\s*(\d+)/i);
            if (quantityMatch && quantityMatch[1]) {
                quantity = parseInt(quantityMatch[1]) || 1;
            }

            // 尝试从行中获取offer_id
            var offerIdText = row.find('.line:contains("offer_id")').text().trim();
            var offerIdMatch = offerIdText.match(/offer_id[：:]\s*([^\s]+)/i);
            if (offerIdMatch && offerIdMatch[1]) {
                offer_id = offerIdMatch[1];
            }

            // 尝试从行中获取SKU
            var skuText = row.find('.line:contains("Sku")').text().trim();
            var skuMatch = skuText.match(/Sku[：:]\s*([^\s]+)/i);
            if (skuMatch && skuMatch[1]) {
                sku = skuMatch[1];
            }

            // 如果没有找到offer_id或SKU，尝试从整个订单区域获取
            if (!offer_id || !sku) {
                var $orderDiv = $this.closest('[id^="posting_number"]');
                if ($orderDiv.length > 0) {
                    // 尝试获取offer_id
                    if (!offer_id) {
                        offerIdText = $orderDiv.find('.line:contains("offer_id")').text().trim();
                        offerIdMatch = offerIdText.match(/offer_id[：:]\s*([^\s]+)/i);
                        if (offerIdMatch && offerIdMatch[1]) {
                            offer_id = offerIdMatch[1];
                        }
                    }

                    // 尝试获取SKU
                    if (!sku) {
                        skuText = $orderDiv.find('.line:contains("Sku")').text().trim();
                        skuMatch = skuText.match(/Sku[：:]\s*([^\s]+)/i);
                        if (skuMatch && skuMatch[1]) {
                            sku = skuMatch[1];
                        }
                    }

                    // 如果还没获取到数量，再次尝试
                    if (quantity === 1) {
                        quantityText = $orderDiv.find('.line:contains("数量")').text().trim();
                        quantityMatch = quantityText.match(/数量[：:]\s*[＊\*]?\s*(\d+)/i);
                        if (quantityMatch && quantityMatch[1]) {
                            quantity = parseInt(quantityMatch[1]) || 1;
                        }
                    }
                }

                // 如果还是没有找到offer_id或SKU，尝试从多商品中获取第一个
                if (!offer_id || !sku) {
                    var $productItems = $orderDiv.find('.product-item');
                    if ($productItems.length > 0) {
                        $productItems.each(function () {
                            // 尝试获取offer_id
                            if (!offer_id) {
                                var itemOfferIdText = $(this).find('.line:contains("offer_id")').text().trim();
                                var itemOfferIdMatch = itemOfferIdText.match(/offer_id:\s*([^\s]+)/i);
                                if (itemOfferIdMatch && itemOfferIdMatch[1]) {
                                    offer_id = itemOfferIdMatch[1];
                                }
                            }

                            // 尝试获取SKU
                            if (!sku) {
                                var itemSkuText = $(this).find('.line:contains("SKU")').text().trim();
                                var itemSkuMatch = itemSkuText.match(/SKU:\s*([^\s]+)/i);
                                if (itemSkuMatch && itemSkuMatch[1]) {
                                    sku = itemSkuMatch[1];
                                }
                            }

                            // 尝试获取该商品的数量
                            if (quantity === 1) {
                                var itemQuantityText = $(this).find('.line:contains("数量")').text().trim();
                                var itemQuantityMatch = itemQuantityText.match(/数量:\s*(\d+)/i);
                                if (itemQuantityMatch && itemQuantityMatch[1]) {
                                    quantity = parseInt(itemQuantityMatch[1]) || 1;
                                }
                            }

                            // 如果都找到了，跳出循环
                            if (offer_id && sku && quantity > 0) {
                                return false;
                            }
                        });
                    }
                }
            }

            // 如果没有找到offer_id，使用SKU作为offer_id
            if (!offer_id && sku) {
                offer_id = sku;
            }

            // 直接从DOM元素中获取数据
            var purchaseCost = row.find('.wuliu_info').data('info');
            var purchase_orderSn = row.find('.purchase_orderSn span').data('info');
            var courierNumber = row.find('.ee-c:contains("快递单号")').find('span').text().trim();

            // 完全重写数据获取逻辑
            try {
                // 直接从DOM中获取确切的数据 - 注意：这里获取的是总价，需要计算单价
                var totalPrice = row.find('.ee-c:contains("产品总价")').find('span').first().text().trim();
                // 计算单价：总价除以数量
                var price = totalPrice && quantity ? (parseFloat(totalPrice.replace(/[^0-9.]/g, '')) / quantity).toString() : '0';

                // 物流/佣金 - 直接获取文本内容
                var logisticsText = row.find('.ee-c:contains("物流/佣金")').text().trim();


                // 拆分物流和佣金
                var deliveryMatch = logisticsText.match(/物流\/佣金：([^\/]+)\/(.+)/);
                var delivery = deliveryMatch ? deliveryMatch[1].trim() : '';
                var commissions = deliveryMatch ? deliveryMatch[2].trim() : '';

                // 类目佣金
                var percentText = row.find('.ee-c:contains("类目佣金")').text().trim();
                var percentMatch = percentText.match(/类目佣金：(.+)%/);
                var percent = percentMatch ? percentMatch[1].trim() : '';

                // 后台/出库
                var weightText = row.find('.ee-c:contains("后台/出库")').text().trim();


                var weightMatch = weightText.match(/后台\/出库：([^\/]+)\/(.+)kg/);
                var weight = weightMatch ? weightMatch[1].trim() : '0';
                var out_weight = weightMatch ? weightMatch[2].trim() : '0';

                // 预估利润
                var profitText = row.find('.ee-c:contains("预估利润")').text().trim();
                var profitMatch = profitText.match(/预估利润：(.+)￥/);
                var profit = profitMatch ? profitMatch[1].trim() : '0';

            } catch (e) {
                // 设置默认值
                price = '0';
                delivery = '0';
                commissions = '0';
                percent = '0';
                weight = '0';
                out_weight = '0';
                profit = '0';
            }

            // 如果无法从DOM获取，则发送AJAX请求获取完整订单数据
            if (!price || !delivery || !commissions) {
                $.ajax({
                    url: 'ajax_order.php?act=get_order_detail',
                    type: 'POST',
                    data: { order_id: orderId, posting_number: posting_number },
                    async: false,
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var data = res.data;
                            // 从AJAX响应中获取数据，确保正确处理
                            // 注意：如果后端返回的是总价，需要计算单价
                            if (data.price) {
                                // 假设后端返回的是总价，计算单价
                                var backendPrice = parseFloat(data.price.toString().replace(/[^0-9.]/g, ''));
                                price = quantity ? (backendPrice / quantity).toString() : backendPrice.toString();
                            }
                            if (data.delivery) delivery = data.delivery;
                            if (data.commissions) commissions = data.commissions;
                            if (data.percent) percent = data.percent;
                            if (data.weight) weight = data.weight;
                            if (data.out_weight) out_weight = data.out_weight;
                            if (data.profit) profit = data.profit;


                        }
                    }
                });
            }

            // 清理数据，只保留数字和小数点
            price = price ? price.replace(/[^0-9.]/g, '') : '0';
            delivery = delivery ? delivery.replace(/[^0-9.]/g, '') : '0';
            commissions = commissions ? commissions.replace(/[^0-9.]/g, '') : '0';
            percent = percent ? percent.replace(/[^0-9.]/g, '') : '0';
            weight = weight ? weight.replace(/[^0-9.]/g, '') : '0';
            out_weight = out_weight ? out_weight.replace(/[^0-9.]/g, '') : '0';
            profit = profit ? profit.replace(/[^0-9.]/g, '') : '0';

            // 确保所有值都是有效数字
            price = parseFloat(price) || 0;
            delivery = parseFloat(delivery) || 0;
            commissions = parseFloat(commissions) || 0;
            percent = parseFloat(percent) || 0;
            weight = parseFloat(weight) || 0;
            out_weight = parseFloat(out_weight) || 0;
            profit = parseFloat(profit) || 0;

            // 转回字符串以便传递
            price = price.toString();
            delivery = delivery.toString();
            commissions = commissions.toString();
            percent = percent.toString();
            weight = weight.toString();
            out_weight = out_weight.toString();
            profit = profit.toString();

            openEditWindow(posting_number, purchaseCost, purchase_orderSn, price, delivery, commissions, profit, percent, weight, out_weight, courierNumber, sku, quantity, offer_id, posting_number);
        });


        $(document).on('click', '.order_notes', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number');
            if (!posting_number) {
                return;
            }
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=get_order_notes',
                type: 'POST',
                data: { posting_number: posting_number },
                success: function (res) {
                    layer.closeAll();
                    if (res.code === 0) {
                        openNotesWindow(posting_number, res.data.OrderNotes || '');
                    } else {
                        layer.msg(res.msg || '获取备注失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });

        $(document).on('click', '.order_self_only', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number');
            if (!posting_number) {
                layer.msg('订单号不能为空', { icon: 2 });
                return;
            }
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=order_preview',
                type: 'POST',
                data: { posting_number },
                success: function (res) {
                    layer.closeAll();
                    if (res.code === 0) {
                        // 使用浏览器直接打印功能
                        directPrint(res.url);
                    } else {
                        layer.msg(res.msg || '获取面单失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });

        $(document).on('click', '.cancel-purchase-btn', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number');
            if (!posting_number) {
                layer.msg('订单号不能为空', { icon: 2 });
                return;
            }

            layer.confirm('确定要取消关联的采购订单吗？<br><span style="color: #FF5722;">此操作将清空采购成本和采购单号！</span>', {
                icon: 3,
                title: '强制取消关联订单',
                btn: ['确定取消', '保持关联']
            }, function (index) {
                layer.close(index);
                layer.load(2);
                $.ajax({
                    url: 'ajax_order.php?act=cancel_purchase_relation',
                    type: 'POST',
                    data: { posting_number: posting_number },
                    success: function (res) {
                        layer.closeAll();
                        if (res.code === 0) {
                            // 局部更新当前订单
                            updateSingleOrder(posting_number);
                            layer.msg('采购关联已取消', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '取消关联失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll();
                        layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                    }
                });
            }, function (index) {
                layer.close(index);
            });
        });

        // 添加到黑名单按钮点击事件
        $(document).on('click', '.add-blacklist-btn', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number'),
                customer_name = $this.data('customer_name');

            if (!posting_number || !customer_name) {
                layer.msg('订单号或客户姓名不能为空', { icon: 2 });
                return;
            }

            // 直接显示添加黑名单弹窗，允许同一客户多次添加黑名单记录
            showAddBlacklistModal(posting_number, customer_name);
        });

        // 移除黑名单按钮点击事件
        $(document).on('click', '.remove-blacklist-btn', function () {
            var $this = $(this),
                customer_name = $this.data('customer_name'),
                posting_number = $this.data('posting_number');

            if (!customer_name) {
                layer.msg('客户姓名不能为空', { icon: 2 });
                return;
            }

            // 先查询该客户的黑名单记录数量
            $.ajax({
                url: 'ajax_order.php?act=get_blacklist_count_by_customer',
                type: 'POST',
                data: { customer_name: customer_name },
                success: function (res) {
                    if (res.code === 0) {
                        var count = res.data.count;
                        var content = '';

                        if (count > 1) {
                            content = `
                                <div style="padding: 20px;">
                                    <p>客户 "${customer_name}" 在 ${count} 个订单中被添加到黑名单。</p>
                                    <p>请选择移除方式：</p>
                                    <div class="layui-form">
                                        <input type="radio" name="remove_type" value="single" title="仅移除当前订单的黑名单记录" checked>
                                        <input type="radio" name="remove_type" value="all" title="移除该客户的所有黑名单记录">
                                    </div>
                                </div>
                            `;

                            layer.open({
                                type: 1,
                                title: '选择移除方式',
                                area: ['450px', '250px'],
                                content: content,
                                btn: ['确定移除', '取消'],
                                btn1: function (index, layero) {
                                    var remove_type = layero.find('input[name="remove_type"]:checked').val();
                                    layer.close(index);
                                    performRemove(customer_name, posting_number, remove_type);
                                },
                                success: function (layero, index) {
                                    layui.form.render();
                                }
                            });
                        } else {
                            // 只有一条记录，直接移除
                            layer.confirm('确定要将 "' + customer_name + '" 从黑名单中移除吗？', {
                                icon: 3,
                                title: '移除黑名单确认',
                                btn: ['确定移除', '取消']
                            }, function (index) {
                                layer.close(index);
                                performRemove(customer_name, posting_number, 'all');
                            });
                        }
                    } else {
                        layer.msg(res.msg || '查询失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });

        // 执行移除黑名单操作
        function performRemove(customer_name, posting_number, remove_type) {
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=remove_blacklist',
                type: 'POST',
                data: {
                    customer_name: customer_name,
                    posting_number: posting_number,
                    remove_type: remove_type
                },
                success: function (res) {
                    layer.closeAll();
                    if (res.code === 0) {
                        layer.msg('已从黑名单中移除', { icon: 1 });
                        // 局部更新当前订单以隐藏黑名单图标
                        updateSingleOrder(posting_number);
                    } else {
                        layer.msg(res.msg || '移除失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        }

        // 查看黑名单历史
        $(document).on('click', '.view-blacklist-history', function () {
            var $this = $(this),
                customer_name = $this.data('customer_name');

            if (!customer_name) {
                layer.msg('客户姓名不能为空', { icon: 2 });
                return;
            }

            layer.load(2);
            $.ajax({
                url: 'ajax_order.php?act=get_customer_blacklist_history',
                type: 'POST',
                data: { customer_name: customer_name },
                success: function (res) {
                    layer.closeAll();
                    if (res.code === 0) {
                        showBlacklistHistory(customer_name, res.data);
                    } else {
                        layer.msg(res.msg || '获取历史记录失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll();
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        });

        // 显示黑名单历史弹窗
        function showBlacklistHistory(customer_name, history) {
            var content = `
                <div style="padding: 20px;">
                    <h3 style="margin-bottom: 15px; color: #333;">客户 "${customer_name}" 的黑名单历史</h3>
                    <div style="max-height: 400px; overflow-y: auto;">
            `;

            if (history.length === 0) {
                content += '<p style="text-align: center; color: #999;">暂无黑名单记录</p>';
            } else {
                content += '<table class="layui-table" style="margin: 0;">';
                content += '<thead><tr><th>订单号</th><th>原因</th><th>详细说明</th><th>添加时间</th></tr></thead>';
                content += '<tbody>';

                history.forEach(function (item) {
                    content += '<tr>';
                    content += '<td>' + item.posting_number + '</td>';
                    content += '<td><span style="color: #FF5722;">' + item.reason_type_text + '</span></td>';
                    content += '<td>' + (item.reason_text || '-') + '</td>';
                    content += '<td>' + item.created_at + '</td>';
                    content += '</tr>';
                });

                content += '</tbody></table>';
            }

            content += `
                    </div>
                    <div style="text-align: center; margin-top: 15px; color: #999;">
                        共 ${history.length} 条记录
                    </div>
                </div>
            `;

            layer.open({
                type: 1,
                title: '黑名单历史',
                area: ['700px', '500px'],
                content: content,
                btn: ['关闭'],
                btn1: function (index) {
                    layer.close(index);
                }
            });
        }

        // 显示添加黑名单弹窗
        function showAddBlacklistModal(posting_number, customer_name) {
            var content = `
                <div style="padding: 20px;">
                    <form class="layui-form" lay-filter="blacklistForm">
                        <input type="hidden" name="posting_number" value="${posting_number}">
                        <input type="hidden" name="customer_name" value="${customer_name}">
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户姓名</label>
                            <div class="layui-input-block">
                                <input type="text" value="${customer_name}" readonly class="layui-input layui-disabled">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">黑名单原因</label>
                            <div class="layui-input-block">
                                <input type="radio" name="reason_type" value="1" title="频繁退货退款" checked>
                                <input type="radio" name="reason_type" value="2" title="恶意差评">
                                <input type="radio" name="reason_type" value="3" title="货到付款拒收">
                                <input type="radio" name="reason_type" value="4" title="其他">
                            </div>
                        </div>
                        
                        <div class="layui-form-item layui-form-text" id="reasonTextDiv" style="display: none;">
                            <label class="layui-form-label">详细原因</label>
                            <div class="layui-input-block">
                                <textarea name="reason_text" placeholder="请输入详细原因..." class="layui-textarea" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            `;

            layer.open({
                type: 1,
                title: '添加到黑名单',
                area: ['500px', '400px'],
                content: content,
                btn: ['确定添加', '取消'],
                btn1: function (index, layero) {
                    var formData = form.val('blacklistForm');

                    if (formData.reason_type === '4' && !formData.reason_text.trim()) {
                        layer.msg('选择其他原因时必须填写详细原因', { icon: 2 });
                        return false;
                    }

                    layer.load(2);
                    $.ajax({
                        url: 'ajax_order.php?act=add_blacklist',
                        type: 'POST',
                        data: formData,
                        success: function (res) {
                            layer.closeAll();
                            if (res.code === 0) {
                                layer.msg('已添加到黑名单', { icon: 1 });
                                layer.close(index);
                                // 局部更新当前订单以显示黑名单图标
                                updateSingleOrder(posting_number);
                            } else {
                                layer.msg(res.msg || '添加失败', { icon: 2 });
                            }
                        },
                        error: function (xhr) {
                            layer.closeAll();
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                },
                success: function (layero, index) {
                    form.render();

                    // 监听原因类型变化
                    form.on('radio()', function (data) {
                        if (data.elem.name === 'reason_type') {
                            var $reasonTextDiv = layero.find('#reasonTextDiv');
                            if (data.value === '4') {
                                $reasonTextDiv.show();
                            } else {
                                $reasonTextDiv.hide();
                            }
                        }
                    });
                }
            });
        }

        $(document).on('click', '.send_order', function () {
            var $this = $(this),
                posting_number = $this.data('posting_number');
            if (!posting_number) {
                return;
            }
            layer.confirm('确定要申请出货吗？', {
                icon: 3,
                title: '申请出货确认',
                btn: ['确定', '取消']
            }, function (index) {
                layer.close(index);
                layer.load(2);
                $.ajax({
                    url: 'ajax_order.php?act=send_order',
                    type: 'POST',
                    data: { posting_number },
                    success: function (res) {
                        layer.closeAll();
                        if (res.code === 0) {
                            //table.reload('orderTable');
                            // 局部更新当前订单
                            updateSingleOrder(posting_number);
                            layer.msg('申请出货成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '申请出货失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll();
                        layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                    }
                });
            }, function (index) {
                layer.close(index);
            });
        });

        // 声明全局变量保存当前状态和搜索词
        var currentStatus = 'PPW';
        var text = '';
        var money = '';
        var moneys = '';
        var storeid = '';
        var date1 = '';
        var date2 = '';
        var groupid = '';
        var sort_order = 'desc'; // Add sort order variable
        var paypt = '';
        var cancelled = null;

        // 在选项卡切换事件中
        layui.element.on('tab(orderTab)', function (data) {
            const status = ['pending', 'awaiting_deliver', 'awaiting_deliver2', 'delivering', 'delivered', 'cancelled', 'all'][data.index];

            // 显示加载提示
            var loadingIndex = layer.load(2, { shade: [0.3, '#fff'] });

            // 显示/隐藏子标签
            if (status === 'pending') {
                $('#pendingSubTabs').show();
                // 优化：默认显示未采购（数据量较小，加载更快）
                currentStatus = 'not_purchased';
                // 重置子标签选择状态，默认选中未采购
                $('#pendingSubTabs > .layui-tab-title li').removeClass('layui-this');
                $('#pendingSubTabs > .layui-tab-title li:eq(1)').addClass('layui-this');
            } else {
                $('#pendingSubTabs').hide();
                currentStatus = status;
            }

            // 延迟渲染，避免界面卡顿
            setTimeout(function () {
                renderTable(currentStatus, text, money, moneys, storeid, date1, date2, groupid, 'in_process_at', sort_order, paypt, cancelled);
                layer.close(loadingIndex);
            }, 100);
        });

        // 子标签切换事件
        $(document).on('click', '#pendingSubTabs > .layui-tab-title li', function (e) {
            e.preventDefault();
            e.stopPropagation();

            // 显示加载提示
            var loadingIndex = layer.load(2, { shade: [0.3, '#fff'] });

            // 移除所有active状态
            $('#pendingSubTabs > .layui-tab-title li').removeClass('layui-this');
            // 添加当前点击的active状态
            $(this).addClass('layui-this');

            // 确保子标签区域保持显示
            $('#pendingSubTabs').show();

            // 获取子标签状态
            const subIndex = $(this).index();
            const subStatus = ['pending', 'not_purchased', 'purchased', 'awaiting_verification'][subIndex];

            // 设置当前状态
            currentStatus = subStatus;

            // 优化：限制每页数据量，提高加载速度
            setTimeout(function () {
                renderTable(currentStatus, text, money, moneys, storeid, date1, date2, groupid, 'in_process_at', sort_order, paypt, cancelled);
                layer.close(loadingIndex);
            }, 100);
        });

        // 已移除三级筛选功能

        // 重置按钮点击事件
        layui.form.on('reset(orderForm)', function () {
            // 重置所有变量
            //renderTable(currentStatus);
            renderTable(currentStatus, text, money, moneys, storeid, date1, date2, groupid, '', '', paypt, cancelled);
        });

        // 搜索按钮点击事件
        layui.form.on('submit(search)', function (data) {
            var field = data.field;
            currentStatus = currentStatus; // 可以去掉，因为 currentStatus 已经存在
            text = field.text || '';
            money = field.money || '';
            moneys = field.moneys || '';
            storeid = field.storeid || '';
            date1 = field.date1 || '';
            date2 = field.date2 || '';
            groupid = field.groupid || '';



            renderTable(currentStatus, text, money, moneys, storeid, date1, date2, groupid, '', '', paypt, cancelled);
            return false;
        });

        // 添加键盘回车事件
        $('#posting_number').on('keypress', function (e) {
            if (e.which === 13) {
                $('#searchBtn').click();
                return false;
            }
        });



        // 打开备注编辑弹窗
        function openNotesWindow(posting_number, currentNotes) {
            layer.open({
                type: 1,
                title: '订单备注 - ' + posting_number,
                area: ['500px', '400px'],
                zIndex: layer.zIndex,
                shade: 0.3,
                closeBtn: 1,
                content: `
                    <form class="layui-form" lay-filter="notesForm" style="padding: 20px;">
                        <input type="hidden" name="posting_number" value="${posting_number}">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">订单备注</label>
                            <div class="layui-input-block">
                                <textarea name="notes" placeholder="请输入订单备注信息..." class="layui-textarea" style="height: 200px;">${currentNotes}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="saveNotes">保存备注</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                `,
                success: function (layero, index) {
                    form.render();

                    // 监听保存备注表单提交
                    form.on('submit(saveNotes)', function (formData) {
                        var field = formData.field;

                        layer.load(2);
                        $.ajax({
                            url: 'ajax_order.php?act=update_order_notes',
                            type: 'POST',
                            data: {
                                posting_number: field.posting_number,
                                notes: field.notes
                            },
                            success: function (res) {
                                layer.closeAll();
                                if (res.code === 0) {
                                    layer.msg('备注保存成功', { icon: 1 });
                                    layer.close(index);
                                    // 即刻更新本地显示与缓存
                                    try {
                                        if (!window._orderNotesCache) window._orderNotesCache = {};
                                        window._orderNotesCache[field.posting_number] = field.notes || '';
                                        setOrderNotesInDOM(field.posting_number, field.notes || '');
                                    } catch(e) {}
                                    // 再进行局部刷新，确保其它字段同步
                                    updateSingleOrder(field.posting_number);
                                } else {
                                    layer.msg(res.msg || '保存失败', { icon: 2 });
                                }
                            },
                            error: function (xhr) {
                                layer.closeAll();
                                layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                            }
                        });
                        return false;
                    });
                }
            });
        }
        // 打开编辑弹窗 - 现代化版本
        function openEditWindow(posting_number, purchaseCost, purchase_orderSn, price, delivery, commissions, profit, percent, weight, out_weight, courierNumber, sku, quantity, offer_id, posting_number) {
            // 处理可能的多个物流单号和采购单号
            var courierNumbers = [];
            var purchaseOrderSns = [];

            if (courierNumber && typeof courierNumber === 'string') {
                courierNumbers = courierNumber.split(',').map(function (item) {
                    return item.trim();
                }).filter(Boolean);
            }

            if (purchase_orderSn && typeof purchase_orderSn === 'string') {
                purchaseOrderSns = purchase_orderSn.split(',').map(function (item) {
                    return item.trim();
                }).filter(Boolean);
            }

            // 动态生成HTML内容 - 更紧凑版本
            var content = `
                <div style="padding: 12px;">
                    <h3 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #1E9FFF; padding-bottom: 6px; font-size: 15px;">
                        <i class="layui-icon layui-icon-edit" style="color: #1E9FFF;"></i> 编辑采购信息
                    </h3>
                    
                    <form class="layui-form" lay-filter="purchaseEditForm">
                        <input type="hidden" name="posting_number" value="${posting_number}">
                        
                        <!-- 基础信息 -->
                        <div class="layui-row layui-col-space10" style="margin-bottom: 12px;">
                            <div class="layui-col-md6">
                                <div class="layui-form-item" style="margin-bottom: 6px;">
                                    <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">采购成本</label>
                                    <div class="layui-input-block" style="margin-left: 80px;">
                                        <input type="text" name="cost" value="${purchaseCost || ''}" placeholder="采购成本" class="layui-input" id="editCostInput">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item" style="margin-bottom: 6px;">
                                    <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">商品数量</label>
                                    <div class="layui-input-block" style="margin-left: 80px;">
                                        <input type="text" value="${quantity || 1}" readonly class="layui-input layui-disabled">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 价格信息 -->
                        <div class="layui-row layui-col-space10" style="margin-bottom: 12px;">
                            <div class="layui-col-md6">
                                <div class="layui-form-item" style="margin-bottom: 6px;">
                                    <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">单价 (¥)</label>
                                    <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                        <input type="text" id="editMoneyInput" value="${price || ''}" class="layui-input" placeholder="单价" style="margin-right: 6px;">
                                        <button type="button" id="updatePriceBtn" class="layui-btn layui-btn-normal layui-btn-xs">
                                            <i class="layui-icon layui-icon-refresh"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item" style="margin-bottom: 6px;">
                                    <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">总价 (¥)</label>
                                    <div class="layui-input-block" style="margin-left: 80px;">
                                        <input type="text" id="editPriceInput" value="${price * (quantity || 1)}" class="layui-input" placeholder="总价">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 采购单号 -->
                        <div id="purchaseOrderContainer" style="margin-bottom: 12px;">
                            <div class="layui-form-item purchase-item" style="margin-bottom: 6px;">
                                <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">采购单号</label>
                                <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                    <input type="text" name="purchase_orderSn[]" value="${purchaseOrderSns[0] || ''}" placeholder="采购单号" class="layui-input" style="margin-right: 6px;">
                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-xs add-purchase">
                                        <i class="layui-icon layui-icon-add-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 物流单号 -->
                        <div id="courierNumberContainer" style="margin-bottom: 12px;">
                            <div class="layui-form-item courier-item" style="margin-bottom: 6px;">
                                <label class="layui-form-label" style="width: 70px; padding: 8px 5px;">物流单号</label>
                                <div class="layui-input-block" style="margin-left: 80px; display: flex;">
                                    <input type="text" name="courierNumber[]" value="${courierNumbers[0] || ''}" placeholder="物流单号" class="layui-input" style="margin-right: 6px;">
                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-xs add-courier">
                                        <i class="layui-icon layui-icon-add-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 利润分析 - 超紧凑布局 -->
                        <div style="background: #f8f9fa; border-radius: 3px; padding: 8px; border-left: 3px solid #1E9FFF;">
                            <h4 style="margin: 0 0 8px 0; color: #333; font-size: 13px;">
                                <i class="layui-icon layui-icon-chart" style="color: #1E9FFF;"></i> 利润分析
                            </h4>
                            <div class="layui-row layui-col-space5">
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                        <div style="color: #666; font-size: 11px;">物流费</div>
                                        <div id="editDelivery" style="font-weight: bold; color: #333; font-size: 12px;">${delivery || '0.00'} ¥</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                        <div style="color: #666; font-size: 11px;">平台佣金</div>
                                        <div id="editCommissions" style="font-weight: bold; color: #333; font-size: 12px;">${commissions || '0.00'} ¥</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                        <div style="color: #666; font-size: 11px;">佣金比例</div>
                                        <div id="editPercent" style="font-weight: bold; color: #333; font-size: 12px;">${percent || '0'} %</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 6px; background: #fff; border-radius: 2px;">
                                        <div style="color: #666; font-size: 11px;">重量(kg)</div>
                                        <div id="editWeight" style="font-weight: bold; color: #333; font-size: 12px;">${weight || '0.00'}/${out_weight || '0.00'}</div>
                                    </div>
                                </div>
                            </div>
                            <div style="background: #2c3e50; padding: 10px; border-radius: 3px; text-align: center; margin-top: 8px; border: 2px solid #34495e;">
                                <div style="color: #bdc3c7; font-size: 11px; margin-bottom: 3px;">预估利润</div>
                                <div id="editProfit" style="color: #f1c40f; font-size: 18px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">计算中...</div>
                            </div>
                        </div>
                    </form>
                </div>
            `;
            layer.closeAll();
            layer.open({
                type: 1,
                title: false, // 不显示默认标题
                area: ['600px', '580px'], // 增加高度确保所有内容可见
                shade: [0.3, '#000'],
                closeBtn: 1,
                content: content,
                btn: ['保存修改', '取消'],
                btnAlign: 'c',
                btn1: function (index, layero) {
                    // 保存按钮逻辑
                    savePurchaseInfo(layero, index);
                }, btn2: function (index, layero) {
                    // 取消按钮逻辑，局部刷新当前订单

                    layer.close(index);
                },
                success: function (layero, index) {
                    // 绑定rowData到layero
                    layero.data('rowData', {
                        posting_number: posting_number, // 新增posting_number
                        cost: purchaseCost,
                        price: price,
                        delivery: delivery,
                        commissions: commissions,
                        percent: percent,
                        weight: weight,
                        out_weight: out_weight,
                        sku: sku || '',
                        quantity: quantity || 1,
                        offer_id: offer_id || sku || ''
                    });

                    // 添加额外的物流单号和采购单号
                    for (var i = 1; i < courierNumbers.length; i++) {
                        addCourierField(layero, courierNumbers[i]);
                    }
                    for (var i = 1; i < purchaseOrderSns.length; i++) {
                        addPurchaseOrderField(layero, purchaseOrderSns[i]);
                    }

                    form.render();

                    // 显示初始预估利润
                    updateProfit(layero);

                    // 事件绑定
                    bindPurchaseEditEvents(layero);
                }
            });
        }

        // 绑定采购编辑弹窗的事件
        function bindPurchaseEditEvents(layero) {
            // 更新单价按钮点击事件
            layero.find('#updatePriceBtn').off('click').on('click', function () {
                var newMoney = layero.find('#editMoneyInput').val().trim();
                if (!newMoney || isNaN(parseFloat(newMoney))) {
                    layer.msg('请输入有效的单价', { icon: 2 });
                    return;
                }

                var rowData = layero.data('rowData');
                var offer_id = rowData.offer_id || rowData.sku;

                if (!offer_id) {
                    layer.prompt({
                        formType: 0,
                        title: '请输入商品offer_id',
                        area: ['300px', '50px']
                    }, function (value, index, elem) {
                        offer_id = value.trim();
                        if (!offer_id) {
                            layer.msg('offer_id不能为空', { icon: 2 });
                            return;
                        }
                        layer.close(index);
                        updatePriceWithOfferId(offer_id, newMoney, layero);
                    });
                    return;
                }

                updatePriceWithOfferId(offer_id, newMoney, layero);
            });

            // 监听输入框变化
            layero.find('#editMoneyInput, #editPriceInput, #editCostInput').on('input', function () {
                var rowData = layero.data('rowData');

                if (this.id === 'editMoneyInput') {
                    var newMoney = $(this).val().trim();
                    if (newMoney && !isNaN(parseFloat(newMoney))) {
                        var quantity = rowData.quantity || 1;
                        var totalPrice = (parseFloat(newMoney) * quantity).toFixed(2);
                        layero.find('#editPriceInput').val(totalPrice);
                        rowData.money = newMoney;
                        rowData.price = totalPrice;
                    }
                } else if (this.id === 'editPriceInput') {
                    var newTotalPrice = $(this).val().trim();
                    if (newTotalPrice && !isNaN(parseFloat(newTotalPrice))) {
                        rowData.price = newTotalPrice;
                    }
                }

                layero.data('rowData', rowData);
                updateProfit(layero);
            });

            // 添加物流单号按钮
            layero.find('.add-courier').off('click').on('click', function () {
                addCourierField(layero);
            });

            // 添加采购单号按钮
            layero.find('.add-purchase').off('click').on('click', function () {
                addPurchaseOrderField(layero);
            });
        }

        // 更新价格的辅助函数
        function updatePriceWithOfferId(offer_id, money, layero) {
            var rowData = layero.data('rowData');
            if (rowData) {
                var quantity = rowData.quantity || 1;
                var totalPrice = (parseFloat(money) * quantity).toFixed(2);
                layero.find('#editPriceInput').val(totalPrice);
                rowData.money = money;
                rowData.price = totalPrice;
                layero.data('rowData', rowData);
                updateProfit(layero);
            }

            var loadingIndex = layer.load(2, { shade: [0.3, '#fff'] });
            updateOrderPrice(offer_id, money, function (success) {
                layer.close(loadingIndex);
                if (success) {
                    layer.msg('单价更新成功', { icon: 1 });
                    if (typeof table !== 'undefined' && table.reload) {
                        table.reload('orderTable');
                    }
                } else {
                    layer.msg('单价更新失败，请检查offer_id是否正确', { icon: 2 });
                }
            });
        }

        // 保存采购信息
        function savePurchaseInfo(layero, index) {
            var rowData = layero.data('rowData');
            if (!rowData || !rowData.posting_number) {
                layer.msg('数据错误：订单编号缺失', { icon: 2 });
                return false;
            }

            // 收集所有物流单号和采购单号
            var courierNumbers = [];
            var purchaseOrderSns = [];

            layero.find('input[name="courierNumber[]"]').each(function () {
                var value = $(this).val().trim();
                if (value) courierNumbers.push(value);
            });

            layero.find('input[name="purchase_orderSn[]"]').each(function () {
                var value = $(this).val().trim();
                if (value) purchaseOrderSns.push(value);
            });

            var courierNumberStr = courierNumbers.join(',');
            var purchaseOrderSnStr = purchaseOrderSns.join(',');
            var cost = layero.find('#editCostInput').val().trim();
            var newPrice = layero.find('#editPriceInput').val().trim();

            var loadingIndex = layer.load(2, { shade: [0.3, '#fff'] });

            $.ajax({
                url: 'ajax_order.php?act=update_order',
                type: 'POST',
                data: {
                    posting_number: rowData.posting_number,
                    cost: cost,
                    courierNumber: courierNumberStr,
                    purchase_orderSn: purchaseOrderSnStr,
                    price: newPrice
                },
                success: function (res) {
                    layer.close(loadingIndex);
                    if (res.code === 0) {
                        layer.msg('保存成功', { icon: 1 });
                        layer.close(index);

                        // 只刷新当前订单
                        if (rowData && rowData.posting_number) {
                            updateSingleOrder(rowData.posting_number);
                        } else { layer.msg('无法刷新订单：缺少posting_number', { icon: 7 }); }
                    } else {
                        layer.msg(res.msg || '保存失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.close(loadingIndex);
                    layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                }
            });
        }

        // 添加物流单号字段 - 超紧凑版本
        function addCourierField(layero, value) {
            var container = layero.find('#courierNumberContainer');
            var newItem = $('<div class="layui-form-item courier-item" style="margin-bottom: 6px;">' +
                '<label class="layui-form-label" style="width: 70px; padding: 8px 5px;"></label>' +
                '<div class="layui-input-block" style="margin-left: 80px; display: flex;">' +
                '<input type="text" name="courierNumber[]" placeholder="物流单号" class="layui-input" style="margin-right: 6px;">' +
                '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-field">' +
                '<i class="layui-icon layui-icon-delete"></i>' +
                '</button>' +
                '</div>' +
                '</div>');

            if (value) {
                newItem.find('input').val(value);
            }

            container.append(newItem);

            // 绑定删除按钮事件
            newItem.find('.remove-field').on('click', function () {
                newItem.remove();
            });
        }

        // 添加采购单号字段 - 超紧凑版本
        function addPurchaseOrderField(layero, value) {
            var container = layero.find('#purchaseOrderContainer');
            var newItem = $('<div class="layui-form-item purchase-item" style="margin-bottom: 6px;">' +
                '<label class="layui-form-label" style="width: 70px; padding: 8px 5px;"></label>' +
                '<div class="layui-input-block" style="margin-left: 80px; display: flex;">' +
                '<input type="text" name="purchase_orderSn[]" placeholder="采购单号" class="layui-input" style="margin-right: 6px;">' +
                '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-field">' +
                '<i class="layui-icon layui-icon-delete"></i>' +
                '</button>' +
                '</div>' +
                '</div>');

            if (value) {
                newItem.find('input').val(value);
            }

            container.append(newItem);

            // 绑定删除按钮事件
            newItem.find('.remove-field').on('click', function () {
                newItem.remove();
            });
        }

        // 更新订单价格
        function updateOrderPrice(offer_id, price, callback) {
            // 直接使用传入的offer_id和价格，不依赖订单ID
            if (!offer_id) {
                layer.msg('offer_id不存在，无法更新价格', { icon: 2 });
                if (callback) callback(false);
                return;
            }

            // 直接调用update_price接口
            $.ajax({
                url: 'ajax_order.php?act=update_price',
                type: 'POST',
                data: {
                    offer_id: offer_id, // 使用offer_id参数
                    price: price
                },
                dataType: 'json',
                success: function (res) {
                    if (res && res.code === 0) {
                        if (callback) callback(true);
                    } else {
                        if (callback) callback(false);
                    }
                },
                error: function (xhr) {
                    if (callback) callback(false);
                }
            });
        }

        // 更新预估利润计算
        function updateProfit(layero) {
            var rowData = layero.data('rowData');
            var costInput = layero.find('#editCostInput').val();
            var cost = parseFloat(costInput) || 0;

            // 直接从产品总价输入框获取总价
            var totalPrice = parseFloat(layero.find('#editPriceInput').val()) || 0;
            var delivery = parseFloat(rowData.delivery) || 0;
            var commissions = parseFloat(rowData.commissions) || 0;

            // 计算预估利润：产品总价 - 采购成本 - 物流费 - 佣金
            var profit = totalPrice - cost - delivery - commissions;
            profit = profit.toFixed(2);

            // 更新显示
            layero.find('#editProfit').text(profit + ' ¥');

            // 根据利润值设置颜色和样式
            var $profitElement = layero.find('#editProfit');
            if (profit > 0) {
                $profitElement.css({
                    'color': '#27ae60',
                    'text-shadow': '1px 1px 2px rgba(0,0,0,0.3)'
                });
            } else if (profit < 0) {
                $profitElement.css({
                    'color': '#e74c3c',
                    'text-shadow': '1px 1px 2px rgba(0,0,0,0.3)'
                });
            } else {
                $profitElement.css({
                    'color': '#f39c12',
                    'text-shadow': '1px 1px 2px rgba(0,0,0,0.3)'
                });
            }
        }

        // 状态样式映射
        const getStatusStyle = (status) => {
            const styleMap = {
                'awaiting_packaging': 'layui-bg-cyan',
                'awaiting_deliver': 'layui-bg-orange',
                'awaiting_deliver2': 'layui-bg-orange',
                'delivering': 'layui-bg-blue',
                'delivered': 'layui-bg-green',
                'cancelled': 'layui-bg-gray',
            };
            return styleMap[status] || '';
        }

        // 添加防抖机制，避免重复更新
        var updateTimers = {};

        // 添加局部更新订单函数 - 超快版本
        function updateSingleOrder(posting_number, retryCount) {
            if (!posting_number) return;

            // 防抖：如果正在更新同一个订单，取消之前的更新
            if (updateTimers[posting_number]) {
                clearTimeout(updateTimers[posting_number]);
            }

            // 设置防抖延迟
            updateTimers[posting_number] = setTimeout(function () {
                delete updateTimers[posting_number];
                performUpdate(posting_number, retryCount);
            }, 50); // 50ms防抖延迟

            function performUpdate(posting_number, retryCount) {
                // 重试次数限制
                retryCount = retryCount || 0;
                var maxRetries = 1; // 减少重试次数
                // 显示加载指示器
                var $orderDiv = $('#' + posting_number);
                if ($orderDiv.length > 0) {
                    $orderDiv.css('opacity', '0.5');
                }
                // 获取单个订单的最新数据
                $.ajax({
                    url: 'ajax_order.php?act=get_single_order',
                    type: 'POST',
                    data: { posting_number: posting_number },
                    timeout: 1000, // 进一步减少超时时间到1秒
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            // 查找对应的DOM元素并更新
                            var $orderDiv = $('#' + posting_number);
                            if ($orderDiv.length > 0) {
                                try {
                                    // 保存当前的滚动位置
                                    var scrollTop = $('.pear-page').scrollTop();
                                    // 快速数据预处理
                                    var safeData = res.data || {};
                                    // 只处理关键字段，避免遍历所有字段
                                    var criticalFields = ['posting_number', 'status', 'cost', 'purchase_orderSn', 'courierNumber', 'tracking_number', 'purchase_ok'];
                                    criticalFields.forEach(function (field) {
                                        if (safeData[field] === null || safeData[field] === undefined) {
                                            safeData[field] = '';
                                        }
                                    });

                                    // 使用模板渲染新的订单内容
                                    var newHtml = layui.laytpl($('#html').html()).render(safeData);
                                    $orderDiv.replaceWith(newHtml);

                                    // 快速重新绑定事件
                                    form.render();

                                    // 恢复滚动位置
                                    $('.pear-page').scrollTop(scrollTop);


                                } catch (e) {
                                    // 静默失败
                                } finally {
                                    // 恢复不透明度
                                    $orderDiv.css('opacity', '1');
                                }
                            }
                        }
                    },
                    error: function (xhr, status, error) {
                        // 如果是超时或网络错误，尝试重试
                        if ((status === 'timeout' || status === 'error') && retryCount < maxRetries) {
                            setTimeout(function () {
                                performUpdate(posting_number, retryCount + 1);
                            }, 300); // 减少重试延迟到300ms
                        }
                    },
                    complete: function () {
                        // 确保在请求完成后恢复不透明度
                        var $orderDiv = $('#' + posting_number);
                        if ($orderDiv.length > 0) {
                            $orderDiv.css('opacity', '1');
                        }
                    }
                });
            }
        }

        // 初始化加载
        // 页面加载时默认显示"待处理"标签，并显示"未采购"子标签
        currentStatus = 'not_purchased';
        $('#pendingSubTabs').show();
        $('#purchasedFilters').removeClass('show');

        // 确保子标签正确显示
        $('#pendingSubTabs > .layui-tab-title li').removeClass('layui-this');
        $('#pendingSubTabs > .layui-tab-title li:eq(1)').addClass('layui-this');

        // 优化：使用更小的数据集进行初始化
        //renderTable('not_purchased', '', '', '', '', '', '', '', 'in_process_at', 'desc');
        renderTable('not_purchased', '', '', '', '', '', '', '', 'in_process_at', sort_order, paypt, cancelled);

        var procurementModal = null;
        // 代发采购弹窗（修改后）
        $(document).on('click', '#showModal', function () {
            var $btn = $(this);

            // 添加加载状态
            $btn.addClass('loading');

            // 如果弹窗已存在则激活它
            if (procurementModal && layer.get(procurementModal)) {
                layer.setTop(procurementModal);
                $btn.removeClass('loading');
                return;
            }
            var $this = $(this),
                posting_number = $this.data('posting_number');
            var random = Math.random().toString(36).substring(2);
            layer.open({
                type: 2,
                id: 'procurementModal', // 唯一ID防止重复
                title: '代发采购',
                shadeClose: true,
                maxmin: true,
                area: ['1400px', '1000px'],
                content: './order_dfd_purchase_modal.php?posting_number=' + posting_number + '&r=' + random,
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    layer.close(index);
                    procurementModal = null;
                    $btn.removeClass('loading');
                    // 确定按钮：只刷新当前订单行，不进行同步操作
                    if (posting_number) {
                        updateSingleOrder(posting_number);
                    }
                },
                cancel: function (layero) {
                    procurementModal = null;
                    $btn.removeClass('loading');
                    // 取消按钮：刷新当前订单行
                    if (posting_number) {
                        updateSingleOrder(posting_number);
                    }
                },
                end: function (layero) {
                    procurementModal = null;
                    $btn.removeClass('loading');
                    // 取消按钮：刷新当前订单行
                    if (posting_number) {
                        updateSingleOrder(posting_number);
                    }
                }
            });

        });



        // 单个商品调用库存按钮点击事件
        $(document).on('click', '.btn-inventory', function () {
            var $btn = $(this);
            var sku = $btn.data('sku');
            var posting_number = $btn.data('posting_number');

            if (!sku) {
                layer.msg('SKU为空，无法查询库存', { icon: 2 });
                return;
            }

            sku = String(sku);
            // 查询匹配的产品库存记录
            var skuList = sku.split(',').map(function (item) { return item.trim(); }).filter(Boolean).join(',');
            layer.load(2);
            $.ajax({
                url: 'ajax.php',
                method: 'GET',
                data: {
                    act: 'query_platform_sku',
                    sku_list: skuList
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        if (res.data.length === 0) {
                            layer.msg('无匹配库存记录', { icon: 0 });
                            return;
                        }
                        // 构建库存调用表单
                        var content = '<form class="layui-form" id="callInventoryForm" style="padding: 20px;">';
                        content += '<input type="hidden" name="posting_number" value="' + posting_number + '">';
                        content += '<div class="layui-form-item">';
                        content += '<label class="layui-form-label">选择产品</label>';
                        content += '<div class="layui-input-block">';
                        content += '<select name="product_id" lay-filter="productSelect" required>';
                        res.data.forEach(function (item) {
                            content += '<option value="' + item.id + '" data-sku="' + item.sku + '" data-price="' + item.price + '">' + item.title + ' (SKU: ' + item.sku + ')</option>';
                        });
                        content += '</select>';
                        content += '</div></div>';
                        content += '<div class="layui-form-item">';
                        content += '<label class="layui-form-label">变动数量</label>';
                        content += '<div class="layui-input-block">';
                        content += '<input type="number" name="change_quantity" required lay-verify="required|number" min="1" value="1" class="layui-input">';
                        content += '</div></div>';
                        content += '<div class="layui-form-item">';
                        content += '<label class="layui-form-label">变动类型</label>';
                        content += '<div class="layui-input-block">';
                        content += '<select name="change_type" required>';
                        //content += '<option value="in">入库</option>';
                        content += '<option value="out">出库</option>';
                        content += '</select>';
                        content += '</div></div>';
                        content += '<div class="layui-form-item layui-form-text">';
                        content += '<label class="layui-form-label">备注</label>';
                        content += '<div class="layui-input-block">';
                        content += '<textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>';
                        content += '</div></div>';
                        content += '<div class="layui-form-item" style="text-align: center;">';
                        content += '<button class="layui-btn" lay-submit lay-filter="submitCallInventory">提交</button>';
                        content += '<button type="button" class="layui-btn layui-btn-primary" id="btnCancelCallInventory" style="margin-left: 10px;">取消</button>';
                        content += '</div></form>';

                        layer.open({
                            type: 1,
                            title: '调用库存',
                            area: ['500px', '550px'],
                            content: content,
                            success: function (layero, index) {
                                layui.form.render();
                                // 取消按钮事件
                                $('#btnCancelCallInventory').off('click').on('click', function () {
                                    layer.close(index);
                                });
                                // 监听表单提交
                                layui.form.on('submit(submitCallInventory)', function (formData) {
                                    var data = formData.field;
                                    data.act = 'update_purchase_info_inventory';
                                    data.posting_number = posting_number;
                                    data.sku = sku; // 添加当前操作的SKU
                                    layer.load(2);
                                    $.ajax({
                                        url: 'ajax.php',
                                        method: 'GET',
                                        data: data,
                                        success: function (res) {
                                            layer.closeAll('loading');
                                            if (res.code === 0) {
                                                layer.msg('调用库存成功，采购信息已更新', { icon: 1 });
                                                // 局部更新当前订单
                                                updateSingleOrder(posting_number);
                                                layer.closeAll('page');
                                            } else {
                                                layer.msg('调用库存失败: ' + (res.msg || ''), { icon: 2 });
                                            }
                                        },
                                        error: function () {
                                            layer.closeAll('loading');
                                            layer.msg('请求失败，请检查网络', { icon: 2 });
                                        }
                                    });
                                    return false;
                                });
                            }
                        });
                    } else {
                        layer.msg(res.msg || '查询失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });
        });

        // 多商品调用库存按钮点击事件
        $(document).on('click', '.btn-inventory-multi', function () {
            var $btn = $(this);
            var posting_number = $btn.data('posting_number');

            // 获取订单中所有商品的SKU
            layer.load(2);
            $.ajax({
                url: 'ajax_order.php',
                method: 'GET',
                data: {
                    act: 'get_order_products',
                    posting_number: posting_number
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0 && res.data && res.data.products_info) {
                        var products = res.data.products_info;
                        if (products.length === 0) {
                            layer.msg('订单中没有商品信息', { icon: 0 });
                            return;
                        }

                        // 收集所有SKU
                        var allSkus = products.map(function (item) {
                            return item.sku;
                        }).join(',');

                        // 查询所有SKU的库存记录
                        layer.load(2);
                        $.ajax({
                            url: 'ajax.php',
                            method: 'GET',
                            data: {
                                act: 'query_platform_sku',
                                sku_list: allSkus
                            },
                            success: function (inventoryRes) {
                                layer.closeAll('loading');
                                if (inventoryRes.code === 0) {
                                    if (inventoryRes.data.length === 0) {
                                        layer.msg('无匹配库存记录', { icon: 0 });
                                        return;
                                    }

                                    // 检查是否所有SKU都有库存记录
                                    var foundSkus = inventoryRes.data.map(function (item) {
                                        return item.sku;
                                    });

                                    var missingSkus = [];
                                    products.forEach(function (product) {
                                        if (!foundSkus.includes(product.sku)) {
                                            missingSkus.push(product.sku);
                                        }
                                    });

                                    if (missingSkus.length > 0) {
                                        layer.confirm('以下SKU没有找到库存记录: ' + missingSkus.join(', ') + '<br>是否继续?', {
                                            btn: ['继续', '取消']
                                        }, function (index) {
                                            layer.close(index);
                                            showInventoryForm(inventoryRes.data, posting_number, allSkus, products);
                                        });
                                    } else {
                                        showInventoryForm(inventoryRes.data, posting_number, allSkus, products);
                                    }
                                } else {
                                    layer.msg(inventoryRes.msg || '查询库存失败', { icon: 2 });
                                }
                            },
                            error: function () {
                                layer.closeAll('loading');
                                layer.msg('请求失败，请检查网络', { icon: 2 });
                            }
                        });
                    } else {
                        layer.msg(res.msg || '获取订单商品信息失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络', { icon: 2 });
                }
            });

            // 显示多商品库存调用表单
            function showInventoryForm(inventoryData, posting_number, skuList, products) {
                // 计算所有商品的总价
                var totalPrice = 0;
                products.forEach(function (product) {
                    var matchingInventory = inventoryData.find(function (item) {
                        return item.sku === product.sku;
                    });
                    if (matchingInventory) {
                        totalPrice += parseFloat(matchingInventory.price) * parseInt(product.quantity);
                    }
                });

                // 构建库存调用表单
                var content = '<form class="layui-form" id="callInventoryMultiForm" style="padding: 20px;">';
                content += '<input type="hidden" name="posting_number" value="' + posting_number + '">';
                content += '<input type="hidden" name="sku_list" value="' + skuList + '">';

                content += '<div class="layui-form-item">';
                content += '<label class="layui-form-label">订单商品</label>';
                content += '<div class="layui-input-block">';
                content += '<div class="layui-form-mid">' + products.length + '个商品</div>';
                content += '</div></div>';

                products.forEach(function (product, index) {
                    var matchingInventory = inventoryData.find(function (item) {
                        return item.sku === product.sku;
                    });

                    content += '<div class="layui-form-item">';
                    content += '<label class="layui-form-label">商品' + (index + 1) + '</label>';
                    content += '<div class="layui-input-block">';
                    if (matchingInventory) {
                        content += '<div class="layui-form-mid">' + matchingInventory.title + ' (SKU: ' + product.sku + ') - 数量: ' + product.quantity + '</div>';
                    } else {
                        content += '<div class="layui-form-mid" style="color: #FF5722;">SKU: ' + product.sku + ' - 未找到库存记录</div>';
                    }
                    content += '</div></div>';
                });

                content += '<div class="layui-form-item">';
                content += '<label class="layui-form-label">总采购金额</label>';
                content += '<div class="layui-input-block">';
                content += '<input type="number" name="total_cost" required lay-verify="required|number" min="0" value="' + totalPrice.toFixed(2) + '" class="layui-input">';
                content += '</div></div>';

                content += '<div class="layui-form-item">';
                content += '<label class="layui-form-label">变动类型</label>';
                content += '<div class="layui-input-block">';
                content += '<select name="change_type" required>';
                content += '<option value="out">出库</option>';
                content += '</select>';
                content += '</div></div>';

                content += '<div class="layui-form-item layui-form-text">';
                content += '<label class="layui-form-label">备注</label>';
                content += '<div class="layui-input-block">';
                content += '<textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>';
                content += '</div></div>';

                content += '<div class="layui-form-item" style="text-align: center;">';
                content += '<button class="layui-btn" lay-submit lay-filter="submitCallInventoryMulti">提交</button>';
                content += '<button type="button" class="layui-btn layui-btn-primary" id="btnCancelCallInventoryMulti" style="margin-left: 10px;">取消</button>';
                content += '</div></form>';

                layer.open({
                    type: 1,
                    title: '多商品调用库存',
                    area: ['600px', '600px'],
                    content: content,
                    success: function (layero, index) {
                        layui.form.render();
                        // 取消按钮事件
                        $('#btnCancelCallInventoryMulti').off('click').on('click', function () {
                            layer.close(index);
                        });

                        // 监听表单提交
                        layui.form.on('submit(submitCallInventoryMulti)', function (formData) {
                            var data = formData.field;
                            data.act = 'update_purchase_info_inventory_multi';
                            layer.load(2);
                            $.ajax({
                                url: 'ajax.php',
                                method: 'GET',
                                data: data,
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 0) {
                                        layer.msg('调用库存成功，采购信息已更新', { icon: 1 });
                                        // 局部更新当前订单
                                        updateSingleOrder(posting_number);
                                        layer.closeAll('page');
                                    } else {
                                        layer.msg('调用库存失败: ' + (res.msg || ''), { icon: 2 });
                                    }
                                },
                                error: function () {
                                    layer.closeAll('loading');
                                    layer.msg('请求失败，请检查网络', { icon: 2 });
                                }
                            });
                            return false;
                        });
                    }
                });
            }
        });
        
        //面单下载
        table.on('toolbar(orderTable)', function (obj) {

            if (obj.event === 'getCheckData') {
                // 获取所有选中的数据
                var checkboxes = document.querySelectorAll('.order-checkbox:checked');
                var posting_numbers = [];
                var invalidOrders = [];
                
                checkboxes.forEach(function (checkbox) {
                    var posting_number = checkbox.getAttribute('data-posting_number');
                    var status = checkbox.getAttribute('data-status');
                    var packing_status = checkbox.getAttribute('data-packing_status');
                    
                    // 允许等待发货、交运平台、运输中状态的订单打印
                    // 等待发货: awaiting_deliver && packing_status=0
                    // 交运平台: awaiting_deliver && packing_status=1
                    // 运输中: delivering
                    // 注意：awaiting_registration（移交给快递）状态不允许打印
                    if ((status === 'awaiting_deliver' && packing_status === '0') || 
                        (status === 'awaiting_deliver' && packing_status === '1') || 
                        status === 'delivering') {
                        posting_numbers.push(posting_number);
                    } else {
                        invalidOrders.push(posting_number);
                    }
                });

                if (posting_numbers.length === 0 && invalidOrders.length === 0) {
                    layer.msg('请选择要打印面单的订单', { icon: 2 });
                    return;
                }
                
                if (invalidOrders.length > 0) {
                    var invalidMsg = '以下订单状态不符合打印条件（必须为等待发货、交运平台或运输中状态）：\n' + invalidOrders.join('\n');
                    if (posting_numbers.length === 0) {
                        layer.msg(invalidMsg, { icon: 2 });
                        return;
                    } else {
                        layer.confirm(invalidMsg + '\n\n是否继续打印其他符合条件的订单？', {
                            icon: 3,
                            title: '提示'
                        }, function(index) {
                            layer.close(index);
                            // 继续执行打印逻辑
                            executePrint(posting_numbers);
                        });
                        return;
                    }
                }
                
                // 执行打印逻辑
                executePrint(posting_numbers);
                
                function executePrint(validPostingNumbers) {
                    var loadingIndex = layer.load(2, {
                        shade: [0.5, '#f5f5f5'],
                        time: 0,
                        content: '正在获取面单链接...'
                    });

                    $.ajax({
                        url: 'ajax_order.php?act=batch_print_order_tcpdf',
                        type: 'POST',
                        data: JSON.stringify({ posting_numbers: validPostingNumbers }),
                        dataType: 'json',
                        success: function (res) {
                            layer.close(loadingIndex);

                            if (res.code !== 0) {
                                layer.msg(res.msg || '批量打印失败', { icon: 2 });
                                return;
                            }

                            // 使用浏览器打印功能
                            directPrint(res.url);

                        },
                        error: function (xhr) {
                            layer.close(loadingIndex);
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        },
                        timeout: 30000
                    });
                }
            } else if (obj.event === 'exportOrders') {
                // get selected orders
                var checkboxes = document.querySelectorAll('.order-checkbox:checked');
                var posting_numbers = [];
                checkboxes.forEach(function (checkbox) {
                    posting_numbers.push(checkbox.getAttribute('data-posting_number'));
                });

                layer.open({
                    type: 1,
                    title: '导出订单',
                    area: ['650px', '600px'],
                    content: $('#exportOptionsTpl').html(),
                    btn: ['导出', '取消'],
                    success: function (layero, index) {
                        form.render(null, 'exportForm'); // Render the form in the modal

                        // Checkbox logic (select all, group select)
                        form.on('checkbox(selectAllFields)', function (data) {
                            var checked = data.elem.checked;
                            $(layero).find('.layui-card-body input[type="checkbox"]').prop('checked', checked);
                            form.render('checkbox');
                        });

                        form.on('checkbox(groupCheckbox)', function (data) {
                            var checked = data.elem.checked;
                            $(data.elem).closest('fieldset').next('.layui-form-item').find('input[type="checkbox"]').prop('checked', checked);
                            form.render('checkbox');
                        });
                    },
                    yes: function (index, layero) {
                        // "Export" button is clicked
                        var formData = form.val('exportForm');
                        var selectedFields = [];
                        // Correctly iterate over fields
                        $('input[name^="fields["]:checked').each(function () {
                            selectedFields.push($(this).attr('name').match(/fields\[(.*?)\]/)[1]);
                        });

                        if (selectedFields.length === 0) {
                            layer.msg('请至少选择一个要导出的字段', { icon: 2 });
                            return;
                        }

                        // Start export
                        var export_type = formData.export_type;
                        var display_type = formData.display_type;

                        // Show loading
                        var loadingIndex = layer.load(2, {
                            shade: [0.3, '#fff'],
                            content: '正在生成导出文件，请稍候...'
                        });

                        // Get current filter conditions from the main form
                        var searchParams = form.val('orderForm');

                        // Add selected posting numbers (if any)
                        searchParams.posting_numbers = posting_numbers.join(',');

                        // Add export options
                        searchParams.export_fields = selectedFields.join(',');
                        searchParams.export_type = export_type;
                        searchParams.display_type = display_type;

                        // Get sort info
                        searchParams.sort_field = 'in_process_at';
                        searchParams.sort_order = sort_order; // The global variable

                        // Add current status from tab
                        searchParams.status = currentStatus;

                        // Replace form submission with AJAX call
                        $.ajax({
                            url: 'ajax_order.php?act=export_orders',
                            type: 'POST',
                            data: searchParams,
                            dataType: 'json',
                            success: function (res) {
                                layer.close(loadingIndex);
                                if (res.code === 0) {
                                    layer.close(index); // close the modal
                                    layer.msg('文件已生成，即将开始下载', { icon: 1, time: 2000 });
                                    // Trigger download
                                    window.location.href = res.url;
                                } else {
                                    layer.msg(res.msg || '导出失败', { icon: 2 });
                                }
                            },
                            error: function (xhr) {
                                layer.close(loadingIndex);
                                layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                            }
                        });
                    }
                });
            } else if (obj.event === 'batchShipClick') {
                // 获取所有选中的数据
                var checkboxes = document.querySelectorAll('.order-checkbox:checked');
                var posting_numbers = [];
                checkboxes.forEach(function (checkbox) {
                    posting_numbers.push(checkbox.getAttribute('data-posting_number'));
                });

                if (posting_numbers.length === 0) {
                    layer.msg('请选择要交运的订单', { icon: 2 });
                    return;
                }
                layer.confirm('确定要批量交运吗？', {
                    icon: 3,
                    title: '批量交运确认',
                    btn: ['确定', '取消']
                }, function (index) {
                    layer.close(index); // 关闭弹窗
                    var loadingIndex = layer.load(2, {
                        shade: [0.5, '#f5f5f5'],
                        time: 0,
                        content: '正在批量交运...'
                    });
                    $.ajax({
                        url: 'ajax_order.php?act=batch_ship',
                        type: 'POST',
                        data: { posting_numbers: posting_numbers },
                        dataType: 'json',
                        success: function (res) {
                            layer.close(loadingIndex);
                            if (res.code === 0) {
                                layer.msg(res.msg, { icon: 1 });
                                //table.reload('orderTable');
                                // 局部更新所有选中的订单
                                posting_numbers.forEach(function (posting_number) {
                                    updateSingleOrder(posting_number);
                                });
                                // 重置勾选数量显示
                                setTimeout(function () {
                                    updateSelectedCount();
                                }, 500);
                            }
                        },
                        error: function (xhr) {
                            layer.close(loadingIndex);
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                });

            } else if(obj.event === 'SynchronizePinduoduoorders') {
                upordesdata('pdd');
            } else if(obj.event === 'Synchronize1688orders'){
                upordesdata('1688');
            } else if (obj.event === 'batchSetPurchase') {
                // 获取所有选中的数据
                var checkboxes = document.querySelectorAll('.order-checkbox:checked');
                var posting_numbers = [];
                checkboxes.forEach(function (checkbox) {
                    posting_numbers.push(checkbox.getAttribute('data-posting_number'));
                });

                if (posting_numbers.length === 0) {
                    layer.msg('请选择要批量设置采购信息的订单', { icon: 2 });
                    return;
                }

                // 显示批量采购信息填写弹窗
                var index = layer.open({
                    type: 1,
                    title: '批量设置采购信息 - 共' + posting_numbers.length + '个订单',
                    area: ['600px', '500px'],
                    content: `
                        <div style="padding: 20px;">
                            <form class="layui-form" lay-filter="batchPurchaseForm">
                                <input type="hidden" name="posting_numbers" value="${posting_numbers.join(',')}">
                                
                                <div class="layui-form-item">
                                    <label class="layui-form-label">采购成本</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="cost" placeholder="请输入采购成本" class="layui-input">
                                    </div>
                                </div>
                                
                                <div class="layui-form-item">
                                    <label class="layui-form-label">采购单号</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="purchase_orderSn" placeholder="请输入采购单号" class="layui-input">
                                    </div>
                                </div>
                                
                                <div class="layui-form-item">
                                    <label class="layui-form-label">快递单号</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="courierNumber" placeholder="请输入快递单号" class="layui-input">
                                    </div>
                                </div>
                                
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit lay-filter="submitBatchPurchase">开始批量设置</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>
                            </form>
                            
                            <!-- 进度显示区域 -->
                            <div id="batchProgress" style="margin-top: 20px; display: none;">
                                <div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="progressBar">
                                    <div class="layui-progress-bar" lay-percent="0%"></div>
                                </div>
                                <div id="progressText" style="text-align: center; margin-top: 10px;">
                                    正在处理 0/${posting_numbers.length}
                                </div>
                                <div id="successCount" style="color: #5FB878; margin-top: 5px;">成功: 0</div>
                                <div id="failCount" style="color: #FF5722; margin-top: 5px;">失败: 0</div>
                                <div id="currentProcessing" style="margin-top: 10px; color: #1E9FFF;"></div>
                            </div>
                        </div>
                    `,
                    success: function (layero, index) {
                        form.render();

                        // 监听表单提交
                        form.on('submit(submitBatchPurchase)', function (data) {
                            var field = data.field;

                            // 阻止表单默认提交行为
                            event.preventDefault();

                            // 显示进度区域
                            $('#batchProgress').show();

                            // 初始化进度条
                            element.progress('progressBar', '0%');

                            // 获取所有订单号
                            var posting_numbers = field.posting_numbers.split(',');
                            var total = posting_numbers.length;
                            var successCount = 0;
                            var failCount = 0;

                            // 禁用提交按钮
                            $('[lay-filter="submitBatchPurchase"]').addClass('layui-btn-disabled').text('批量处理中...');

                            // 逐个处理订单
                            function processNext(i) {
                                if (i >= total) {
                                    // 全部处理完成
                                    layer.msg(`批量设置完成！成功 ${successCount} 个，失败 ${failCount} 个`, { icon: 1 });
                                    $('[lay-filter="submitBatchPurchase"]').removeClass('layui-btn-disabled').text('开始批量设置');
                                    layer.closeAll();
                                    return;
                                }

                                var posting_number = posting_numbers[i];

                                // 更新当前处理信息
                                $('#currentProcessing').text(`正在处理: ${posting_number}`);
                                $('#progressText').text(`正在处理 ${i + 1}/${total}`);

                                // 计算进度
                                var progress = Math.round(((i) / total) * 100);
                                element.progress('progressBar', progress + '%');

                                // 准备请求数据
                                var requestData = {
                                    posting_number: posting_number,
                                    cost: field.cost,
                                    purchase_orderSn: field.purchase_orderSn,
                                    courierNumber: field.courierNumber
                                };
                                $.ajax({
                                    url: 'ajax_order.php?act=update_order',
                                    type: 'POST',
                                    data: requestData,
                                    success: function (res) {
                                        if (res.code === 0) {
                                            successCount++;
                                            $('#successCount').text('成功: ' + successCount);
                                            updateSingleOrder(posting_number);
                                        } else {
                                            failCount++;
                                            $('#failCount').text('失败: ' + failCount);
                                            console.error('更新失败:', posting_number, res.msg);
                                        }
                                        setTimeout(function () {
                                            processNext(i + 1);
                                        }, 300); // 添加300ms延迟，避免请求过于频繁
                                    },
                                    error: function (xhr) {
                                        failCount++;
                                        $('#failCount').text('失败: ' + failCount);
                                        console.error('请求失败:', posting_number, xhr.statusText);

                                        // 处理下一个订单
                                        setTimeout(function () {
                                            processNext(i + 1);
                                        }, 300);
                                    }
                                });
                            }

                            // 开始处理第一个订单
                            processNext(0);

                            return false; // 阻止表单跳转
                        });
                    }
                });
            } else if (obj.event === 'batch_order_package') {
                // 获取所有选中的数据
                var checkboxes = document.querySelectorAll('.order-checkbox:checked');
                var posting_numbers = [];
                checkboxes.forEach(function (checkbox) {
                    posting_numbers.push(checkbox.getAttribute('data-posting_number'));
                });

                if (posting_numbers.length === 0) {
                    layer.msg('请选择要移入备货的订单', { icon: 2 });
                    return;
                }
                layer.confirm('移入等待发货,将自动备货？', {
                    icon: 3,
                    title: '批量备货确认',
                    btn: ['确定', '取消']
                }, function (index) {
                    layer.close(index); // 关闭弹窗
                    var loadingIndex = layer.load(2, {
                        shade: [0.5, '#f5f5f5'],
                        time: 0,
                        content: '正在批量备货...'
                    });
                    $.ajax({
                        url: 'ajax_order.php?act=batch_order_package',
                        type: 'POST',
                        data: { posting_numbers: posting_numbers },
                        dataType: 'json',
                        success: function (res) {
                            layer.close(loadingIndex);
                            if (res.code === 0) {
                                layer.msg(res.msg, { icon: 1 });
                                // 局部更新所有选中的订单
                                table.reload('orderTable');

                                // 重置勾选数量显示
                                setTimeout(function () {
                                    updateSelectedCount();
                                }, 500);
                            }
                        },
                        error: function (xhr) {
                            layer.close(loadingIndex);
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                });
            }
        });

        // 直接打印函数
        function directPrint(url) {
            if (!url) {
                layer.msg('打印链接为空', { icon: 2 });
                return;
            }
            // 创建隐藏的iframe用于打印
            const printIframe = document.createElement('iframe');
            printIframe.style.position = 'fixed';
            printIframe.style.left = '-9999px';
            printIframe.style.top = '-9999px';
            printIframe.style.width = '0px';
            printIframe.style.height = '0px';
            printIframe.style.border = 'none';
            document.body.appendChild(printIframe);

            // 显示加载提示
            const loadingIndex = layer.load(2, {
                shade: [0.3, '#fff'],
                content: '正在准备打印...'
            });

            printIframe.onload = function () {
                try {
                    // 等待内容完全加载
                    setTimeout(() => {
                        layer.close(loadingIndex);

                        // 获取iframe的window对象
                        const printWindow = printIframe.contentWindow;

                        // 调用打印功能
                        printWindow.print();

                        // 显示打印提示，告诉用户打印对话框已打开
                        layer.msg('打印对话框已打开，请完成打印操作', { icon: 1, time: 3000 });

                        // 监听窗口焦点变化，当用户关闭打印对话框时清理iframe
                        let printDialogClosed = false;
                        let focusCheckInterval;

                        // 检查打印对话框是否关闭的函数
                        const checkPrintDialogStatus = () => {
                            // 通过检查窗口焦点来判断打印对话框是否关闭
                            if (document.hasFocus() && !printDialogClosed) {
                                printDialogClosed = true;
                                clearInterval(focusCheckInterval);

                                // 延迟清理iframe，确保打印完成
                                setTimeout(() => {
                                    if (document.body.contains(printIframe)) {
                                        document.body.removeChild(printIframe);
                                    }
                                }, 2000);
                            }
                        };

                        // 开始监听焦点变化
                        setTimeout(() => {
                            focusCheckInterval = setInterval(checkPrintDialogStatus, 500);
                        }, 1000);

                        // 备用清理机制，60秒后强制清理
                        setTimeout(() => {
                            if (document.body.contains(printIframe)) {
                                document.body.removeChild(printIframe);
                            }
                            if (focusCheckInterval) {
                                clearInterval(focusCheckInterval);
                            }
                        }, 60000);

                    }, 2000); // 等待2秒确保内容加载完成

                } catch (error) {
                    layer.close(loadingIndex);
                    layer.msg('打印失败: ' + error.message, { icon: 2 });
                    if (document.body.contains(printIframe)) {
                        document.body.removeChild(printIframe);
                    }
                }
            };

            printIframe.onerror = function () {
                layer.close(loadingIndex);
                layer.msg('加载打印内容失败', { icon: 2 });
                if (document.body.contains(printIframe)) {
                    document.body.removeChild(printIframe);
                }
            };

            // 设置iframe的src开始加载
            printIframe.src = url;

            // 超时处理
            setTimeout(() => {
                if (document.body.contains(printIframe)) {
                    layer.close(loadingIndex);
                    layer.msg('打印准备超时，请稍后重试', { icon: 2 });
                    document.body.removeChild(printIframe);
                }
            }, 30000); // 30秒超时
        }

        var isChineseTitle = true;
        // Use event delegation to handle click on toggleTitleBtn inside the toolbar
        $(document).on('click', '#toggleTitleBtn', function () {
            isChineseTitle = !isChineseTitle;
            var btn = this;
            if (isChineseTitle) {
                btn.innerText = '切换到俄文标题';
                // 显示中文标题，隐藏俄文标题
                document.querySelectorAll('[id^="title_chinese_"]').forEach(function (el) {
                    el.style.display = '';
                });
                document.querySelectorAll('[id^="title_russian_"]').forEach(function (el) {
                    el.style.display = 'none';
                });
            } else {
                btn.innerText = '切换到中文标题';
                // 显示俄文标题，隐藏中文标题
                document.querySelectorAll('[id^="title_chinese_"]').forEach(function (el) {
                    el.style.display = 'none';
                });
                document.querySelectorAll('[id^="title_russian_"]').forEach(function (el) {
                    el.style.display = '';
                });
            }
        });

        // 下单时间排序
        $(document).on('click', '#sortOrderTimeBtn', function () {
            var newOrder = sort_order === 'desc' ? 'asc' : 'desc';

            var text = $('input[name="text"]').val() || '';
            var money = $('input[name="money"]').val() || '';
            var moneys = $('input[name="moneys"]').val() || '';
            var storeid = $('select[name="storeid"]').val() || '';
            var date1 = $('input[name="date1"]').val() || '';
            var date2 = $('input[name="date2"]').val() || '';
            var groupid = $('select[name="groupid"]').val() || '';

            // 重新渲染表格，添加排序参数
            renderTable(currentStatus, text, money, moneys, storeid, date1, date2, groupid, 'in_process_at', newOrder, paypt, cancelled);
        });
        
        function upordesdata(type){
            // 获取所有订单
            $.ajax({
                url: 'ajax_order.php?act=get_ordersdata',
                type: 'POST',
                data: {type:type},
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0 && res.orders && res.orders.length > 0) {
                        // 创建进度弹层
                        var totalOrders = res.orders.length;
                        var processed = 0;
                        var successCount = 0;
                        var failCount = 0;
                        var isProcessing = true;
                        var timeouts = [];
                        var handlers = [];
                        
                        var progressLayer = layer.open({
                            type: 1,
                            title: '<i class="fas fa-tasks"></i> 订单处理进度',
                            content: `
                                <div class="progress-container">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-title">总订单数</div>
                                            <div class="stat-value">${totalOrders}</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-title">已处理</div>
                                            <div class="stat-value" id="processed">0</div>
                                        </div>
                                        <div class="stat-card stat-success">
                                            <div class="stat-title">成功</div>
                                            <div class="stat-value" id="successCount">0</div>
                                        </div>
                                        <div class="stat-card stat-fail">
                                            <div class="stat-title">失败</div>
                                            <div class="stat-value" id="failCount">0</div>
                                        </div>
                                    </div>
                                    
                                    <div class="current-order">
                                        <div class="current-label">当前处理:</div>
                                        <div class="order-sn" id="currentOrder">准备开始...</div>
                                    </div>
                                    
                                    <div class="progress-wrapper">
                                        <div class="progress-info">
                                            <span id="progressPercent">0%</span>
                                        </div>
                                        <div class="progress-bar">
                                            <div id="progressBar" class="progress-fill"></div>
                                        </div>
                                    </div>
                                </div>
                            `,
                            area: ['500px', 'auto'],
                            btn: ['<i class="fas fa-times"></i> 取消处理'],
                            btnClass: {
                                '0': 'cancel-btn'
                            },
                            yes: function() {
                                isProcessing = false;
                                timeouts.forEach(timeout => clearTimeout(timeout));
                                handlers.forEach(handler => window.removeEventListener("message", handler));
                                
                                layer.close(progressLayer);
                                layer.msg('<i class="fas fa-stop-circle"></i> 已取消处理', {icon: 2});
                            },
                            success: function(layero) {
                                // 添加Font Awesome图标库
                                var fa = document.createElement('link');
                                fa.rel = 'stylesheet';
                                fa.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
                                document.head.appendChild(fa);
                                
                                // 添加自定义样式
                                var style = document.createElement('style');
                                style.textContent = `
                                    .progress-container {
                                        padding: 20px;
                                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                                    }
                                    
                                    .stats-grid {
                                        display: grid;
                                        grid-template-columns: repeat(4, 1fr);
                                        gap: 12px;
                                        margin-bottom: 20px;
                                    }
                                    
                                    .stat-card {
                                        background: #fff;
                                        border-radius: 8px;
                                        padding: 15px;
                                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                                        text-align: center;
                                        border-top: 3px solid #1E9FFF;
                                    }
                                    
                                    .stat-success {
                                        border-top-color: #67C23A;
                                    }
                                    
                                    .stat-fail {
                                        border-top-color: #F56C6C;
                                    }
                                    
                                    .stat-title {
                                        font-size: 12px;
                                        color: #909399;
                                        margin-bottom: 5px;
                                    }
                                    
                                    .stat-value {
                                        font-size: 18px;
                                        font-weight: bold;
                                        color: #303133;
                                    }
                                    
                                    .current-order {
                                        background: #f5f7fa;
                                        border-radius: 6px;
                                        padding: 12px 15px;
                                        margin-bottom: 20px;
                                        display: flex;
                                        align-items: center;
                                    }
                                    
                                    .current-label {
                                        font-size: 13px;
                                        color: #909399;
                                        margin-right: 10px;
                                    }
                                    
                                    .order-sn {
                                        font-weight: 500;
                                        color: #303133;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                    }
                                    
                                    .progress-wrapper {
                                        margin-top: 25px;
                                    }
                                    
                                    .progress-info {
                                        display: flex;
                                        justify-content: space-between;
                                        margin-bottom: 8px;
                                        font-size: 13px;
                                    }
                                    
                                    .progress-bar {
                                        height: 10px;
                                        background: #ebeef5;
                                        border-radius: 5px;
                                        overflow: hidden;
                                    }
                                    
                                    .progress-fill {
                                        height: 100%;
                                        background: linear-gradient(90deg, #1E9FFF, #46A3FF);
                                        width: 0%;
                                        border-radius: 5px;
                                        transition: width 0.3s ease;
                                    }
                                    
                                    .cancel-btn {
                                        background: #F56C6C !important;
                                        color: white !important;
                                        border: none !important;
                                    }
                                    
                                    .layui-layer-title {
                                        font-weight: 500;
                                    }
                                    
                                    .layui-layer-title i {
                                        margin-right: 8px;
                                    }
                                `;
                                document.head.appendChild(style);
                            }
                        });
                        
                        // 逐个处理订单
                        function processNextOrder(index,type) {
                            if (!isProcessing || index >= totalOrders) {
                                if (isProcessing) {
                                    layer.close(progressLayer);
                                    layer.msg(`<i class="fas fa-check-circle"></i> 处理完成! 成功: ${successCount}, 失败: ${failCount}`, {icon: 1});
                                }
                                return;
                            }
                            
                            var orderSn = res.orders[index];
                            $('#currentOrder').text(orderSn);
                            if(type=='pdd'){
                                window.postMessage({
                                    direction: "from-page-script",
                                    message: {
                                        action: 'pddorder',
                                        orderId: orderSn
                                    }
                                }, "*");
                                
                                var timeout = setTimeout(function() {
                                    if (isProcessing) {
                                        failCount++;
                                        processed++;
                                        updateProgress();
                                        processNextOrder(index + 1,type);
                                    }
                                }, 10000);
                                
                                timeouts.push(timeout);
                                
                                function handleResponse(event) {
                                    if (event.data.direction == "from-content-script"){
                                        console.log(event.data.message);
                                        const apidata = event.data.message;
                                        if(apidata?.data?.order_sn === orderSn){
                                            clearTimeout(timeout);
                                            window.removeEventListener("message", handleResponse);
                                            
                                            if (isProcessing) {
                                                if (apidata.success == true) {
                                                    successCount++;
                                                } else {
                                                    failCount++;
                                                }
                                                
                                                processed++;
                                                updateProgress();
                                                processNextOrder(index + 1,type);
                                            }
                                        }else if(apidata?.message){
                                            layer.closeAll();
                                            layer.msg(apidata.message, { icon: 2, time: 3000 });
                                            return ;
                                        }
                                    }
                                }
                                
                                window.addEventListener("message", handleResponse);
                                handlers.push(handleResponse);
                            }else{
                                $.ajax({
                                    url: 'ajax_order.php?act=updata_kd',
                                    type: 'POST',
                                    data: {posting_number:orderSn},
                                    dataType: 'json',
                                    success: function (res) {
                                        if(res.code === 0){
                                            successCount++;
                                        }else{
                                            failCount++;
                                        }
                                        processed++;
                                        updateProgress();
                                        processNextOrder(index + 1,type);
                                    },
                                    error: function (xhr) {
                                        failCount++;
                                    }
                                });
                            }
                        }
                        
                        function updateProgress() {
                            $('#processed').text(processed);
                            $('#successCount').text(successCount);
                            $('#failCount').text(failCount);
                            var percent = Math.round((processed / totalOrders) * 100);
                            $('#progressBar').css('width', percent + '%');
                            $('#progressPercent').text(percent + '%');
                        }
                        
                        processNextOrder(0,type);
                        
                    } else {
                        layer.msg('<i class="fas fa-exclamation-circle"></i> 没有获取到订单数据', {icon: 2});
                    }
                },
                error: function (xhr) {
                    layer.msg('<i class="fas fa-times-circle"></i> 请求失败: ' + xhr.statusText, {icon: 2});
                }
            });
        }
    });
</script>

<!-- 导出订单弹窗模板 -->
<script type="text/html" id="exportOptionsTpl">
    <div id="exportOrderModal" style="padding: 20px;">
        <form class="layui-form" lay-filter="exportForm">
            <!-- 导出方式 -->
            <div class="layui-form-item">
                <label class="layui-form-label">导出方式:</label>
                <div class="layui-input-block">
                    <input type="radio" name="export_type" value="detail" title="导出明细" checked>
                    <input type="radio" name="export_type" value="summary" title="导出产品汇总" disabled>
                </div>
            </div>
            <!-- 显示方式 -->
            <div class="layui-form-item">
                <label class="layui-form-label">显示方式:</label>
                <div class="layui-input-block">
                    <input type="radio" name="display_type" value="order" title="按订单显示(每个订单导出一行)" checked>
                    <input type="radio" name="display_type" value="product" title="按产品显示(每个产品导出一行)" disabled>
                    <input type="radio" name="display_type" value="package" title="按包裹显示(每个包裹导出一行)" disabled>
                </div>
            </div>
            <!-- 选择模板 -->
            <div class="layui-form-item">
                <label class="layui-form-label">选择模板:</label>
                <div class="layui-input-inline">
                    <select name="template">
                        <option value="profit">利润</option>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <a href="javascript:;" id="addCustomTemplate" style="color: #1E9FFF;">添加自定义模板</a>
                </div>
            </div>

            <!-- 字段选择 -->
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 0;">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <input type="checkbox" name="select_all" title="全选" lay-skin="primary" lay-filter="selectAllFields">
                        </div>
                        <div class="layui-card-body" style="padding: 10px;">
                            <!-- 订单信息 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                                <legend><input type="checkbox" name="group_order_info" title="订单信息" lay-skin="primary" lay-filter="groupCheckbox"></legend>
                            </fieldset>
                            <div class="layui-form-item pane">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="fields[posting_number]" title="订单号" lay-skin="primary">
                                    <input type="checkbox" name="fields[in_process_at]" title="下单时间" lay-skin="primary">
                                    <input type="checkbox" name="fields[shipment_date]" title="发货截止时间" lay-skin="primary">
                                </div>
                            </div>
                            <!-- 产品信息 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                                <legend><input type="checkbox" name="group_product_info" title="产品信息" lay-skin="primary" lay-filter="groupCheckbox"></legend>
                            </fieldset>
                            <div class="layui-form-item pane">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="fields[primary_image]" title="产品图片" lay-skin="primary">
                                    <input type="checkbox" name="fields[sku]" title="SKU" lay-skin="primary">
                                    <input type="checkbox" name="fields[offer_id]" title="Offer ID" lay-skin="primary">
                                    <input type="checkbox" name="fields[order_name]" title="商品中文名" lay-skin="primary">
                                    <input type="checkbox" name="fields[name2]" title="商品俄文名" lay-skin="primary">
                                    <input type="checkbox" name="fields[pj]" title="规格" lay-skin="primary">
                                    <input type="checkbox" name="fields[quantity]" title="数量" lay-skin="primary">
                                </div>
                            </div>
                            <!-- 客户信息 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                                <legend><input type="checkbox" name="group_customer_info" title="客户信息" lay-skin="primary" lay-filter="groupCheckbox"></legend>
                            </fieldset>
                            <div class="layui-form-item pane">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="fields[customer_name]" title="客户姓名" lay-skin="primary">
                                    <input type="checkbox" name="fields[country]" title="国家" lay-skin="primary">
                                    <input type="checkbox" name="fields[region]" title="地区/邮编" lay-skin="primary">
                                </div>
                            </div>
                            <!-- 物流信息 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                                <legend><input type="checkbox" name="group_logistics_info" title="物流信息" lay-skin="primary" lay-filter="groupCheckbox"></legend>
                            </fieldset>
                            <div class="layui-form-item pane">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="fields[tracking_number]" title="Ozon运单号" lay-skin="primary">
                                    <input type="checkbox" name="fields[tpl_provider]" title="物流方式" lay-skin="primary">
                                    <input type="checkbox" name="fields[wl]" title="物流服务商" lay-skin="primary">
                                    <input type="checkbox" name="fields[speed]" title="运输方式" lay-skin="primary">
                                    <input type="checkbox" name="fields[warehouse]" title="发货仓库" lay-skin="primary">
                                </div>
                            </div>
                             <!-- 采购信息 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                                <legend><input type="checkbox" name="group_purchase_info" title="采购信息" lay-skin="primary" lay-filter="groupCheckbox"></legend>
                            </fieldset>
                            <div class="layui-form-item pane">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="fields[purchase_orderSn]" title="采购单号" lay-skin="primary">
                                    <input type="checkbox" name="fields[purchase_type]" title="采购平台" lay-skin="primary">
                                    <input type="checkbox" name="fields[purchase_ok]" title="采购状态" lay-skin="primary">
                                    <input type="checkbox" name="fields[courierNumber]" title="快递单号" lay-skin="primary">
                                    <input type="checkbox" name="fields[purchase_lus]" title="快递状态" lay-skin="primary">
                                </div>
                            </div>
                            <!-- 金额信息 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 10px;">
                                <legend><input type="checkbox" name="group_amount_info" title="金额信息" lay-skin="primary" lay-filter="groupCheckbox"></legend>
                            </fieldset>
                            <div class="layui-form-item pane">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="fields[money]" title="单价(RMB)" lay-skin="primary">
                                    <input type="checkbox" name="fields[price]" title="总价(RMB)" lay-skin="primary">
                                    <input type="checkbox" name="fields[cost]" title="采购成本(RMB)" lay-skin="primary">
                                    <input type="checkbox" name="fields[delivery]" title="预估运费(RMB)" lay-skin="primary">
                                    <input type="checkbox" name="fields[commissions]" title="平台佣金(RMB)" lay-skin="primary">
                                    <input type="checkbox" name="fields[percent]" title="佣金比例(%)" lay-skin="primary">
                                    <input type="checkbox" name="fields[profit]" title="预估利润(RMB)" lay-skin="primary">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 隐藏的提交按钮 -->
            <button class="layui-btn" lay-submit lay-filter="submitExport" style="display: none;"></button>
        </form>
    </div>
</script>
</body>
</html>
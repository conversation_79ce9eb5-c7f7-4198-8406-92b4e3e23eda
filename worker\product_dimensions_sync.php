<?php
/**
 * Ozon商品尺寸信息同步守护进程
 * 自动获取所有店铺的商品重量、尺寸等属性信息并同步到数据库
 * 
 * 使用方法:
 * 1. 单次执行: php product_dimensions_sync.php
 * 2. 守护进程: php product_dimensions_sync.php --daemon
 * 3. 调试模式: php product_dimensions_sync.php --debug
 * 4. 检查状态: php product_dimensions_sync.php --status
 * 5. 强制停止: php product_dimensions_sync.php --kill
 * 6. 快速模式: php product_dimensions_sync.php --fast (批量250，可能不稳定)
 * 7. 稳定模式: php product_dimensions_sync.php --slow (批量100，很稳定)
 * 8. 多进程模式: php product_dimensions_sync.php --multi=4 (4个进程并发)
 * 9. 宝塔进程守护: 直接运行此脚本，脚本会自动检测并避免重复运行
 * 
 * 宝塔进程守护管理器配置建议:
 * - 启动文件: /www/wwwroot/你的域名/worker/product_dimensions_sync.php
 * - 运行目录: /www/wwwroot/你的域名/worker
 * - 进程数量: 1 (重要！只能设置为1)
 * - 自动重启: 开启
 */

// 设置执行环境
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 0);
error_reporting(E_ALL);

// 检查是否在CLI模式下运行
if (php_sapi_name() !== 'cli') {
    header('Content-Type: text/plain');
    echo "此脚本只能在命令行模式下运行\n";
    exit(1);
}

// 设置正确的工作目录，确保相对路径正确
chdir(__DIR__ . '/..');
require_once __DIR__ . '/../includes/common.php';

// 内置配置
$config = [
    'daemon_mode' => false,           // 是否以守护进程模式运行
    'sync_interval' => 3600,         // 同步间隔（秒），默认1小时
    'batch_size' => 150,             // 每批处理的商品数量（降低到100避免API超时）
    'api_delay' => 8,                // 全局API调用间隔（秒），减少到8秒提高效率
    'max_execution_time' => 1800,    // 最大执行时间（秒），30分钟
    'log_level' => 'INFO',           // 日志级别: DEBUG, INFO, WARN, ERROR
    'max_products_per_store' => 50000, // 每个店铺最多处理的商品数量
    'log_file' => __DIR__ . '/logs/product_sync.log', // 日志文件路径
    'bt_daemon_mode' => false,       // 宝塔守护进程模式（自动检测）
    'api_timeout' => 30,             // API超时时间（秒）
    'max_retries' => 5,              // API调用最大重试次数
    'retry_delay' => 3,              // 重试间隔（秒）
    'adaptive_batch' => true,        // 启用自适应批量大小
];

// 全局变量
$running = true;
$logDir = __DIR__ . '/logs';

// 确保日志目录存在
if (!is_dir($logDir)) {
    if (!mkdir($logDir, 0755, true)) {
        echo "错误: 无法创建日志目录 {$logDir}\n";
        echo "请检查目录权限或手动创建该目录\n";
        exit(1);
    }
}

// 全局锁句柄存储（用于按店铺加锁）
$storeLocks = [];

// 检查命令行参数
$isDaemonMode = in_array('--daemon', $argv ?? []);
$isDebugMode = in_array('--debug', $argv ?? []);
$isStatusMode = in_array('--status', $argv ?? []);
$isKillMode = in_array('--kill', $argv ?? []);
$isFastMode = in_array('--fast', $argv ?? []);
$isSlowMode = in_array('--slow', $argv ?? []);
$isMultiProcessMode = in_array('--multi', $argv ?? []);
$processCount = 1; // 默认单进程

// 解析进程数量参数 --multi=4
foreach ($argv ?? [] as $arg) {
    if (strpos($arg, '--multi=') === 0) {
        $processCount = (int)substr($arg, 8);
        $isMultiProcessMode = true;
        break;
    }
}

// 状态检查模式
if ($isStatusMode) {
    checkProcessStatus();
    exit(0);
}

// 强制停止模式
if ($isKillMode) {
    killRunningProcesses();
    exit(0);
}

// 自动检测宝塔守护进程环境
function detectBaotaEnvironment() {
    // 检测是否在宝塔环境中运行
    $processName = isset($_SERVER['argv'][0]) ? basename($_SERVER['argv'][0]) : '';
    $ppid = function_exists('posix_getppid') ? posix_getppid() : 0;
    
    // 检查父进程或环境变量
    if ($ppid > 1) {
        // 在某个进程管理器下运行
        return true;
    }
    
    // 检查是否有宝塔相关的环境变量或路径
    if (strpos(__DIR__, '/www/wwwroot/') !== false) {
        return true;
    }
    
    return false;
}

// 设置模式
if ($isDaemonMode) {
    $config['daemon_mode'] = true;
    echo "启动守护进程模式...\n";
} elseif (detectBaotaEnvironment() && !$isStatusMode && !$isKillMode) {
    // 自动启用宝塔守护模式
    $config['bt_daemon_mode'] = true;
    $config['daemon_mode'] = true;
    echo "检测到宝塔环境，启动守护进程模式...\n";
}

if ($isDebugMode) {
    $config['log_level'] = 'DEBUG';
    echo "启用调试模式，将输出详细日志...\n";
}

if ($isFastMode) {
    // 快速模式：更大批量，更短间隔，但可能不稳定
    $config['batch_size'] = 250;
    $config['api_delay'] = 5;
    $config['retry_delay'] = 2;
    $config['adaptive_batch'] = true;
    echo "启用快速模式：批量150，API间隔5秒...\n";
}

if ($isSlowMode) {
    // 稳定模式：小批量，长间隔，更稳定
    $config['batch_size'] = 100;
    $config['api_delay'] = 12;
    $config['retry_delay'] = 5;
    $config['adaptive_batch'] = false;
    echo "启用稳定模式：批量100，API间隔12秒...\n";
}

if ($isMultiProcessMode && $processCount > 1) {
    // 多进程模式：增加API间隔以避免冲突
    $originalDelay = $config['api_delay'];
    $config['api_delay'] = $originalDelay * $processCount;
    echo "多进程模式：API间隔调整为 {$config['api_delay']} 秒（原{$originalDelay}秒 × {$processCount}进程）\n";
}

// 注册信号处理器（如果支持的话）
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, 'signalHandler');
    pcntl_signal(SIGINT, 'signalHandler');
    pcntl_signal(SIGHUP, 'signalHandler');
}

// 日志函数
function writeLog($level, $message, $data = null) {
    global $config;
    
    // 检查日志级别
    $levels = ['DEBUG' => 0, 'INFO' => 1, 'WARN' => 2, 'ERROR' => 3];
    $currentLevelValue = $levels[$config['log_level']] ?? 1;
    $messageLevelValue = $levels[$level] ?? 1;
    
    if ($messageLevelValue < $currentLevelValue) {
        return; // 跳过低级别日志
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message";
    
    if ($data) {
        $logEntry .= ' | Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    // 写入日志文件
    file_put_contents($config['log_file'], $logEntry . "\n", FILE_APPEND | LOCK_EX);
    
    // 输出到控制台
    echo $logEntry . "\n";
    
    // 日志轮转 - 保留最近7天
    if (rand(1, 100) === 1) {
        cleanOldLogs();
    }
}

// 清理旧日志
function cleanOldLogs() {
    global $logDir;
    
    $files = glob($logDir . '/*.log');
    foreach ($files as $file) {
        if (filemtime($file) < time() - (7 * 24 * 3600)) {
            unlink($file);
        }
    }
}

// 按店铺加锁函数
function lockStore($storeId) {
    global $logDir, $storeLocks;
    
    // 确保日志目录存在
    if (!is_dir($logDir)) {
        if (!mkdir($logDir, 0755, true)) {
            writeLog('ERROR', "无法创建日志目录", ['log_dir' => $logDir, 'store_id' => $storeId]);
            return false;
        }
    }
    
    $lockFile = $logDir . "/store_{$storeId}.lock";
    $lockHandle = fopen($lockFile, 'c+');
    
    // 检查文件句柄是否创建成功
    if ($lockHandle === false) {
        writeLog('ERROR', "无法创建店铺锁文件", [
            'store_id' => $storeId, 
            'lock_file' => $lockFile,
            'error' => error_get_last()['message'] ?? 'Unknown error'
        ]);
        return false;
    }
    
    if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
        fclose($lockHandle);
        writeLog('INFO', "店铺正被其他进程处理，跳过", ['store_id' => $storeId]);
        return false;
    }
    
    // 存储锁句柄
    $storeLocks[$storeId] = ['handle' => $lockHandle, 'file' => $lockFile];
    
    writeLog('DEBUG', "店铺锁定成功", ['store_id' => $storeId]);
    return true;
}

// 释放店铺锁
function unlockStore($storeId) {
    global $storeLocks;
    
    if (isset($storeLocks[$storeId])) {
        $lockInfo = $storeLocks[$storeId];
        flock($lockInfo['handle'], LOCK_UN);
        fclose($lockInfo['handle']);
        
        if (file_exists($lockInfo['file'])) {
            unlink($lockInfo['file']);
        }
        
        unset($storeLocks[$storeId]);
        writeLog('DEBUG', "店铺锁释放", ['store_id' => $storeId]);
    }
}

// 注册退出时释放所有锁
register_shutdown_function(function() {
    global $storeLocks;
    foreach ($storeLocks as $storeId => $lockInfo) {
        unlockStore($storeId);
    }
});

// 全局API调用间隔控制
function waitForApiInterval($storeId) {
    global $config, $logDir;
    
    // 确保日志目录存在
    if (!is_dir($logDir)) {
        if (!mkdir($logDir, 0755, true)) {
            writeLog('ERROR', "无法创建日志目录", ['log_dir' => $logDir, 'store_id' => $storeId]);
            return;
        }
    }
    
    $apiLockFile = $logDir . '/api_last_call.lock';
    $apiTimeFile = $logDir . '/api_last_call.time';
    
    // 获取API调用锁
    $apiLock = fopen($apiLockFile, 'c+');
    if ($apiLock === false) {
        writeLog('ERROR', "无法创建API锁文件", [
            'store_id' => $storeId,
            'lock_file' => $apiLockFile,
            'error' => error_get_last()['message'] ?? 'Unknown error'
        ]);
        return;
    }
    
    flock($apiLock, LOCK_EX); // 阻塞式锁定
    
    try {
        // 读取上次API调用时间
        $lastCallTime = 0;
        if (file_exists($apiTimeFile)) {
            $lastCallTime = (float)file_get_contents($apiTimeFile);
        }
        
        $currentTime = microtime(true);
        $timeSinceLastCall = $currentTime - $lastCallTime;
        $requiredInterval = $config['api_delay'];
        
        if ($timeSinceLastCall < $requiredInterval) {
            $waitTime = $requiredInterval - $timeSinceLastCall;
            writeLog('DEBUG', "全局API间隔控制，等待中", [
                'store_id' => $storeId,
                'wait_seconds' => round($waitTime, 2),
                'last_call_ago' => round($timeSinceLastCall, 2),
                'required_interval' => $requiredInterval
            ]);
            
            usleep($waitTime * 1000000); // 微秒
        }
        
        // 更新最后调用时间
        file_put_contents($apiTimeFile, microtime(true), LOCK_EX);
        
    } finally {
        flock($apiLock, LOCK_UN);
        fclose($apiLock);
    }
}

// 信号处理器
function signalHandler($signal) {
    global $running;
    
    writeLog('INFO', "接收到信号: $signal");
    
    switch ($signal) {
        case SIGTERM:
        case SIGINT:
            writeLog('INFO', '准备停止守护进程...');
            $running = false;
            break;
        case SIGHUP:
            writeLog('INFO', '重新加载配置...');
            break;
    }
}

// 主执行函数
function executeSync() {
    global $DB, $config;
    
    $startTime = time();
    writeLog('INFO', '开始执行商品尺寸信息同步任务');
    
    try {
        // 检查数据库连接
        if (!$DB) {
            throw new Exception('数据库连接失败');
        }
        
        // 先获取所有店铺进行调试
        $allStores = $DB->getAll("SELECT id, storename, apistatus, ClientId FROM ozon_store");
        
        // 检查查询结果是否有效
        if ($allStores === false) {
            throw new Exception('查询店铺数据失败');
        }
        
        // 确保返回的是数组
        if (!is_array($allStores)) {
            $allStores = [];
        }
        
        writeLog('INFO', '数据库中的店铺总数', ['total_stores' => count($allStores)]);
        
        if (!empty($allStores)) {
            $statusCounts = [];
            foreach ($allStores as $store) {
                $status = $store['apistatus'];
                $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
            }
            writeLog('INFO', '店铺状态分布', $statusCounts);
        }
        
        // 获取所有状态正常的店铺
        $stores = $DB->getAll("SELECT * FROM ozon_store WHERE apistatus = 1 AND `key` != ''");
        
        // 检查查询结果是否有效
        if ($stores === false) {
            throw new Exception('查询状态正常店铺数据失败');
        }
        
        // 确保返回的是数组
        if (!is_array($stores)) {
            $stores = [];
        }
        
        if (empty($stores)) {
            writeLog('WARN', '没有找到状态正常的店铺(apistatus=1)');
            
            // 如果没有apistatus=1的店铺，尝试查找其他状态的店铺
            $otherStores = $DB->getAll("SELECT * FROM ozon_store WHERE apistatus != 1");
            
            // 检查查询结果是否有效
            if ($otherStores === false) {
                throw new Exception('查询其他状态店铺数据失败');
            }
            
            // 确保返回的是数组
            if (!is_array($otherStores)) {
                $otherStores = [];
            }
            
            if (!empty($otherStores)) {
                writeLog('INFO', '发现其他状态的店铺，将处理所有店铺', ['count' => count($otherStores)]);
                $stores = $allStores; // 使用所有店铺
            } else {
                return;
            }
        }
        
        writeLog('INFO', '找到状态正常的店铺', ['count' => count($stores)]);
        
        $totalStats = [
            'stores_processed' => 0,
            'total_products' => 0,
            'products_synced' => 0,
            'products_updated' => 0,
            'api_calls' => 0,
            'errors' => 0
        ];
        
        // 处理每个店铺
        foreach ($stores as $store) {
            if (time() - $startTime > $config['max_execution_time']) {
                writeLog('WARN', '达到最大执行时间，停止处理');
                break;
            }
            
            // 尝试锁定店铺
            if (!lockStore($store['id'])) {
                // 店铺被其他进程处理，跳过
                continue;
            }
            
            try {
                $storeStats = processSingleStore($store);
                
                // 累计统计
                foreach ($totalStats as $key => $value) {
                    $totalStats[$key] += $storeStats[$key] ?? 0;
                }
            } finally {
                // 确保释放店铺锁
                unlockStore($store['id']);
            }
            
            // 注意：API调用间隔已移至全局控制，不再需要这里的sleep
        }
        
        $duration = time() - $startTime;
        writeLog('INFO', '同步任务执行完成', array_merge($totalStats, ['duration_seconds' => $duration]));
        
    } catch (Exception $e) {
        writeLog('ERROR', '同步任务执行失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    }
}

// 处理单个店铺
function processSingleStore($store) {
    global $config, $DB;
    
    $storeStartTime = time();
    writeLog('INFO', "开始处理店铺: {$store['storename']}", ['store_id' => $store['id'], 'client_id' => $store['ClientId']]);
    
    $stats = [
        'stores_processed' => 1,
        'total_products' => 0,
        'products_synced' => 0,
        'products_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    // 自适应批量大小控制
    $adaptiveBatchSize = $config['batch_size'];
    $consecutiveTimeouts = 0;
    $consecutiveSuccess = 0;
    
    try {
        // 获取店铺商品总数
        $result = $DB->getRow("SELECT COUNT(*) as total FROM ozon_products WHERE storeid = ?", [$store['id']]);
        
        // 检查count查询结果是否有效
        if ($result === false) {
            throw new Exception("查询店铺商品总数失败: {$store['id']}");
        }
        
        // 确保返回的是数字
        $totalProducts = (int)($result['total'] ?? 0);
        $stats['total_products'] = $totalProducts;
        
        writeLog('INFO', "店铺ID: {$store['id']} 一共 {$totalProducts} 个商品 开始时间: " . date('Y-m-d H:i:s', $storeStartTime));
        
        if ($totalProducts == 0) {
            writeLog('INFO', "店铺ID: {$store['id']} 没有商品，跳过处理");
            return $stats;
        }
        
        // 创建API客户端
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        
        $offset = 0;
        $processedProducts = 0;
        
        while ($processedProducts < $config['max_products_per_store'] && $offset < $totalProducts) {
            // 自适应批量大小调整
            if ($config['adaptive_batch']) {
                if ($consecutiveTimeouts >= 2) {
                    // 连续超时，减少批量大小
                    $adaptiveBatchSize = max(20, $adaptiveBatchSize - 20);
                    $consecutiveTimeouts = 0;
                    writeLog('INFO', "检测到API超时，降低批量大小", [
                        'store_id' => $store['id'],
                        'new_batch_size' => $adaptiveBatchSize
                    ]);
                } elseif ($consecutiveSuccess >= 3 && $adaptiveBatchSize < $config['batch_size']) {
                    // 连续成功，尝试增加批量大小
                    $adaptiveBatchSize = min($config['batch_size'], $adaptiveBatchSize + 10);
                    $consecutiveSuccess = 0;
                    writeLog('INFO', "API调用稳定，增加批量大小", [
                        'store_id' => $store['id'],
                        'new_batch_size' => $adaptiveBatchSize
                    ]);
                }
            }
            
            // 获取当前批次的商品（添加SKU有效性检查）
            $sql = "SELECT offer_id, sku FROM ozon_products 
                    WHERE storeid = {$store['id']} 
                    AND sku IS NOT NULL 
                    AND sku != '' 
                    AND sku REGEXP '^[0-9]+$'
                    LIMIT {$adaptiveBatchSize} OFFSET {$offset}";
            
            $products = $DB->getAll($sql);
            
            // 检查查询结果是否有效
            if ($products === false) {
                writeLog('ERROR', "查询商品数据失败", ['store_id' => $store['id'], 'offset' => $offset]);
                break;
            }
            
            // 确保返回的是数组
            if (!is_array($products)) {
                $products = [];
            }
            
            if (empty($products)) {
                writeLog('DEBUG', "没有更多有效商品", ['store_id' => $store['id'], 'offset' => $offset]);
                break;
            }
            
            // 二次验证SKU格式
            $validProducts = [];
            foreach ($products as $product) {
                if (isset($product['sku']) && 
                    !empty($product['sku']) && 
                    is_numeric($product['sku']) && 
                    preg_match('/^\d+$/', $product['sku'])) {
                    $validProducts[] = $product;
                } else {
                    writeLog('WARN', "跳过无效SKU商品", [
                        'store_id' => $store['id'],
                        'invalid_sku' => $product['sku'] ?? 'null',
                        'offer_id' => $product['offer_id'] ?? 'unknown'
                    ]);
                }
            }
            
            if (empty($validProducts)) {
                writeLog('DEBUG', "当前批次无有效商品", ['store_id' => $store['id'], 'offset' => $offset]);
                $offset += $config['batch_size'];
                continue;
            }
            
            $products = $validProducts;
            
            $batchCount = count($products);
            writeLog('INFO', "店铺ID: {$store['id']} 处理第 " . ($offset + 1) . "-" . ($offset + $batchCount) . " 个商品，共 {$batchCount} 个，批量大小: {$adaptiveBatchSize}");
            
            // 同步当前批次
            $batchStats = syncBatchProducts($products, $store, $client);
            
            // 根据批次结果调整自适应参数
            if ($config['adaptive_batch']) {
                if (isset($batchStats['has_timeout']) && $batchStats['has_timeout']) {
                    $consecutiveTimeouts++;
                    $consecutiveSuccess = 0;
                } else {
                    $consecutiveSuccess++;
                    $consecutiveTimeouts = 0;
                }
            }
            
            // 累计统计
            $stats['products_synced'] += $batchStats['products_synced'];
            $stats['products_updated'] += $batchStats['products_updated'];
            $stats['api_calls'] += $batchStats['api_calls'];
            $stats['errors'] += $batchStats['errors'];
            
            $offset += $adaptiveBatchSize; // 使用自适应批量大小
            $processedProducts += $batchCount;
            
            // 批次间隔（根据API性能动态调整）
            if ($offset < $totalProducts) {
                $batchDelay = 1; // 基础延迟1秒
                
                // 如果有超时，增加延迟
                if (isset($batchStats['has_timeout']) && $batchStats['has_timeout']) {
                    $batchDelay = 3;
                    writeLog('DEBUG', "检测到API超时，增加批次间隔", ['store_id' => $store['id'], 'delay' => $batchDelay]);
                }
                
                sleep($batchDelay);
            }
        }
        
        $duration = time() - $storeStartTime;
        
        // 计算性能指标
        $productsPerSecond = $stats['products_synced'] > 0 ? round($stats['products_synced'] / $duration, 2) : 0;
        $avgApiTime = $stats['api_calls'] > 0 ? round($duration / $stats['api_calls'], 2) : 0;
        
        writeLog('INFO', "店铺ID: {$store['id']} 处理完成 耗时: {$duration}秒", array_merge($stats, [
            'products_per_second' => $productsPerSecond,
            'avg_api_time_seconds' => $avgApiTime
        ]));
        
    } catch (Exception $e) {
        $stats['errors']++;
        writeLog('ERROR', "处理店铺失败: {$store['storename']}", ['store_id' => $store['id'], 'error' => $e->getMessage()]);
    }
    
    return $stats;
}

/**
 * 批量同步商品尺寸信息
 */
function syncBatchProducts($products, $store, $client) {
    // 确保products参数是有效的数组
    if (!is_array($products)) {
        writeLog('ERROR', "syncBatchProducts接收到无效的products参数", ['store_id' => $store['id']]);
        return [
            'products_synced' => 0,
            'products_updated' => 0,
            'api_calls' => 0,
            'errors' => 1
        ];
    }
    
    $stats = [
        'products_synced' => count($products),
        'products_updated' => 0,
        'api_calls' => 0,
        'errors' => 0,
        'has_timeout' => false
    ];
    
    try {
        // 提取SKU列表
        $skus = array_column($products, 'sku');
        $filters = ['sku' => $skus, 'visibility' => 'ALL'];
        
        writeLog('DEBUG', "调用Ozon API获取商品属性", ['store_id' => $store['id'], 'sku_count' => count($skus)]);
        
        // 全局API调用间隔控制
        waitForApiInterval($store['id']);
        
        // 调用Ozon API（改进的重试机制）
        $maxRetries = $config['max_retries'];
        $retryDelay = $config['retry_delay'];
        $response = null;
        
        for ($retry = 0; $retry <= $maxRetries; $retry++) {
            try {
                $apiStartTime = microtime(true);
                $response = $client->getProductAttributes($filters, count($skus));
                $apiDuration = round((microtime(true) - $apiStartTime) * 1000, 2); // 毫秒
                $stats['api_calls']++;
                
                writeLog('DEBUG', "API调用完成", [
                    'store_id' => $store['id'],
                    'duration_ms' => $apiDuration,
                    'sku_count' => count($skus),
                    'retry_attempt' => $retry
                ]);
                
                // 成功调用，跳出重试循环
                break;
                
            } catch (Exception $e) {
                $errorMsg = $e->getMessage();
                $isTimeout = strpos($errorMsg, 'timeout') !== false || strpos($errorMsg, 'timed out') !== false;
                $isLastRetry = ($retry === $maxRetries);
                
                // 标记超时状态
                if ($isTimeout) {
                    $stats['has_timeout'] = true;
                }
                
                writeLog($isLastRetry ? 'ERROR' : 'WARN', 
                    $isLastRetry ? "API调用最终失败" : "API调用失败，准备重试", [
                    'store_id' => $store['id'],
                    'error' => $errorMsg,
                    'error_code' => $e->getCode(),
                    'sku_count' => count($skus),
                    'retry_attempt' => $retry,
                    'is_timeout' => $isTimeout
                ]);
                
                if ($isLastRetry) {
                    $stats['errors']++;
                    return $stats;
                }
                
                // 根据错误类型调整重试间隔
                if ($isTimeout) {
                    $waitTime = $retryDelay * ($retry + 1); // 超时时递增等待时间
                } else {
                    $waitTime = $retryDelay; // 其他错误固定等待时间
                }
                
                writeLog('INFO', "等待后重试", [
                    'store_id' => $store['id'],
                    'wait_seconds' => $waitTime,
                    'retry_number' => $retry + 1
                ]);
                
                sleep($waitTime);
            }
        }
        
        // 1. 检查API响应是否为null或false
        if ($response === null || $response === false) {
            writeLog('ERROR', "API调用失败，返回null或false", ['store_id' => $store['id']]);
            $stats['errors']++;
            return $stats;
        }
        
        // 2. 检查响应是否为数组
        if (!is_array($response)) {
            writeLog('ERROR', "API返回非数组数据", [
                'store_id' => $store['id'], 
                'response_type' => gettype($response),
                'response_preview' => is_string($response) ? substr($response, 0, 200) : 'non-string'
            ]);
            $stats['errors']++;
            return $stats;
        }
        
        // 3. 检查API错误
        if (isset($response['error'])) {
            writeLog('ERROR', "API返回错误信息", [
                'store_id' => $store['id'], 
                'error' => $response['error'],
                'full_response' => json_encode($response, JSON_UNESCAPED_UNICODE)
            ]);
            $stats['errors']++;
            return $stats;
        }
        
        // 4. 检查result字段
        if (!isset($response['result'])) {
            writeLog('ERROR', "API响应缺少result字段", [
                'store_id' => $store['id'], 
                'response_keys' => array_keys($response),
                'response' => json_encode($response, JSON_UNESCAPED_UNICODE)
            ]);
            $stats['errors']++;
            return $stats;
        }
        
        // 5. 检查result是否为数组
        if (!is_array($response['result'])) {
            writeLog('ERROR', "API响应result字段不是数组", [
                'store_id' => $store['id'], 
                'result_type' => gettype($response['result']),
                'result_value' => $response['result']
            ]);
            $stats['errors']++;
            return $stats;
        }
        
        $apiProducts = $response['result'];
        writeLog('DEBUG', "API返回商品信息", ['store_id' => $store['id'], 'returned_count' => count($apiProducts)]);
        
        // 异常计数器，防止日志爆炸
        $batchErrors = 0;
        $maxBatchErrors = 10; // 单批次最多允许10个异常
        
        // 处理每个商品
        foreach ($apiProducts as $index => $productInfo) {
            // 检查批次异常数量
            if ($batchErrors >= $maxBatchErrors) {
                writeLog('ERROR', "批次异常过多，停止处理", [
                    'store_id' => $store['id'],
                    'batch_errors' => $batchErrors,
                    'remaining_products' => count($apiProducts) - $index
                ]);
                break;
            }
            
            // 验证商品数据结构
            if (!is_array($productInfo)) {
                writeLog('WARN', "商品数据不是数组", [
                    'store_id' => $store['id'],
                    'product_index' => $index,
                    'product_type' => gettype($productInfo),
                    'product_value' => $productInfo
                ]);
                $stats['errors']++;
                $batchErrors++;
                continue;
            }
            
            // 验证SKU
            if (!isset($productInfo['sku']) || empty($productInfo['sku'])) {
                writeLog('WARN', "商品SKU为空或不存在", [
                    'store_id' => $store['id'],
                    'product_index' => $index,
                    'product_keys' => array_keys($productInfo)
                ]);
                continue;
            }
            
            // 验证SKU格式（应该是数字字符串）
            if (!is_scalar($productInfo['sku']) || !preg_match('/^\d+$/', (string)$productInfo['sku'])) {
                writeLog('WARN', "商品SKU格式无效", [
                    'store_id' => $store['id'],
                    'sku' => $productInfo['sku'],
                    'sku_type' => gettype($productInfo['sku'])
                ]);
                continue;
            }
            
            // 提取尺寸数据（带错误处理）
            try {
                $updateData = extractProductDimensions($productInfo);
            } catch (Exception $e) {
                writeLog('ERROR', "提取商品尺寸数据失败", [
                    'store_id' => $store['id'],
                    'sku' => $productInfo['sku'],
                    'error' => $e->getMessage(),
                    'product_data' => json_encode($productInfo, JSON_UNESCAPED_UNICODE)
                ]);
                $stats['errors']++;
                $batchErrors++;
                continue;
            }
            
            if (!empty($updateData)) {
                global $DB;
                
                // 确保数据类型正确
                $storeIdInt = (int)$store['id'];
                $updateDataFixed = [];
                foreach ($updateData as $field => $value) {
                    $updateDataFixed[$field] = (int)$value;
                }
                
                try {
                    // 先检查当前记录的值，避免无意义的更新
                    $currentRecord = $DB->getRow("SELECT weight, depth, width, height FROM ozon_products WHERE storeid = ? AND sku = ?", [$storeIdInt, $productInfo['sku']]);
                    
                    if (!$currentRecord) {
                        writeLog('WARN', "商品记录不存在", [
                            'store_id' => $store['id'],
                            'sku' => $productInfo['sku']
                        ]);
                        continue;
                    }
                    
                    // 检查是否需要更新
                    $needsUpdate = false;
                    $changedFields = [];
                    foreach ($updateDataFixed as $field => $newValue) {
                        $currentValue = (int)($currentRecord[$field] ?? 0);
                        if ($currentValue !== $newValue) {
                            $needsUpdate = true;
                            $changedFields[$field] = [
                                'old' => $currentValue,
                                'new' => $newValue
                            ];
                        }
                    }
                    
                    if (!$needsUpdate) {
                        writeLog('DEBUG', "商品尺寸数据无变化，跳过更新", [
                            'store_id' => $store['id'],
                            'sku' => $productInfo['sku'],
                            'current_data' => [
                                'weight' => (int)($currentRecord['weight'] ?? 0),
                                'depth' => (int)($currentRecord['depth'] ?? 0),
                                'width' => (int)($currentRecord['width'] ?? 0),
                                'height' => (int)($currentRecord['height'] ?? 0)
                            ]
                        ]);
                        continue;
                    }
                    
                    // 构建SQL语句 - 只更新有变化的字段
                    $setParts = [];
                    $values = [];
                    foreach ($updateDataFixed as $field => $value) {
                        if (isset($changedFields[$field])) {
                            $setParts[] = "`{$field}` = ?";
                            $values[] = $value;
                        }
                    }
                    $values[] = $storeIdInt;
                    $values[] = $productInfo['sku'];
                    
                    $sql = "UPDATE ozon_products SET " . implode(', ', $setParts) . " WHERE storeid = ? AND sku = ?";
                    
                    // 使用PdoHelper的query方法
                    $stmt = $DB->query($sql, $values);
                    $affectedRows = $stmt ? $stmt->rowCount() : 0;
                    
                    if ($stmt && $affectedRows > 0) {
                        $stats['products_updated']++;
                        writeLog('INFO', "商品尺寸更新成功", [
                            'store_id' => $store['id'],
                            'sku' => $productInfo['sku'],
                            'changed_fields' => $changedFields,
                            'affected_rows' => $affectedRows
                        ]);
                    } else {
                        writeLog('ERROR', "数据库更新失败", [
                            'store_id' => $store['id'],
                            'sku' => $productInfo['sku'],
                            'db_error' => $DB->error(),
                            'sql' => $sql,
                            'values' => $values
                        ]);
                        $stats['errors']++;
                    }
                    
                } catch (Exception $e) {
                    writeLog('ERROR', "数据库操作异常", [
                        'store_id' => $store['id'],
                        'sku' => $productInfo['sku'],
                        'error' => $e->getMessage()
                    ]);
                    $stats['errors']++;
                    $batchErrors++;
                }
            }
        }
        
        writeLog('INFO', "批次同步完成", [
            'store_id' => $store['id'],
            'synced' => $stats['products_synced'],
            'updated' => $stats['products_updated'],
            'errors' => $stats['errors']
        ]);
        
    } catch (Exception $e) {
        writeLog('ERROR', "批次同步失败", ['store_id' => $store['id'], 'error' => $e->getMessage()]);
        $stats['errors']++;
    }
    
    return $stats;
}

/**
 * 从API响应中提取商品尺寸信息（健壮版本）
 */
function extractProductDimensions($productInfo) {
    $updateData = [];
    
    // 验证输入参数
    if (!is_array($productInfo)) {
        throw new Exception("商品信息不是数组: " . gettype($productInfo));
    }
    
    // 尺寸字段定义（字段名 => [最小值, 最大值, 描述]）
    $dimensionFields = [
        'weight' => [1, 100000, '重量(克)'],      // 1克到100kg
        'depth' => [1, 5000, '深度(毫米)'],       // 1mm到5m
        'width' => [1, 5000, '宽度(毫米)'],       // 1mm到5m  
        'height' => [1, 5000, '高度(毫米)']       // 1mm到5m
    ];
    
    foreach ($dimensionFields as $field => $config) {
        list($minValue, $maxValue, $description) = $config;
        
        if (!isset($productInfo[$field])) {
            continue; // 字段不存在，跳过
        }
        
        $value = $productInfo[$field];
        
        // 类型检查和转换
        if (!is_numeric($value)) {
            if (is_string($value) && preg_match('/^\d+(\.\d+)?$/', $value)) {
                // 数字字符串，尝试转换
                $value = floatval($value);
            } else {
                writeLog('WARN', "字段值不是数字", [
                    'field' => $field,
                    'value' => $value,
                    'type' => gettype($value),
                    'sku' => $productInfo['sku'] ?? 'unknown'
                ]);
                continue;
            }
        }
        
        // 转换为整数
        $intValue = (int)round($value);
        
        // 范围检查
        if ($intValue < $minValue) {
            writeLog('DEBUG', "字段值小于最小值，跳过", [
                'field' => $field,
                'value' => $intValue,
                'min_value' => $minValue,
                'description' => $description,
                'sku' => $productInfo['sku'] ?? 'unknown'
            ]);
            continue;
        }
        
        if ($intValue > $maxValue) {
            writeLog('WARN', "字段值超过最大值，使用最大值", [
                'field' => $field,
                'original_value' => $intValue,
                'max_value' => $maxValue,
                'description' => $description,
                'sku' => $productInfo['sku'] ?? 'unknown'
            ]);
            $intValue = $maxValue;
        }
        
        // 验证通过，添加到更新数据
        $updateData[$field] = $intValue;
        
        writeLog('DEBUG', "提取尺寸字段成功", [
            'field' => $field,
            'value' => $intValue,
            'original_value' => $productInfo[$field],
            'sku' => $productInfo['sku'] ?? 'unknown'
        ]);
    }
    
    // 记录提取结果
    if (empty($updateData)) {
        writeLog('DEBUG', "未提取到有效尺寸数据", [
            'sku' => $productInfo['sku'] ?? 'unknown',
            'available_fields' => array_keys($productInfo)
        ]);
    } else {
        writeLog('DEBUG', "尺寸数据提取完成", [
            'sku' => $productInfo['sku'] ?? 'unknown',
            'extracted_fields' => array_keys($updateData),
            'data' => $updateData
        ]);
    }
    
    return $updateData;
}

/**
 * 多进程执行函数
 */
function executeMultiProcess($processCount) {
    global $DB, $config;
    
    writeLog('INFO', "启动多进程模式", ['process_count' => $processCount]);
    
    if (!function_exists('pcntl_fork')) {
        writeLog('ERROR', '多进程需要pcntl扩展支持');
        echo "错误：PHP需要安装pcntl扩展才能支持多进程\n";
        echo "在宝塔面板中：软件商店 -> PHP -> 设置 -> 安装扩展 -> pcntl\n";
        return false;
    }
    
    // 获取所有店铺
    $stores = $DB->getAll("SELECT * FROM ozon_store WHERE apistatus = 1 AND `key` != ''");
    if (empty($stores)) {
        writeLog('WARN', '没有找到可处理的店铺');
        return false;
    }
    
    $totalStores = count($stores);
    writeLog('INFO', "找到 {$totalStores} 个店铺，将分配给 {$processCount} 个进程");
    
    // 分配店铺给不同进程
    $storeGroups = array_chunk($stores, ceil($totalStores / $processCount));
    $children = [];
    
    for ($i = 0; $i < count($storeGroups); $i++) {
        $pid = pcntl_fork();
        
        if ($pid == -1) {
            writeLog('ERROR', "进程 {$i} 创建失败");
            continue;
        } elseif ($pid == 0) {
            // 子进程
            $processId = $i + 1;
            $storeGroup = $storeGroups[$i];
            
            writeLog('INFO', "进程 {$processId} 启动", [
                'pid' => getmypid(),
                'stores_count' => count($storeGroup),
                'store_ids' => array_column($storeGroup, 'id')
            ]);
            
            executeProcessStores($storeGroup, $processId);
            exit(0); // 子进程完成后退出
        } else {
            // 父进程
            $children[] = $pid;
            writeLog('INFO', "创建子进程", ['process_id' => $i + 1, 'pid' => $pid]);
        }
    }
    
    // 等待所有子进程完成
    $totalStats = [
        'processes' => count($children),
        'completed' => 0,
        'total_duration' => 0
    ];
    
    $startTime = time();
    foreach ($children as $pid) {
        $status = 0;
        pcntl_waitpid($pid, $status);
        $totalStats['completed']++;
        writeLog('INFO', "子进程完成", [
            'pid' => $pid, 
            'completed' => $totalStats['completed'],
            'total' => $totalStats['processes']
        ]);
    }
    
    $totalStats['total_duration'] = time() - $startTime;
    writeLog('INFO', '多进程执行完成', $totalStats);
    
    return true;
}

/**
 * 进程处理指定的店铺组
 */
function executeProcessStores($stores, $processId) {
    global $config;
    
    $processStats = [
        'process_id' => $processId,
        'stores_assigned' => count($stores),
        'stores_processed' => 0,
        'total_products' => 0,
        'products_synced' => 0,
        'products_updated' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    $startTime = time();
    
    foreach ($stores as $store) {
        // 检查是否超过最大执行时间
        if (time() - $startTime > $config['max_execution_time']) {
            writeLog('WARN', "进程 {$processId} 达到最大执行时间，停止处理");
            break;
        }
        
        // 尝试锁定店铺
        if (!lockStore($store['id'])) {
            writeLog('INFO', "进程 {$processId} 跳过已被锁定的店铺", ['store_id' => $store['id']]);
            continue;
        }
        
        try {
            writeLog('INFO', "进程 {$processId} 开始处理店铺", [
                'store_id' => $store['id'],
                'store_name' => $store['storename']
            ]);
            
            $storeStats = processSingleStore($store);
            
            // 累计统计
            foreach ($processStats as $key => $value) {
                if (isset($storeStats[$key]) && is_numeric($value)) {
                    $processStats[$key] += $storeStats[$key];
                }
            }
            $processStats['stores_processed']++;
            
        } finally {
            unlockStore($store['id']);
        }
        
        // 进程间延迟，避免资源争抢
        sleep(2);
    }
    
    $duration = time() - $startTime;
    $processStats['duration'] = $duration;
    
    writeLog('INFO', "进程 {$processId} 处理完成", $processStats);
}

/**
 * 守护进程循环
 */
function daemonLoop() {
    global $config, $running;
    
    writeLog('INFO', '启动守护进程模式', ['sync_interval' => $config['sync_interval']]);
    
    while ($running) {
        try {
            executeSync();
            
            if ($running) {
                writeLog('INFO', "同步完成，等待下次执行", ['next_sync_in_seconds' => $config['sync_interval']]);
                sleep($config['sync_interval']);
            }
            
        } catch (Exception $e) {
            writeLog('ERROR', '守护进程执行异常', ['error' => $e->getMessage()]);
            sleep(60); // 出错时等待1分钟再重试
        }
        
        // 处理信号
        if (function_exists('pcntl_signal_dispatch')) {
            pcntl_signal_dispatch();
        }
    }
    
    writeLog('INFO', '守护进程正常退出');
}

// 检查进程状态
function checkProcessStatus() {
    global $logDir;
    
    $lockFile = $logDir . '/global_sync.lock';
    echo "=== 商品尺寸同步进程状态 ===\n";
    
    if (!file_exists($lockFile)) {
        echo "状态: 未运行\n";
        echo "锁文件: 不存在\n";
        return;
    }
    
    $lockHandle = fopen($lockFile, 'r');
    if (!$lockHandle) {
        echo "状态: 无法读取锁文件\n";
        return;
    }
    
    if (flock($lockHandle, LOCK_EX | LOCK_NB)) {
        // 能够获取锁说明没有进程在运行
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        unlink($lockFile);
        echo "状态: 未运行 (清理了废弃的锁文件)\n";
        return;
    }
    
    // 读取锁文件内容
    rewind($lockHandle);
    $content = fread($lockHandle, 1024);
    fclose($lockHandle);
    
    echo "状态: 正在运行\n";
    echo "锁文件信息:\n";
    echo $content;
    
    // 检查最新日志
    $logFile = $logDir . '/product_sync.log';
    if (file_exists($logFile)) {
        $lastLines = tail($logFile, 5);
        echo "\n最近日志:\n";
        echo $lastLines;
    }
}

// 强制停止所有运行的进程
function killRunningProcesses() {
    global $logDir;
    
    echo "正在强制停止所有同步进程...\n";
    
    // 删除全局锁文件
    $lockFile = $logDir . '/global_sync.lock';
    if (file_exists($lockFile)) {
        unlink($lockFile);
        echo "已删除全局锁文件\n";
    }
    
    // 删除所有店铺锁文件
    $lockFiles = glob($logDir . '/store_*.lock');
    foreach ($lockFiles as $file) {
        unlink($file);
    }
    echo "已删除 " . count($lockFiles) . " 个店铺锁文件\n";
    
    // 删除API锁文件
    $apiLockFile = $logDir . '/api_last_call.lock';
    $apiTimeFile = $logDir . '/api_last_call.time';
    if (file_exists($apiLockFile)) {
        unlink($apiLockFile);
    }
    if (file_exists($apiTimeFile)) {
        unlink($apiTimeFile);
    }
    echo "已删除API锁文件\n";
    
    echo "强制停止完成\n";
}

// 读取文件的最后几行
function tail($filename, $lines = 10) {
    if (!file_exists($filename)) {
        return "文件不存在\n";
    }
    
    $handle = fopen($filename, "r");
    if (!$handle) {
        return "无法读取文件\n";
    }
    
    $result = [];
    while (($line = fgets($handle)) !== false) {
        $result[] = $line;
        if (count($result) > $lines) {
            array_shift($result);
        }
    }
    
    fclose($handle);
    return implode('', $result);
}

// 全局进程锁，防止多个实例同时运行
function acquireGlobalLock() {
    global $logDir;
    
    // 确保日志目录存在
    if (!is_dir($logDir)) {
        if (!mkdir($logDir, 0755, true)) {
            echo "错误: 无法创建日志目录 {$logDir}\n";
            exit(1);
        }
    }
    
    $lockFile = $logDir . '/global_sync.lock';
    $lockHandle = fopen($lockFile, 'c+');
    
    // 检查文件句柄是否创建成功
    if ($lockHandle === false) {
        $error = error_get_last()['message'] ?? 'Unknown error';
        echo "错误: 无法创建全局锁文件 {$lockFile}: {$error}\n";
        exit(1);
    }
    
    if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
        fclose($lockHandle);
        writeLog('INFO', '另一个同步进程正在运行，退出当前进程');
        // 在宝塔环境下，等待一段时间后再退出，避免频繁重启
        global $config;
        if ($config['bt_daemon_mode']) {
            writeLog('INFO', '宝塔守护模式：等待60秒后退出，避免频繁重启');
            sleep(60);
        }
        exit(0);
    }
    
    // 写入进程信息
    ftruncate($lockHandle, 0);
    fwrite($lockHandle, "PID: " . getmypid() . "\nStart: " . date('Y-m-d H:i:s') . "\n");
    
    writeLog('INFO', '获取全局锁成功', ['pid' => getmypid()]);
    return $lockHandle;
}

// 主程序入口
try {
    // 多进程模式不需要全局锁，因为每个进程处理不同的店铺
    $globalLock = null;
    if (!$isMultiProcessMode || $processCount <= 1) {
        // 获取全局锁，防止多实例运行
        $globalLock = acquireGlobalLock();
        
        // 注册退出时释放全局锁
        register_shutdown_function(function() use ($globalLock) {
            if ($globalLock) {
                flock($globalLock, LOCK_UN);
                fclose($globalLock);
                writeLog('DEBUG', '全局锁已释放');
            }
        });
    }
    
    if ($isMultiProcessMode && $processCount > 1) {
        // 多进程模式
        echo "启动多进程模式，进程数: {$processCount}\n";
        executeMultiProcess($processCount);
    } elseif ($config['daemon_mode']) {
        daemonLoop();
    } else {
        executeSync();
    }
} catch (Exception $e) {
    writeLog('ERROR', '程序执行失败', ['error' => $e->getMessage()]);
    exit(1);
}

writeLog('INFO', '程序执行完成');
?>
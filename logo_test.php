<?php

function addLogoWatermark($imageUrls, $logoUrl, $position = 'top-right', $scale = 0.1, $opacity = 0.8, $margin = 20) {
    $apiUrl = 'http://127.0.0.1:8000/logo-watermark';
    
    $data = [
        'image_urls' => $imageUrls,
        'logo_url' => $logoUrl,
        'position' => $position,
        'scale' => $scale,
        'opacity' => $opacity,
        'margin' => $margin
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($apiUrl, false, $context);
    
    if ($result === FALSE) {
        throw new Exception('调用logo水印服务失败');
    }
    
    return json_decode($result, true);
}

// Logo水印测试
echo "开始Logo水印服务测试...\n";

try {
    // 测试图片URL
    $imageUrls = [
        'https://picsum.photos/800/600',  // 随机测试图片1
        'https://picsum.photos/1024/768'  // 随机测试图片2
    ];
    
    // Logo URL (使用一个简单的logo图片)
    $logoUrl = 'https://via.placeholder.com/200x100/FF0000/FFFFFF?text=LOGO';
    
    echo "测试图片URL:\n";
    foreach ($imageUrls as $url) {
        echo "- $url\n";
    }
    echo "\nLogo URL: $logoUrl\n";
    
    // 测试不同位置的logo水印
    $positions = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
    
    foreach ($positions as $position) {
        echo "\n正在添加Logo水印 (位置: $position)...\n";
        $result = addLogoWatermark(
            $imageUrls,
            $logoUrl,
            $position,
            0.15,  // logo大小为图片宽度的15%
            0.8,   // 80%透明度
            30     // 30像素边距
        );
        
        // 输出结果
        echo "处理成功！\n";
        echo "处理后的图片路径：\n";
        foreach ($result['processed_paths'] as $path) {
            echo "- $path\n";
        }
    }
    
} catch (Exception $e) {
    echo "\n错误：" . $e->getMessage() . "\n";
}

echo "\nLogo水印测试完成。\n";
?>
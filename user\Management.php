<link rel="stylesheet" href="../assets/css/Management.css">
<script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>

<style>
    /* 尺寸重量筛选区域样式优化 */
    .search-form-container .layui-form-item .layui-inline {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .search-form-container .layui-form-mid {
        padding: 0 5px;
        color: #666;
        font-weight: bold;
    }

    /* 尺寸重量输入框样式 */
    .search-form-container input[type="number"] {
        text-align: center;
    }

    /* 标签样式调整 */
    .search-form-container .layui-form-label {
        width: 80px;
        text-align: right;
        padding-right: 10px;
    }

    /* 响应式处理 */
    @media (max-width: 1200px) {
        .search-form-container .layui-inline {
            margin-bottom: 8px;
        }

        .search-form-container .layui-form-label {
            width: 70px;
            font-size: 12px;
        }
    }
</style>

<!-- Layui风格的回到顶部按钮 -->
<button id="layuiBackToTop" class="layui-btn layui-btn-danger layui-btn-radius"
    style="position: fixed; right: 20px; bottom: 130px; z-index: 99999; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
    <i class="layui-icon layui-icon-up" style="font-size: 38px; color: #fff;"></i>
</button>
<button id="layuiBackTodown" class="layui-btn layui-btn-danger layui-btn-radius"
    style="position: fixed; right: 20px; bottom: 90px; z-index: 99999; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
    <i class="layui-icon layui-icon-down" style="font-size: 38px; color: #fff;"></i>
</button>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">商品管理</div>
        <div class="layui-card-body">
            <!-- 搜索区域结构调整 -->
            <div class="search-form-container">
                <form class="layui-form layui-form-pane" lay-filter="searchForm">
                    <div class="layui-form-item">
                        <!-- 第一行 -->
                        <div class="layui-inline">
                            <label class="layui-form-label">SKU</label>
                            <div class="layui-input-inline">
                                <input type="text" name="sku" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">货号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="offer_id" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">商品名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name" class="layui-input" placeholder="请输入商品名称">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">商品状态</label>
                            <div class="layui-input-inline">
                                <select name="status" lay-search>
                                    <option value="">全部状态</option>
                                    <option value="1">正常销售</option>
                                    <option value="2">已归档</option>
                                    <option value="3">审核中</option>
                                    <option value="4">已下架</option>
                                    <option value="5">库存不足</option>
                                    <option value="6">重复出现</option>
                                    <option value="7">不可出售</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <!-- 第二行 -->
                        <div class="layui-inline">
                            <label class="layui-form-label">促销状态</label>
                            <div class="layui-input-inline">
                                <select name="promotion" lay-search>
                                    <option value="">全部</option>
                                    <option value="1">参加促销</option>
                                    <option value="0">未参加促销</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">SKU状态</label>
                            <div class="layui-input-inline">
                                <select name="sku_status" lay-search>
                                    <option value="">全部</option>
                                    <option value="null">SKU为空</option>
                                    <option value="not_null">SKU不为空</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">店铺</label>
                            <div class="layui-input-inline">
                                <select name="shop_id" lay-search multiple>
                                    <option value="">所有店铺</option>
                                </select>
                            </div>
                        </div>



                        <div class="layui-inline">
                            <label class="layui-form-label">分组</label>
                            <div class="layui-input-inline">
                                <select name="group_id" lay-search id="groupSelect">
                                    <option value="">所有分组</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <!-- 第三行 - 重量和销量筛选 -->
                        <div class="layui-inline">
                            <label class="layui-form-label">重量(g)</label>
                            <div class="layui-input-inline" style="width: 70px;">
                                <input type="number" name="weight_min" class="layui-input" placeholder="最小">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 70px;">
                                <input type="number" name="weight_max" class="layui-input" placeholder="最大">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">销量</label>
                            <div class="layui-input-inline" style="width: 70px;">
                                <input type="number" name="salesvolume_min" class="layui-input" placeholder="最小">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 70px;">
                                <input type="number" name="salesvolume_max" class="layui-input" placeholder="最大">
                            </div>
                        </div>


                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="search">搜索</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
            <!-- 操作按钮区域 -->
            <div class="layui-btn-container" style="margin-bottom: 15px;">
                <button class="layui-btn layui-btn-normal" id="syncProducts">
                    <i class="layui-icon layui-icon-refresh"></i> 同步商品
                </button>
                <button class="layui-btn layui-btn-danger" id="batchOperation">
                    <i class="layui-icon layui-icon-edit"></i> 批量操作
                </button>
                <button class="layui-btn layui-bg-purple" id="Batchresend">
                    <i class="layui-icon layui-icon-release"></i> 批量重传
                </button>
                <button class="layui-btn layui-bg-orange" id="batchArchive">
                    <i class="layui-icon layui-icon-file"></i> 批量归档
                </button>
            </div>

            <!-- 表格区域 -->
            <table id="productTable" lay-filter="productTable"></table>
            <!-- 表格操作列模板 -->
        </div>
    </div>
</div>
<script type="text/html" id="operationTpl">
    <div class="operation-btns">
        <button class="layui-btn layui-btn-xs" lay-event="setSource"><i class="layui-icon layui-icon-link"></i>货源</button>
        <button class="layui-btn layui-btn-xs" lay-event="editPrice"><i class="layui-icon layui-icon-rmb"></i>价格</button></br>
        <button class="layui-btn layui-btn-xs" lay-event="editStock"><i class="layui-icon layui-icon-cart"></i>库存</button>
        <button class="layui-btn layui-btn-xs" lay-event="editProduct"><i class="layui-icon layui-icon-edit"></i>编辑</button></br>
        <button class="layui-btn layui-btn-xs" lay-event="sync"><i class="layui-icon layui-icon-refresh"></i>同步</button>
        <button class="layui-btn layui-btn-xs" lay-event="reupload"><i class="layui-icon layui-icon-upload"></i>重传</button>
    </div>
</script>

<!-- 上传状态模板 -->
<script type="text/html" id="uploadStatusTpl">
<!--{{# if(d.upload_status === 1){ }}
    <span class="layui-badge layui-bg-green">上传成功</span>
{{# } else if(d.upload_status === 2){ }}
    <span class="layui-badge layui-bg-orange">上传中</span>
{{# } else { }}
    <span class="layui-badge layui-bg-red">上传异常</span>
{{# } }}-->

{{# if(d.status === 1){ }}
<span class="layui-badge layui-bg-green">正常销售</span>
{{# } else if(d.status === 2){ }}
<span class="layui-badge layui-bg-gray">已归档</span>
{{# } else if(d.status === 3){ }}
<span class="layui-badge layui-bg-orange">审核中</span>
{{# } else if(d.status === 4){ }}
<span class="layui-badge layui-bg-red">已下架</span>
{{# } else if(d.status === 5){ }}
<span class="layui-badge layui-bg-orange">库存不足</span>
{{# } else if(d.status === 6){ }}
<span class="layui-badge layui-bg-red" title="{{d.errors}}">重复出现</span>
{{# } else if(d.status === 7){ }}
<span class="layui-badge layui-bg-red">不可出售</span>
{{# } }}
</script>

<!-- 货源链接模板 -->
<script type="text/html" id="sourceLinkTpl">
    {{# if(d.source_link){ }}
        <span class="layui-badge layui-bg-green">已设置</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">未设置</span>
    {{# } }}
</script>
<script>
    layui.use(['table', 'form', 'layer', 'util'], function () {
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var util = layui.util;
        var $ = layui.$;

        // 使用Layui的工具函数实现回到顶部
        $('#layuiBackToTop').on('click', function () {
            // 直接获取pear-page作为滚动容器
            var scrollContainer = document.querySelector('.pear-page');

            if (scrollContainer && scrollContainer.scrollTop > 0) {
                scrollContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                console.log('Layui回到顶部按钮初始化成功');
            }
            return false;
        });
        
        $('#layuiBackTodown').on('click', function () {
            // 直接获取pear-page作为滚动容器
            var scrollContainer = document.querySelector('.pear-page');

            if (scrollContainer && scrollContainer.scrollTop > 0) {
                scrollContainer.scrollTo({
                    top: 100,
                    behavior: 'smooth'
                });
                console.log('Layui回到顶部按钮初始化成功');
            }
            return false;
        });
        

        initTable();
        // 初始化表格
        function initTable() {
            table.render({
                elem: '#productTable',
                cellMinWidth: 50, // 全局最小列宽
                autoSort: false,
                totalRow: false,
                scrollX: true,     // 启用横向滚动
                scrollY: '50vh',   // 固定高度
                lineStyle: 'height: 140px;', // 设置行高

                defaultToolbar: ['filter', 'exports', 'print', { // 右上角工具图标
                    title: '提示',
                    layEvent: 'LAYTABLE_TIPS',
                    icon: 'layui-icon-tips',
                    onClick: function (obj) { // 2.9.12+
                        layer.alert('自定义工具栏图标按钮');
                    }
                }],
                url: 'ajax_products.php?act=productlist',
                page: true,
                limit: 20,
                limits: [20, 50, 100, 200, 500],
                toolbar: true,
                defaultToolbar: ['filter', 'exports', 'print'],
                cols: [[
                    {
                        type: 'checkbox'
                    },
                    {
                        // field: 'primary_image', // 移除field，图片列不导出
                        title: '商品图',
                        width: 110, // 固定图片列宽度
                        templet: function (d) {
                            return '<img src="' + (d.primary_image || '/static/images/default-product.png') + '" style="object-fit:cover;width:80px;height:80px;padding:0;box-sizing:border-box;cursor:pointer;" class="product-img-preview" data-original="' + (d.primary_image || '/static/images/default-product.png') + '" title="点击查看大图">';
                        }
                    },
                    {
                        // field已在上一版本移除，保持
                        title: '商品名称/SKU/货号/店铺',
                        width: 350,
                        minWidth: 300,
                        templet: function (d) {
                            return '<a href="https://www.ozon.ru/product/' + d.sku + '/" target="_blank">'
                                + '<div class="eeb-t" title="' + d.name + '" style="color: #1E9FFF; margin-bottom: 8px; font-weight: bold; line-height: 1.4; word-wrap: break-word; overflow-wrap: break-word;">' + d.name + '</div></a>'
                                + '<div class="ee-c" style="line-height: 1.3;">Sku: <a href="https://www.ozon.ru/product/' + d.sku + '/" target="_blank">' + d.sku + '</a></div>'
                                + '<div style="line-height: 1.3;">' + d.offer_id + '</div>'
                                + '<div style="line-height: 1.3;">店铺: ' + d.storename + '</div>';
                        }
                    },
                    {
                        // field: 'price', // 移除field
                        title: '价格',
                        width: 130,
                        sort: true,
                        templet: function (d) {
                            return '<div class="price-detail-trigger" data-sku="' + d.sku + '">'
                                + '单位: ' + d.currency_code + '</br>折前¥: ' + d.old_price + '</br>折后¥: ' + d.price
                                + "</br>" + d.commissions
                                + '</div>';
                        }
                    },
                    {
                        // field: 'stock', // 移除field
                        title: '库存',
                        width: 80,
                        sort: true,
                        templet: function (d) {
                            if (d.stock <= 0 && d.warehouse_name) {
                                return '<span style="color: red;">仓库: ' + d.warehouse_name + '</span>';
                            }
                            return d.stock;
                        }
                    },
                    {
                        // field: 'upload_status', // 移除field
                        title: '商品状态',
                        width: 100, 
                        templet: '#uploadStatusTpl'
                    },
                    {
                        // field: 'dimensions', // 移除field
                        title: '货品尺寸(mm)',
                        width: 80,
                        templet: function (d) {
                            return '<div class="dimension-display">'
                                + '长: ' + (d.depth || 0) + '<br>'
                                + '宽: ' + (d.width || 0) + '<br>'
                                + '高: ' + (d.height || 0)
                                + '</div>';
                        }
                    },
                    {
                        // field: 'weight', // 移除field
                        title: '包装重量(g)',
                        width: 80,
                        templet: function (d) {
                            return '<div class="dimension-display">'
                                + '重量: ' + (d.weight || 0) + '<br>'
                                + '</div>';
                        }
                    },
                    {
                        // field: 'salesvolume', // 移除field
                        title: '销量',
                        width: 80,
                        sort: true,
                        templet: function (d) {
                            return '<div class="dimension-display">'
                                + '销量: ' + (d.salesvolume || 0) + '<br>'
                                + '</div>';
                        }
                    },
                    {
                        // field: 'created_at', // 移除field
                        title: '创建/更新日期',
                        width: 180,
                        sort: true,
                        templet: function (d) {
                            return d.created_at + '</br>' + (d.updated_at || '-');
                        }
                    },
                    {
                        // field: 'source_link', // 移除field
                        title: '货源链接',
                        width: 120,
                        templet: '#sourceLinkTpl'
                    },

                    // --- 隐藏列，专门用于数据导出 ---
                    { field: 'name', title: '商品名称', hide: true },
                    { field: 'sku', title: 'SKU', hide: true },
                    { field: 'offer_id', title: '货号', hide: true },
                    { field: 'storename', title: '店铺', hide: true },
                    { field: 'old_price', title: '折前价格', hide: true },
                    { field: 'price', title: '折后价格', hide: true },
                    { field: 'currency_code', title: '货币单位', hide: true },
                    { field: 'commissions', title: '佣金', hide: true },
                    { field: 'stock', title: '库存数量', hide: true },
                    { field: 'warehouse_name', title: '仓库名称', hide: true },
                    { field: 'status', title: '商品状态值', hide: true },
                    { field: 'depth', title: '长(mm)', hide: true },
                    { field: 'width', title: '宽(mm)', hide: true },
                    { field: 'height', title: '高(mm)', hide: true },
                    { field: 'weight', title: '重量(g)', hide: true },
                    { field: 'salesvolume', title: '销量', hide: true },
                    { field: 'created_at', title: '创建日期', hide: true },
                    { field: 'updated_at', title: '更新日期', hide: true },
                    { field: 'source_link', title: '货源链接地址', hide: true },
                    // --- 隐藏列结束 ---

                    {
                        title: '操作',
                        width: 170, // 固定操作列宽度
                        align: 'center',
                        fixed: 'right', // 固定右侧位置
                        toolbar: '#operationTpl'
                    }
                ]],
                parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data
                    };
                },
                text: {
                    none: '暂无相关数据'
                },
                done: function (res, curr, count) {
                    $(window).on('resize', function () {
                        table.resize('productTable');
                    });
                    
                    // 绑定图片点击放大事件
                    $('#productTable').next('.layui-table-view').on('click', '.product-img-preview', function () {
                        var imgSrc = $(this).data('original');
                        var imgTitle = $(this).closest('tr').find('.eeb-t').text() || '商品图片';
                        
                        layer.photos({
                            photos: {
                                title: imgTitle,
                                data: [{
                                    alt: imgTitle,
                                    pid: 1,
                                    src: imgSrc,
                                    thumb: imgSrc
                                }]
                            },
                            anim: 5, // 切换动画：0-6
                            shade: 0.8, // 遮罩透明度
                            shadeClose: true, // 点击遮罩关闭
                            closeBtn: 1, // 显示关闭按钮
                            move: false // 禁止拖拽
                        });
                    });
                    
                    // 表格渲染完成后绑定点击事件
                    $('#productTable').next('.layui-table-view').on('click', '.price-detail-trigger', function () {
                        const sku = $(this).data('sku'); // 获取行数据标识（假设数据中有唯一ID）
                        const rowData = table.cache['productTable'].find(item => item.sku === sku);
                        console.log(rowData);

                        // 弹窗展示详细数据
                        layer.open({
                            title: '价格详情 - ' + sku,
                            content: `
                            <div class="layui-text" style="padding:15px;">
                              <p>原价：¥${rowData.old_price}</p>
                              <p>当前售价：¥${rowData.price}</p><hr>
                              <p>佣金比例：${rowData.commissions}</p>
                              <hr>
                              <p>物流费用</p>
                              ${rowData.return_amount}
                              <hr>
                              <p>其他费用：待补充</p>
                            </div>
                        `,
                            area: ['400px', '600px'
                            ],
                            btn: ['关闭'
                            ]
                        });
                    });
                    // 新增勾选提示功能
                    updateSelectedCount();
                    table.on('checkbox(productTable)', updateSelectedCount);
                    $('.layui-laypage').on('click', () => setTimeout(updateSelectedCount, 50));
                    $('.layui-laypage-limits').on('change', () => setTimeout(updateSelectedCount, 50));

                }
            });
        }
        table.reload('productTable',
            {
                lineStyle: 'height: 140px;'
            });

        // 新增勾选提示功能
        function updateSelectedCount() {
            const checkStatus = table.checkStatus('productTable');
            const pageSize = $('.layui-table-page .layui-laypage-limits option:selected').val() || 20;
            const tipText = `${checkStatus.data.length}勾选/${pageSize}本页`;

            let $tip = $('.layui-table-grid-selected');
            if ($tip.length === 0) {
                $tip = $(`<div class="layui-table-grid-selected">${tipText}</div>`);
                $('.layui-table-view').append($tip);
            } else {
                $tip.text(tipText);
            }
        }

        // 仅初始化动态店铺选择框
        function initShopSelect() {
            layui.use('form', function () {
                var form = layui.form;
                $.ajax({
                    url: 'ajax.php?act=getShops',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var html = '<option value="">所有店铺</option>';
                            res.data.forEach(shop => {
                                html += `<option value="${shop.id}">${shop.storename}</option>`;
                            });
                            $('select[name="shop_id"]').html(html);
                            form.render('select'); // 必须重新渲染
                        }
                    }
                });
            });
        }




        // 初始化动态分组选择框
        function initGroupSelect() {
            layui.use('form', function () {
                var form = layui.form;
                $.ajax({
                    url: 'ajax.php?act=manage_shop_groups',
                    method: 'POST',
                    data: { op: 'get' },
                    success: function (res) {

                        if (res.code === 1 && res.data && res.data.groups) {
                            var html = '<option value="">所有分组</option>';
                            res.data.groups.forEach(group => {
                                html += `<option value="${group.id}">${group.name}</option>`;
                            });
                            $('#groupSelect').html(html);
                            form.render('select');
                        }
                    }
                });
            });
        }




        function handleSelectError($select, msg) {
            layer.msg(msg,
                {
                    icon: 2
                });
            $select.html('<option value="">加载失败（点击重试）</option>');
            form.render('select');
            $select.off('click').on('click', function () {
                if ($select.find('option:first').text().includes('失败')) {
                    initShopSelect();
                }
            });
        }
        // 初始化固定选择框
        function initStaticSelects() {
            // 商品状态（固定选项）
            form.val('searchForm',
                {
                    "status": ""
                });

            // 促销状态（固定选项）
            form.val('searchForm',
                {
                    "promotion": ""
                });
            // SKU状态（固定选项）
            form.val('searchForm',
                {
                    "sku_status": ""
                });


            form.render();
        }


        form.on('reset(searchForm)', function () {
            // 清空多选店铺并重新渲染
            $('select[name="shop_id"]').val('');
            form.render('select', 'searchForm'); // 关键修复：指定表单过滤器

            // 重置固定选项
            form.val('searchForm', {
                status: '',
                promotion: '',
                sku_status: '',
                // 重置重量筛选字段
                weight_min: '',
                weight_max: '',
                // 重置销量筛选字段
                salesvolume_min: '',
                salesvolume_max: ''
            });

            // 重新加载数据
            table.reload('productTable', {
                where: {},
                page: { curr: 1 }
            });
        });

        // 初始化页面
        $(function () {
            initShopSelect(); // 仅初始化动态店铺选择
            initGroupSelect(); // 初始化动态分组选择
            initStaticSelects(); // 初始化固定选择框
        });

        // 修复后的搜索功能
        form.on('submit(search)', function (data) {
            // 处理搜索数据
            var searchData = data.field;

            // 特殊处理多选店铺ID
            if (searchData.shop_id && Array.isArray(searchData.shop_id)) {
                searchData.shop_id = searchData.shop_id.join(',');
            }

            // 处理重量和销量筛选参数
            // 验证区间输入的合理性
            var dimensionFields = ['weight', 'salesvolume'];
            dimensionFields.forEach(function (field) {
                var minVal = parseFloat(searchData[field + '_min']);
                var maxVal = parseFloat(searchData[field + '_max']);

                // 如果最小值大于最大值，提示用户
                if (!isNaN(minVal) && !isNaN(maxVal) && minVal > maxVal) {
                    var fieldName = field === 'weight' ? '重量' : '销量';
                    layer.msg(fieldName + '的最小值不能大于最大值', { icon: 2 });
                    return false;
                }

                // 清空无效的空字符串
                if (searchData[field + '_min'] === '') {
                    delete searchData[field + '_min'];
                }
                if (searchData[field + '_max'] === '') {
                    delete searchData[field + '_max'];
                }
            });

            // 调试输出搜索条件
            console.log('搜索条件:', searchData);

            // 特别检查销量筛选参数
            if (searchData.salesvolume_min || searchData.salesvolume_max) {
                console.log('销量筛选参数:', {
                    salesvolume_min: searchData.salesvolume_min,
                    salesvolume_max: searchData.salesvolume_max
                });
            }

            table.reload('productTable', {
                where: searchData,
                page: { curr: 1 }
            });
            return false;
        });

        // 同步商品按钮事件
        $('#syncProducts').on('click', function () {
            var btn = $(this);

            // 获取所有选中的商品
            var checkStatus = table.checkStatus('productTable');
            var selectedProducts = checkStatus.data;

            // 如果没有选中任何商品，显示店铺选择弹窗
            if (selectedProducts.length === 0) {
                showShopSelectModal(btn);
                return;
            }

            // 有选中商品，确认同步选中的商品
            layer.confirm('确定要同步选中的 ' + selectedProducts.length + ' 个商品吗？', {
                icon: 3,
                title: '批量同步确认',
                btn: ['确定', '取消']
            }, function (index) {
                layer.close(index);
                performProductSync(btn, selectedProducts);
            });

            // 执行商品同步的函数
            function performProductSync(btn, products) {
                // 禁用按钮防止重复点击
                btn.addClass('layui-btn-disabled').prop('disabled', true);

                // 如果是批量同步选中商品，使用循环同步
                if (products.length > 0) {
                    batchSyncProducts(products, btn);
                }
            }

            // 批量同步商品函数
            function batchSyncProducts(products, btn) {
                var total = products.length;
                var completed = 0;
                var failed = 0;

                var loadingIndex = layer.load(2, {
                    shade: [0.3, '#fff'],
                    content: '正在同步商品 1/' + total + '...'
                });

                // 并发同步，但限制并发数量避免服务器压力过大
                var concurrency = 3; // 同时最多3个请求
                var currentIndex = 0;

                function syncNext() {
                    if (currentIndex >= total) {
                        return;
                    }

                    var product = products[currentIndex];
                    currentIndex++;

                    $.ajax({
                        url: 'ajax_products.php?act=productsync',
                        type: 'POST',
                        data: { sku: product.sku },
                        success: function (res) {
                            completed++;
                            if (res.code !== 0) {
                                failed++;
                            }
                            updateProgress();
                            syncNext(); // 继续下一个
                        },
                        error: function () {
                            completed++;
                            failed++;
                            updateProgress();
                            syncNext(); // 继续下一个
                        }
                    });
                }

                function updateProgress() {
                    // 更新进度提示
                    $('.layui-layer-content').html('正在同步商品 ' + completed + '/' + total + '...');

                    // 如果全部完成
                    if (completed >= total) {
                        layer.close(loadingIndex);
                        btn.removeClass('layui-btn-disabled').prop('disabled', false);

                        if (failed === 0) {
                            layer.msg('批量同步成功！共同步 ' + total + ' 个商品', { icon: 1 });
                        } else {
                            layer.msg('批量同步完成！成功 ' + (total - failed) + ' 个，失败 ' + failed + ' 个', { icon: 2 });
                        }

                        // 刷新表格
                        table.reload('productTable');
                    }
                }

                // 启动并发同步
                for (var i = 0; i < Math.min(concurrency, total); i++) {
                    syncNext();
                }
            }

            // 显示店铺选择弹窗
            function showShopSelectModal(btn) {
                // 获取店铺列表
                $.ajax({
                    url: 'ajax.php?act=getShops',
                    type: 'GET',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var shopList = res.data;

                            // 获取分组列表
                            $.ajax({
                                url: 'ajax.php?act=getShopGroups',
                                success: function (groupRes) {
                                    // 根据接口返回的数据结构调整
                                    var groupList = [];
                                    var isSimpleFormat = false; // 标记是否为简单格式

                                    if (groupRes.data) {
                                        if (groupRes.data.groups && Array.isArray(groupRes.data.groups)) {
                                            // 复杂格式：如果数据在groups字段中
                                            groupList = groupRes.data.groups;
                                        } else if (Array.isArray(groupRes.data)) {
                                            // 复杂格式：如果data直接是数组
                                            groupList = groupRes.data;
                                        } else if (typeof groupRes.data === 'object' && !Array.isArray(groupRes.data)) {
                                            // 简单格式：ID到名称的映射对象 {"1": "震", "2": "覃", ...}
                                            isSimpleFormat = true;
                                            groupList = [];
                                            Object.keys(groupRes.data).forEach(function (id) {
                                                groupList.push({
                                                    id: id,
                                                    name: groupRes.data[id]
                                                });
                                            });
                                        }
                                    }

                                    // 创建分组映射表
                                    var shopToGroupMap = {};
                                    var groupNameMap = {};

                                    // 遍历分组数据
                                    groupList.forEach(function (group) {
                                        groupNameMap[group.id] = group.name;

                                        // 如果是复杂格式且有shopIds数组，为每个店铺ID建立映射
                                        if (!isSimpleFormat && group.shopIds && Array.isArray(group.shopIds)) {
                                            group.shopIds.forEach(function (shopId) {
                                                shopToGroupMap[shopId.toString()] = {
                                                    id: group.id,
                                                    name: group.name
                                                };
                                            });
                                        }
                                    });

                                    // 为店铺添加分组名称
                                    shopList.forEach(function (shop, index) {
                                        var shopId = shop.id.toString();

                                        if (isSimpleFormat) {
                                            // 简单格式：从店铺数据中获取group_id，然后查找分组名称
                                            var groupId = shop.group_id || shop.groupid || shop.group_ID || shop.groupId || shop.Group_id || '';

                                            if (groupId) {
                                                groupId = groupId.toString().trim();
                                            }

                                            if (groupId && groupNameMap[groupId]) {
                                                shop.group_id = groupId;
                                                shop.group_name = groupNameMap[groupId];
                                            } else {
                                                shop.group_name = '默认分组';
                                                shop.group_id = '';
                                            }
                                        } else {
                                            // 复杂格式：使用shopToGroupMap映射
                                            if (shopToGroupMap[shopId]) {
                                                shop.group_id = shopToGroupMap[shopId].id.toString();
                                                shop.group_name = shopToGroupMap[shopId].name;
                                            } else {
                                                shop.group_name = '默认分组';
                                                shop.group_id = '';
                                            }
                                        }
                                    });

                                    var content = `
                                        <div style="padding: 20px;">
                                            <h3 style="margin: 0 0 20px 0;">选择同步店铺</h3>
                                            
                                            <form class="layui-form" lay-filter="shopSelectForm">
                                                <!-- 设置区域 -->
                                                <div style="margin-bottom: 15px;">
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">搜索店铺</label>
                                                        <div class="layui-input-block">
                                                            <input type="text" id="shopSearch" placeholder="输入店铺名称搜索..." class="layui-input">
                                                        </div>
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">分组筛选</label>
                                                        <div class="layui-input-block">
                                                            <select id="groupFilter" lay-filter="groupFilter">
                                                                <option value="">全部分组</option>
                                    `;

                                    groupList.forEach(function (group) {
                                        content += `<option value="${group.id}">${group.name}</option>`;
                                    });

                                    content += `<option value="default">默认分组</option>`;

                                    content += `
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 全选区域 -->
                                                <div class="layui-form-item">
                                                    <div class="layui-input-block">
                                                        <input type="checkbox" id="selectAllShops" title="全选店铺" lay-skin="primary">
                                                        <label for="selectAllShops" style="margin-left: 8px; cursor: pointer;">全选店铺</label>
                                                        <span style="color: #999; margin-left: 20px;">
                                                            已选择: <span id="selectedCount" style="color: #1E9FFF;">0</span> / <span id="totalCount">${shopList.length}</span>
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <!-- 店铺列表区域 -->
                                                <div style="border: 1px solid #e6e6e6; border-radius: 4px; max-height: 300px; overflow-y: auto;">
                                                    <table class="layui-table" style="margin: 0;">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 60px;">选择</th>
                                                                <th>店铺名称</th>
                                                                <th style="width: 100px;">分组</th>
                                                                <th style="width: 80px;">ID</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="shopTableBody">
                                    `;

                                    shopList.forEach(function (shop) {
                                        content += `
                                            <tr class="shop-row" data-shop-id="${shop.id}" data-group-id="${shop.group_id || ''}">
                                                <td style="text-align: center;">
                                                    <input type="checkbox" name="shop_ids" value="${shop.id}" lay-skin="primary" class="shop-checkbox">
                                                </td>
                                                <td>${shop.storename}</td>
                                                <td>${shop.group_name || '默认分组'}</td>
                                                <td>${shop.id}</td>
                                            </tr>
                                        `;
                                    });

                                    content += `
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </form>
                                        </div>
                                    `;

                                    layer.open({
                                        type: 1,
                                        title: '选择同步店铺',
                                        closeBtn: 1,
                                        area: ['800px', '600px'],
                                        content: content,
                                        btn: ['开始同步选中店铺', '同步全部店铺', '取消'],
                                        btnAlign: 'c',
                                        btn1: function (index, layero) {
                                            // 获取选中的店铺
                                            var selectedShops = [];
                                            layero.find('input[name="shop_ids"]:checked').each(function () {
                                                selectedShops.push($(this).val());
                                            });

                                            if (selectedShops.length === 0) {
                                                layer.msg('请选择至少一个店铺', { icon: 2 });
                                                return false;
                                            }

                                            layer.close(index);
                                            performSyncShops(btn, selectedShops);
                                        },
                                        btn2: function (index, layero) {
                                            // 同步全部店铺
                                            layer.close(index);
                                            performSyncShops(btn, []); // 空数组表示全部店铺
                                        },
                                        success: function (layero, index) {
                                            // 立即渲染form
                                            form.render();

                                            // 延迟执行事件绑定，确保layui渲染完成
                                            setTimeout(function () {

                                                // 更新统计数字
                                                function updateCount(skipSelectAllUpdate) {
                                                    // 统计可见的店铺行数（而不是checkbox）
                                                    var total = layero.find('.shop-row:visible').length;
                                                    var checked = layero.find('.shop-checkbox:visible:checked').length;

                                                    layero.find('#selectedCount').text(checked);
                                                    layero.find('#totalCount').text(total);

                                                    // 只有在非全选操作时才自动更新全选状态
                                                    if (!skipSelectAllUpdate) {
                                                        var $selectAll = layero.find('#selectAllShops');
                                                        if (total === 0) {
                                                            $selectAll.prop('checked', false);
                                                        } else if (checked === total) {
                                                            $selectAll.prop('checked', true);
                                                        } else {
                                                            $selectAll.prop('checked', false);
                                                        }
                                                        form.render('checkbox');
                                                    }
                                                }

                                                // 全选/取消全选
                                                layero.find('#selectAllShops').on('click', function () {
                                                    var isChecked = $(this).prop('checked');
                                                    layero.find('.shop-checkbox:visible').prop('checked', isChecked);
                                                    form.render('checkbox');
                                                    updateCount(true); // 跳过自动更新全选状态
                                                });

                                                // 单个店铺选择
                                                layero.find('.shop-checkbox').on('click', function () {
                                                    updateCount();
                                                });

                                                // 搜索功能
                                                layero.find('#shopSearch').on('input', function () {
                                                    var searchText = $(this).val().toLowerCase();
                                                    layero.find('.shop-row').each(function () {
                                                        var shopName = $(this).find('td:eq(1)').text().toLowerCase();
                                                        if (shopName.indexOf(searchText) !== -1) {
                                                            $(this).show();
                                                        } else {
                                                            $(this).hide();
                                                        }
                                                    });
                                                    updateCount();
                                                });

                                                // 分组筛选
                                                layero.find('#groupFilter').on('change', function () {
                                                    var selectedGroupId = $(this).val();
                                                    layero.find('.shop-row').each(function () {
                                                        var groupId = $(this).data('group-id').toString();
                                                        if (selectedGroupId === '') {
                                                            $(this).show();
                                                        } else if (selectedGroupId === 'default' && groupId === '') {
                                                            $(this).show();
                                                        } else if (groupId === selectedGroupId) {
                                                            $(this).show();
                                                        } else {
                                                            $(this).hide();
                                                        }
                                                    });
                                                    updateCount();
                                                });

                                                // 初始化统计
                                                updateCount();
                                            }, 100);
                                        }
                                    });
                                },
                                error: function () {
                                    layer.msg('获取分组数据失败', { icon: 2 });
                                }
                            });
                        } else {
                            layer.msg('获取店铺数据失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('获取店铺数据失败', { icon: 2 });
                    }
                });
            }

            // 执行店铺同步的函数
            function performSyncShops(btn, shopIds) {
                // 禁用按钮防止重复点击
                btn.addClass('layui-btn-disabled').prop('disabled', true);

                var loadingIndex = layer.load(2, {
                    shade: [0.3, '#fff'],
                    content: '正在同步商品数据...'
                });

                $.ajax({
                    url: 'ajax_products.php?act=productsync',
                    type: 'POST',
                    data: { shop_ids: shopIds },
                    dataType: 'json',
                    success: function (res) {
                        layer.close(loadingIndex);
                        btn.removeClass('layui-btn-disabled').prop('disabled', false);
                        
                        if (res.code === 0) {
                            if (shopIds.length === 0) {
                                layer.msg('全部店铺商品同步任务已提交，请稍等服务器执行任务约3~10分钟', { icon: 1 });
                            } else {
                                layer.msg('选中店铺商品同步任务已提交，请稍等服务器执行任务约3~10分钟', { icon: 1 });
                            }
                            table.reload('productTable');
                        } else {
                            layer.msg(res.msg || '同步失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.close(loadingIndex);
                        btn.removeClass('layui-btn-disabled').prop('disabled', false);
                        layer.msg('请求失败，请稍后重试', { icon: 2 });
                    }
                });
            }
        });

        $('#batchOperation').on('click', function () {
            var checkStatus = table.checkStatus('productTable');
            if (checkStatus.data.length === 0) {
                layer.msg('请至少选择一条数据', { icon: 2 });
                return;
            }

            // Extract unique stores from selected products
            var selectedStores = {};
            checkStatus.data.forEach(function (item) {
                if (item.storeid && !selectedStores[item.storeid]) {
                    selectedStores[item.storeid] = {
                        storename: item.storename,
                        warehouses: []
                    };
                }
            });

            // Load warehouses for each store asynchronously and render modal content
            function loadWarehousesForStores(stores, callback) {
                var storeIds = Object.keys(stores);
                var loadedCount = 0;

                storeIds.forEach(function (storeId) {
                    $.ajax({
                        url: 'ajax_products.php?act=get_warehouses',
                        type: 'POST',
                        dataType: 'json',
                        data: { id: storeId },
                        success: function (res) {
                            if (res.code === 0 && res.data) {
                                stores[storeId].warehouses = res.data;
                            }
                        },
                        complete: function () {
                            loadedCount++;
                            if (loadedCount === storeIds.length) {
                                callback();
                            }
                        },
                        error: function () {
                            layer.msg('仓库数据加载失败', { icon: 2 });
                            loadedCount++;
                            if (loadedCount === storeIds.length) {
                                callback();
                            }
                        }
                    });
                });
            }

            function renderBatchStockContent(stores) {
                var contentHtml = '<style>' +
                    '.batch-modal { font-family: Arial, sans-serif; color: #333; }' +
                    '.batch-modal .layui-form-item { margin-bottom: 15px; }' +
                    '.batch-modal label.layui-form-label { color: #1E90FF; font-weight: 600; }' +
                    '.batch-modal select, .batch-modal input { border: 1px solid #1E90FF; border-radius: 4px; padding: 6px 10px; }' +
                    '.batch-modal select:focus, .batch-modal input:focus { outline: none; border-color: #104E8B; box-shadow: 0 0 5px #1E90FF; }' +
                    '.batch-modal .store-section { background-color: #F0F8FF; padding: 10px; border-radius: 6px; margin-bottom: 12px; display: flex; align-items: center; }' +
                    '.batch-modal .store-section label.layui-form-label { width: 120px; margin-bottom: 0; }' +
                    '.batch-modal .store-section .layui-input-inline { margin-right: 10px; }' +
                    '</style>';
                contentHtml += '<div class="batch-modal" style="padding:20px;">';
                contentHtml += '<div class="layui-form-item">';
                contentHtml += '<label class="layui-form-label">操作类型</label>';
                contentHtml += '<div class="layui-input-block">';
                contentHtml += '<select name="batchType" lay-filter="batchTypeSelect" lay-verify="required">';
                contentHtml += '<option value="">请选择操作类型</option>';
                contentHtml += '<option value="price">修改价格</option>';
                contentHtml += '<option value="stock" selected>批量修改库存</option>';
                contentHtml += '<option value="status">修改状态</option>';
                contentHtml += '<option value="sync">同步商品</option>';
                contentHtml += '<option value="ccsp">重传商品</option>';
                contentHtml += '</select>';
                contentHtml += '</div></div>';

                // For each store, render store name, warehouse dropdown, and stock input
                for (var storeId in stores) {
                    var store = stores[storeId];
                    contentHtml += '<div class="layui-form-item store-section" data-storeid="' + storeId + '">';
                    contentHtml += '<label class="layui-form-label">' + store.storename + '</label>';
                    contentHtml += '<div class="layui-input-inline">';
                    contentHtml += '<select name="warehouse_' + storeId + '" lay-verify="required" lay-search>';
                    contentHtml += '<option value="">请选择仓库</option>';
                    store.warehouses.forEach(function (warehouse) {
                        contentHtml += '<option value="' + warehouse.id + '">' + warehouse.name + '</option>';
                    });
                    contentHtml += '</select>';
                    contentHtml += '</div>';
                    contentHtml += '<div class="layui-input-inline">';
                    contentHtml += '<input type="text" name="stock_' + storeId + '" placeholder="请输入库存数量" class="layui-input" lay-verify="required|number">';
                    contentHtml += '</div>';
                    contentHtml += '</div>';
                }

                contentHtml += '</div>';
                return contentHtml;
            }

            loadWarehousesForStores(selectedStores, function () {
                layer.open({
                    title: '批量操作',
                    content: renderBatchStockContent(selectedStores) +
                        '<div class="layui-form-item" id="priceTypeContainer" style="display:none;margin-top:15px;">'
                        + '<label class="layui-form-label">价格类型</label>'
                        + '<div class="layui-input-block">'
                        + '<select name="priceType" lay-filter="priceTypeSelect">'
                        + '<option value="adjust">调整价格</option>'
                        + '<option value="direct">直接设置</option>'
                        + '</select>'
                        + '</div>'
                        + '</div>'
                        + '<div class="layui-form-item" id="priceMultiplierContainer" style="display:none;margin-top:15px;">'
                        + '<label class="layui-form-label">倍数</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="number" step="0.01" min="0" name="priceMultiplier" placeholder="请输入倍数，如1.2" class="layui-input" autocomplete="off">'
                        + '</div>'
                        + '</div>'
                        + '<div class="layui-form-item" id="priceAddContainer" style="display:none;margin-top:15px;">'
                        + '<label class="layui-form-label">加价(元)</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="number" step="0.01" name="priceAdd" placeholder="请输入加价金额" class="layui-input" autocomplete="off">'
                        + '</div>'
                        + '</div>'
                        + '<div class="layui-form-item" id="priceSubtractContainer" style="display:none;margin-top:15px;">'
                        + '<label class="layui-form-label">减价(元)</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="number" step="0.01" name="priceSubtract" placeholder="请输入减价金额" class="layui-input" autocomplete="off">'
                        + '</div>'
                        + '</div>'
                        + '<div class="layui-form-item" id="directOldPriceContainer" style="display:none;margin-top:15px;">'
                        + '<label class="layui-form-label">折前价格</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="number" step="0.01" min="0" name="directOldPrice" placeholder="请输入折前价格" class="layui-input" autocomplete="off">'
                        + '</div>'
                        + '</div>'
                        + '<div class="layui-form-item" id="directPriceContainer" style="display:none;margin-top:15px;">'
                        + '<label class="layui-form-label">折后价格</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="number" step="0.01" min="0" name="directPrice" placeholder="请输入折后价格" class="layui-input" autocomplete="off">'
                        + '</div>'
                        + '</div>',
                    btn: ['确定', '取消'],
                    area: ['600px', 'auto'],
                    success: function (layero, index) {
                        form.render();

                        // Bind batchType change event to toggle visibility of store sections and price inputs
                        $(layero).find('select[name=batchType]').off('change').on('change', function () {
                            var value = $(this).val();
                            if (value === 'stock') {
                                $(layero).find('.store-section').show();
                                $(layero).find('#priceTypeContainer').hide();
                                $(layero).find('#priceMultiplierContainer').hide();
                                $(layero).find('#priceAddContainer').hide();
                                $(layero).find('#priceSubtractContainer').hide();
                                $(layero).find('#directOldPriceContainer').hide();
                                $(layero).find('#directPriceContainer').hide();
                                $(layero).find('[name=batchValue]').parent().parent().show();
                            } else if (value === 'price') {
                                $(layero).find('.store-section').hide();
                                $(layero).find('#priceTypeContainer').show();
                                // 默认显示调整价格选项
                                $(layero).find('#priceMultiplierContainer').show();
                                $(layero).find('#priceAddContainer').show();
                                $(layero).find('#priceSubtractContainer').show();
                                $(layero).find('#directOldPriceContainer').hide();
                                $(layero).find('#directPriceContainer').hide();
                                $(layero).find('[name=batchValue]').parent().parent().hide();
                            } else {
                                $(layero).find('.store-section').hide();
                                $(layero).find('#priceTypeContainer').hide();
                                $(layero).find('#priceMultiplierContainer').hide();
                                $(layero).find('#priceAddContainer').hide();
                                $(layero).find('#priceSubtractContainer').hide();
                                $(layero).find('#directOldPriceContainer').hide();
                                $(layero).find('#directPriceContainer').hide();
                                $(layero).find('[name=batchValue]').parent().parent().show();
                            }
                        });

                        // Bind priceType change event to toggle between adjust and direct price setting
                        $(layero).find('select[name=priceType]').off('change').on('change', function () {
                            var value = $(this).val();
                            if (value === 'adjust') {
                                $(layero).find('#priceMultiplierContainer').show();
                                $(layero).find('#priceAddContainer').show();
                                $(layero).find('#priceSubtractContainer').show();
                                $(layero).find('#directOldPriceContainer').hide();
                                $(layero).find('#directPriceContainer').hide();
                            } else if (value === 'direct') {
                                $(layero).find('#priceMultiplierContainer').hide();
                                $(layero).find('#priceAddContainer').hide();
                                $(layero).find('#priceSubtractContainer').hide();
                                $(layero).find('#directOldPriceContainer').show();
                                $(layero).find('#directPriceContainer').show();
                            }
                        });
                    },
                    yes: function (index, layero) {
                        var $content = $(layero).find('.layui-layer-content');
                        var batchType = $content.find('[name=batchType]').val();

                        if (!batchType) {
                            layer.msg('请选择操作类型', { icon: 2 });
                            return false;
                        }

                        if (batchType === 'stock') {
                            var requests = [];
                            var valid = true;

                            $('.store-section').each(function () {
                                var storeId = $(this).data('storeid');
                                var warehouseId = $content.find('select[name="warehouse_' + storeId + '"]').val();
                                var stockValue = $content.find('input[name="stock_' + storeId + '"]').val();

                                if (!warehouseId) {
                                    layer.msg('请为店铺 ' + selectedStores[storeId].storename + ' 选择仓库', { icon: 2 });
                                    valid = false;
                                    return false; // break each
                                }
                                if (!stockValue || !/^\d+$/.test(stockValue)) {
                                    layer.msg('请为店铺 ' + selectedStores[storeId].storename + ' 输入有效的库存数量', { icon: 2 });
                                    valid = false;
                                    return false; // break each
                                }

                                // Collect offer_ids for this store
                                var offerIds = checkStatus.data.filter(function (item) {
                                    return item.storeid == storeId;
                                }).map(function (item) {
                                    return item.offer_id || item.id;
                                });

                                requests.push({
                                    storeId: storeId,
                                    warehouseId: warehouseId,
                                    stockValue: stockValue,
                                    offerIds: offerIds
                                });
                            });

                            if (!valid) {
                                return false;
                            }

                            layer.load(2);
                            var successCount = 0;
                            var failCount = 0;
                            var totalRequests = requests.length;
                            var completedRequests = 0;

                            function sendRequest(i) {
                                if (i >= totalRequests) {
                                    layer.closeAll('loading');
                                    layer.msg('批量修改库存完成，成功：' + successCount + '，失败：' + failCount, { icon: 1 });
                                    table.reload('productTable');
                                    layer.close(index);
                                    return;
                                }

                                var req = requests[i];
                                // 修复：每个offer_id单独发送请求，而不是一次性发送所有offer_id
                                var currentOfferIds = req.offerIds;
                                var processedCount = 0;

                                // 改进：批量处理offer_ids，每批最多处理10个
                                var BATCH_SIZE = 10; // 每批处理的商品数量

                                function processBatchOfferIds(startIndex) {
                                    if (startIndex >= currentOfferIds.length) {
                                        // 所有offer_id处理完成，继续下一个请求
                                        sendRequest(i + 1);
                                        return;
                                    }

                                    // 计算当前批次的结束索引
                                    var endIndex = Math.min(startIndex + BATCH_SIZE, currentOfferIds.length);
                                    var currentBatch = currentOfferIds.slice(startIndex, endIndex);

                                    // 显示进度信息
                                    var progressPercent = Math.round((startIndex / currentOfferIds.length) * 100);
                                    layer.msg('正在处理: ' + progressPercent + '% (' + startIndex + '/' + currentOfferIds.length + ')', {
                                        icon: 16,
                                        time: 0, // 不自动关闭
                                        shade: 0.1
                                    });

                                    // 记录请求参数，便于调试
                                    var requestData = {
                                        offer_ids: currentBatch.join(','),
                                        stock: req.stockValue,
                                        warehouse_id: req.warehouseId
                                    };
                                    console.log('Batch update request:', requestData);

                                    $.ajax({
                                        url: 'ajax_products.php?act=batch_update_stock',
                                        type: 'POST',
                                        dataType: 'json',
                                        data: requestData,
                                        timeout: 30000, // 增加超时时间到30秒
                                        success: function (res) {
                                            console.log('Batch update response:', res);

                                            if (res.code === 0 || res.success_count > 0) {
                                                // 成功处理
                                                successCount += res.success_count || currentBatch.length;
                                                failCount += res.fail_count || 0;

                                                // 更新进度信息
                                                var totalProcessed = successCount + failCount;
                                                var progressMsg = '处理进度: ' + Math.round((totalProcessed / currentOfferIds.length) * 100) + '%，成功: ' + successCount + '，失败: ' + failCount;
                                                layer.msg(progressMsg, { icon: 16, time: 1000 });
                                            } else {
                                                // 失败处理
                                                failCount += currentBatch.length;
                                                console.error('Failed to update stock for batch:', currentBatch, 'Error:', res.msg);
                                                layer.msg('批次更新失败: ' + (res.msg || '未知错误'), { icon: 2, time: 1000 });
                                            }
                                        },
                                        error: function (xhr, status, error) {
                                            failCount += currentBatch.length;
                                            console.error('AJAX error for batch:', currentBatch, 'Status:', status, 'Error:', error);

                                            // 尝试解析错误响应
                                            var errorMsg = error;
                                            try {
                                                if (xhr.responseText) {
                                                    var errorResponse = JSON.parse(xhr.responseText);
                                                    if (errorResponse.msg) {
                                                        errorMsg = errorResponse.msg;
                                                    }
                                                }
                                            } catch (e) {
                                                console.error('Error parsing error response:', e);
                                            }

                                            layer.msg('请求失败: ' + errorMsg, { icon: 2, time: 1000 });
                                        },
                                        complete: function () {
                                            // 处理下一批offer_ids
                                            processBatchOfferIds(endIndex);
                                        }
                                    });
                                }

                                // 开始批量处理
                                processBatchOfferIds(0);
                            }
                            sendRequest(0);
                        } else if (batchType === 'price') {
                            var priceType = $content.find('[name=priceType]').val();
                            
                            if (priceType === 'adjust') {
                                // 调整价格逻辑
                                var multiplier = parseFloat($content.find('[name=priceMultiplier]').val());
                                var addPrice = parseFloat($content.find('[name=priceAdd]').val());
                                var subtractPrice = parseFloat($content.find('[name=priceSubtract]').val());

                                if (isNaN(multiplier) || multiplier < 0) {
                                    layer.msg('请输入有效的倍数，且不能小于0', { icon: 2 });
                                    return false;
                                }
                                if (isNaN(addPrice)) {
                                    addPrice = 0;
                                }
                                if (isNaN(subtractPrice)) {
                                    subtractPrice = 0;
                                }

                                var skus = checkStatus.data.map(function (item) { return item.sku; }).join(',');

                                layer.load(2);
                                $.ajax({
                                    url: 'ajax_products.php?act=batch_update_price',
                                    type: 'POST',
                                    dataType: 'json',
                                    data: {
                                        skus: skus,
                                        multiplier: multiplier,
                                        add_price: addPrice,
                                        subtract_price: subtractPrice
                                    },
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 0) {
                                            layer.msg('价格批量更新成功', { icon: 1 });
                                            table.reload('productTable');
                                            layer.close(index);
                                        } else {
                                            layer.msg(res.msg || '价格批量更新失败', { icon: 2 });
                                        }
                                    },
                                    error: function () {
                                        layer.closeAll('loading');
                                        layer.msg('请求失败，请检查网络', { icon: 2 });
                                    }
                                });
                            } else if (priceType === 'direct') {
                                // 直接设置价格逻辑
                                var directOldPrice = parseFloat($content.find('[name=directOldPrice]').val());
                                var directPrice = parseFloat($content.find('[name=directPrice]').val());
                                
                                if (isNaN(directOldPrice) || directOldPrice < 0) {
                                    layer.msg('请输入有效的折前价格', { icon: 2 });
                                    return false;
                                }
                                if (isNaN(directPrice) || directPrice < 0) {
                                    layer.msg('请输入有效的折后价格', { icon: 2 });
                                    return false;
                                }

                                var skus = checkStatus.data.map(function (item) { return item.sku; }).join(',');

                                layer.load(2);
                                $.ajax({
                                    url: 'ajax_products.php?act=batch_update_direct_price',
                                    type: 'POST',
                                    dataType: 'json',
                                    data: {
                                        skus: skus,
                                        old_price: directOldPrice.toFixed(2),
                                        price: directPrice.toFixed(2)
                                    },
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 0) {
                                            layer.msg('价格批量设置成功', { icon: 1 });
                                            table.reload('productTable');
                                            layer.close(index);
                                        } else {
                                            layer.msg(res.msg || '价格批量设置失败', { icon: 2 });
                                        }
                                    },
                                    error: function () {
                                        layer.closeAll('loading');
                                        layer.msg('请求失败，请检查网络', { icon: 2 });
                                    }
                                });
                            }
                        } else if (batchType === 'sync') {
                            // 批量同步商品，使用类似单个同步的逻辑
                            layer.close(index);
                            batchSyncProducts(checkStatus.data, null);
                        } else {
                            var batchValue = $content.find('[name=batchValue]').val();
                            if (!batchValue) {
                                layer.msg('请填写操作值', { icon: 2 });
                                return false;
                            }

                            layer.load(2);
                            $.ajax({
                                url: 'ajax_products.php?act=batch_operation', // 确保后端接口正确
                                type: 'POST',
                                data: {
                                    ids: checkStatus.data.map(function (item) { return item.offer_id || item.id; }).join(','),
                                    type: batchType,
                                    value: batchValue
                                },
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 0) {
                                        layer.msg('操作成功', { icon: 1 });
                                        table.reload('productTable');
                                    } else {
                                        layer.msg(res.msg || '操作失败', { icon: 2 });
                                    }
                                },
                                error: function () {
                                    layer.closeAll('loading');
                                    layer.msg('请求失败，请检查网络', { icon: 2 });
                                }
                            });
                        }
                    }
                });
            });
        });

        // 批量重传
        $('#Batchresend').on('click', function () {
            var checkStatus = table.checkStatus('productTable');
            if (checkStatus.data.length === 0) {
                layer.msg('请至少选择一条数据', { icon: 2 });
                return;
            }

            var skus = checkStatus.data.map(function (item) {
                return item.sku;
            });

            layer.load(2);
            $.ajax({
                url: 'ajax_products.php?act=Management_reupload',
                type: 'POST',
                dataType: 'json',
                data: { skus: skus },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, { icon: 1 });
                        table.reload('productTable');
                    } else {
                        layer.msg(res.msg || '操作失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请稍后重试', { icon: 2 });
                }
            });
            layer.close(index);
        });

        // 批量归档
        $('#batchArchive').on('click', function() {
            var checkStatus = table.checkStatus('productTable');
            if (checkStatus.data.length === 0) {
                layer.msg('请至少选择一条数据', {icon: 2});
                return;
            }
        
            // 提取有效的产品ID
            var productIds = checkStatus.data.map(function(item) {
                // 优先使用product_id，如果没有则使用sku
                if (item.product_id && item.product_id !== '' && item.product_id !== null) {
                    return item.product_id;
                } else if (item.sku && item.sku !== '' && item.sku !== null) {
                    return item.sku;
                } else {
                    return null;
                }
            }).filter(function(id) {
                return id && id !== '' && id !== null;
            });
        
            if (productIds.length === 0) {
                layer.msg('选中的商品中没有有效的产品ID', {icon: 2});
                return;
            }
        
            layer.confirm('确定要归档选中的 ' + productIds.length + ' 个商品吗？<br><span style="color:red;">归档后商品将从正常销售页面移除</span>', {
                icon: 3,
                title: '批量归档确认',
                area: ['400px', '200px']
            }, function(index) {
                layer.close(index);
                
                // 分批处理参数
                var BATCH_SIZE = 5; // 每批处理的商品数量
                var totalCount = productIds.length;
                var processedCount = 0;
                var successCount = 0;
                var failCount = 0;
                
                // 显示进度层
                var progressLayer = layer.open({
                    type: 0,
                    title: '批量归档进度',
                    content: '准备开始处理...',
                    area: ['400px', '150px'],
                    btn: []
                });
                
                // 处理单个批次
                function processBatch(startIndex) {
                    if (startIndex >= totalCount) {
                        // 所有批次处理完成
                        layer.close(progressLayer);
                        layer.msg('批量归档完成，成功：' + successCount + '，失败：' + failCount, {icon: 1});
                        table.reload('productTable');
                        return;
                    }
                    
                    // 计算当前批次的结束索引
                    var endIndex = Math.min(startIndex + BATCH_SIZE, totalCount);
                    var currentBatch = productIds.slice(startIndex, endIndex);
                    
                    // 更新进度信息
                    var progressPercent = Math.round((startIndex / totalCount) * 100);
                    layer.msg('正在处理: ' + progressPercent + '% (' + startIndex + '/' + totalCount + ')', {
                        icon: 16,
                        time: 0, // 不自动关闭
                        shade: 0.1
                    });
                    
                    // 发送AJAX请求
                    $.ajax({
                        url: 'ajax_products.php?act=batch_archive_products',
                        type: 'POST',
                        dataType: 'json',
                        data: {
                            product_ids: currentBatch.join(',')
                        },
                        success: function(res) {
                            if (res.code === 0) {
                                // 成功处理
                                successCount += res.success_count || currentBatch.length;
                                failCount += res.fail_count || 0;
                            } else {
                                // 失败处理
                                failCount += currentBatch.length;
                            }
                            
                            // 更新进度信息
                            processedCount = startIndex + currentBatch.length;
                            var newProgress = Math.round((processedCount / totalCount) * 100);
                            layer.msg('处理进度: ' + newProgress + '% (' + processedCount + '/' + totalCount + ')，成功: ' + successCount + '，失败: ' + failCount, {
                                icon: 16,
                                time: 1000
                            });
                            
                            // 处理下一批
                            processBatch(endIndex);
                        },
                        error: function() {
                            // 当前批次全部失败
                            failCount += currentBatch.length;
                            processedCount = startIndex + currentBatch.length;
                            var newProgress = Math.round((processedCount / totalCount) * 100);
                            layer.msg('处理进度: ' + newProgress + '% (' + processedCount + '/' + totalCount + ')，成功: ' + successCount + '，失败: ' + failCount, {
                                icon: 16,
                                time: 1000
                            });
                            
                            // 继续处理下一批
                            processBatch(endIndex);
                        }
                    });
                }
                
                // 开始处理第一批
                processBatch(0);
            });
        });
        
        // 表格行工具事件
        table.on('tool(productTable)', function (obj) {
            var data = obj.data;
            var event = obj.event;

            if (event === 'setSource') {
                layer.open({
                    title: '设置货源链接 - ' + data.sku,
                    type: 1,
                    area: ['800px', '500px'
                    ],
                    content: 'ajax_products.php?act=productsstocks',
                    data: { storeid: data.storeid, offer_id: data.offer_id },
                    success: function (layero, index) {
                        // iframe加载完成后操作
                    }
                });
            } else if (event === 'editPrice') {
                // 创建价格编辑模态框
                var priceModalContent = '<form class="layui-form" id="editPriceForm" style="margin: 20px;">'
                    + '<div class="layui-form-item">'
                    + '<label class="layui-form-label">折前价格</label>'
                    + '<div class="layui-input-block">'
                    + '<input type="text" name="old_price" required lay-verify="required|number" placeholder="请输入折前价格" autocomplete="off" class="layui-input" value="' + (data.old_price || '') + '">'
                    + '</div>'
                    + '</div>'
                    + '<div class="layui-form-item">'
                    + '<label class="layui-form-label">折后价格</label>'
                    + '<div class="layui-input-block">'
                    + '<input type="text" name="price" required lay-verify="required|number" placeholder="请输入折后价格" autocomplete="off" class="layui-input" value="' + (data.price || '') + '">'
                    + '</div>'
                    + '</div>'
                    + '</form>';

                layer.open({
                    type: 1,
                    title: '修改价格 - ' + data.sku,
                    area: ['400px', '280px'],
                    content: priceModalContent,
                    btn: ['确定', '取消'],
                    success: function (layero, index) {
                        var form = layui.form;
                        form.render();
                    },
                    yes: function (index, layero) {
                        var oldPriceInput = layero.find('input[name=old_price]').val();
                        var priceInput = layero.find('input[name=price]').val();

                        if (!oldPriceInput || isNaN(oldPriceInput)) {
                            layer.msg('请输入有效的折前价格', { icon: 2 });
                            return false;
                        }
                        if (!priceInput || isNaN(priceInput)) {
                            layer.msg('请输入有效的折后价格', { icon: 2 });
                            return false;
                        }

                        // 显示加载中提示
                        layer.load(2);

                        $.ajax({
                            url: 'ajax_products.php?act=update_price',
                            type: 'POST',
                            dataType: 'json',
                            data: { 
                                offer_id: data.offer_id, 
                                old_price: oldPriceInput,
                                price: priceInput 
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 0) {
                                    layer.msg('修改成功', { icon: 1 });
                                    obj.update({ 
                                        old_price: parseFloat(oldPriceInput),
                                        price: parseFloat(priceInput) 
                                    });
                                    layer.close(index);
                                } else {
                                    layer.msg(res.msg || '修改失败', { icon: 2 });
                                }
                            },
                            error: function () {
                                layer.closeAll('loading');
                                layer.msg('请求失败，请稍后重试', { icon: 2 });
                            }
                        });
                    },
                    btn2: function (index, layero) {
                        layer.close(index);
                    }
                });
            } else if (event === 'editStock') {
                // Custom modal for editing stock with warehouse selection
                var stockValue = data.stock || '';
                var selectedWarehouseId = data.storeid || '';
                var modalContent = '<form class="layui-form" id="editStockForm" style="margin: 20px;">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">库存数量</label>' +
                    '<div class="layui-input-block">' +
                    '<input type="text" name="stock" required lay-verify="required|number" placeholder="请输入库存数量" autocomplete="off" class="layui-input" value="' + stockValue + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">选择仓库</label>' +
                    '<div class="layui-input-block">' +
                    '<select name="warehouse" id="warehouseSelect" lay-verify="required">' +
                    '<option value="">请选择仓库</option>' +
                    '</select>' +
                    '</div>' +
                    '</div>' +
                    '</form>';

                layer.open({
                    type: 1,
                    title: '修改库存 - ' + data.sku,
                    area: ['400px', '250px'],
                    content: modalContent,
                    btn: ['确定', '取消'],
                    success: function (layero, index) {
                        var form = layui.form;
                        // Load warehouses dynamically
                        function loadStores() {
                            layer.load(2);
                            console.log('Loading warehouses for store ID:', data.storeid);
                            $.ajax({
                                url: 'ajax_products.php?act=get_warehouses',
                                type: 'POST',
                                data: { id: data.storeid }, // 修正参数名为id而不是storeid
                                dataType: 'json',
                                success: function (res) {
                                    layer.closeAll('loading');
                                    console.log('Warehouse response:', res);
                                    if (res && res.code === 0 && res.data && Array.isArray(res.data)) {
                                        var options = '<option value="">请选择仓库</option>';
                                        res.data.forEach(function (warehouse) {
                                            var selected = (warehouse.id == selectedWarehouseId) ? 'selected' : '';
                                            options += '<option value="' + warehouse.id + '" ' + selected + '>' + warehouse.name + '</option>';
                                        });
                                        $('#warehouseSelect').html(options);
                                        form.render('select');
                                    } else {
                                        console.error('Invalid warehouse data:', res);
                                        layer.msg('仓库数据加载失败: ' + (res.msg || '未知错误'), { icon: 2 });
                                    }
                                },
                                error: function (xhr, status, error) {
                                    layer.closeAll('loading');
                                    console.error('AJAX error:', status, error);
                                    layer.msg('仓库数据请求失败: ' + error, { icon: 2 });
                                }
                            });
                        }
                        loadStores();
                        form.render();
                    },
                    yes: function (index, layero) {
                        var form = layui.form;
                        form.on('submit(editStockForm)', function (dataForm) {
                            return false; // prevent default submit
                        });
                        var stockInput = layero.find('input[name=stock]').val();
                        var warehouseSelect = layero.find('select[name=warehouse]').val();

                        if (!stockInput || !/^\d+$/.test(stockInput)) {
                            layer.msg('请输入有效的库存数量', { icon: 2 });
                            return false;
                        }
                        if (!warehouseSelect) {
                            layer.msg('请选择仓库', { icon: 2 });
                            return false;
                        }

                        console.log('Submitting stock update:', {
                            storeid: warehouseSelect,
                            offer_id: data.offer_id,
                            stock: stockInput
                        });

                        // 显示加载中提示
                        layer.load(2);

                        $.ajax({
                            url: 'ajax_products.php?act=update_stock',
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                storeid: warehouseSelect, // 仓库ID
                                offer_id: data.offer_id,  // 商品ID
                                stock: stockInput         // 库存数量
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 0) {
                                    layer.msg('修改成功', { icon: 1 });
                                    obj.update({ stock: parseInt(stockInput) });
                                    layer.close(index);
                                } else {
                                    layer.msg(res.msg || '修改失败', { icon: 2 });
                                }
                            },
                            error: function (xhr, status, error) {
                                layer.closeAll('loading');
                                layer.msg('请求失败，请稍后重试', { icon: 2 });
                            }
                        });
                    },
                    btn2: function (index, layero) {
                        layer.close(index);
                    }
                });
            } else if (event === 'editProduct') {
                // 使用独立的商品编辑页面 - 完整功能
                layer.open({
                    type: 2,
                    title: '编辑商品 - ' + data.sku,
                    shadeClose: true,
                    maxmin: true, //开启最大化最小化按钮
                    area: ['1400px', '900px'],
                    content: './product_edit_modal.php?offer_id=' + data.offer_id + '&storeid=' + data.storeid
                });
            } else if (event === 'reupload') {
                layer.confirm('确定要一键重传该商品吗？', { icon: 3, title: '提示' }, function (index) {
                    layer.load(2);
                    $.ajax({
                        url: 'ajax_products.php?act=Management_reupload',
                        type: 'POST',
                        dataType: 'json',
                        data: { sku: data.sku, storeid: data.storeid },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.msg('操作成功', { icon: 1 });
                                table.reload('productTable');
                            } else {
                                layer.msg(res.msg || '操作失败', { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.closeAll('loading');
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            } else if (event === 'sync') {
                layer.confirm('确定要同步该商品数据吗？', { icon: 3, title: '提示' }, function (index) {
                    layer.load(2);
                    $.ajax({
                        url: 'ajax_products.php?act=productsync',
                        type: 'POST',
                        dataType: 'json',
                        data: {
                            offer_id: data.offer_id, storeid: data.storeid
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.msg('同步成功', { icon: 1 });
                                table.reload('productTable');
                            } else {
                                layer.msg(res.msg || '同步失败', { icon: 2 });
                            }
                        }, error: function () {
                            layer.closeAll('loading');
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            }
        });
    });
</script>